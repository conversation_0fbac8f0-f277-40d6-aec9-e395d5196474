package com.boyo.web.controller.eam;

import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.eam.domain.EquipLedgerProperty;
import com.boyo.eam.service.IEquipLedgerPropertyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 设备台账属性表(EquipLedgerProperty)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
@Api("设备台账属性表")
@RestController
@RequestMapping("/equip/equipLedgerProperty")
@AllArgsConstructor
public class EquipLedgerPropertyController extends BaseController{
    /**
     * 服务对象
     */
    private final IEquipLedgerPropertyService equipLedgerPropertyService;

    /**
     * 查询设备台账属性表列表
     *
     */
    @ApiOperation("查询设备台账属性表列表")
    @GetMapping("/list")
    public TableDataInfo list(EquipLedgerProperty equipLedgerProperty) {
        startPage();
        List<EquipLedgerProperty> list = equipLedgerPropertyService.selectEquipLedgerPropertyList(equipLedgerProperty);
        return getDataTable(list);
    }
    
    /**
     * 获取设备台账属性表详情
     */
    @ApiOperation("获取设备台账属性表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(equipLedgerPropertyService.getById(id));
    }

    /**
     * 新增设备台账属性表
     */
    @ApiOperation("新增设备台账属性表")
    @PostMapping
    public AjaxResult add(@RequestBody EquipLedgerProperty equipLedgerProperty) {
        equipLedgerProperty.setOpenid(super.generateOpenid());
        return toBooleanAjax(equipLedgerPropertyService.save(equipLedgerProperty));
    }

    /**
     * 修改设备台账属性表
     */
    @ApiOperation("修改设备台账属性表")
    @PutMapping
    public AjaxResult edit(@RequestBody EquipLedgerProperty equipLedgerProperty) {
        return toBooleanAjax(equipLedgerPropertyService.updateById(equipLedgerProperty));
    }

    /**
     * 删除设备台账属性表
     */
    @ApiOperation("删除设备台账属性表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(equipLedgerPropertyService.removeByIds(Arrays.asList(ids)));
    }

}
