package com.boyo.web.controller.iot;

import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.iot.domain.IotEquipmentProp;
import com.boyo.iot.service.IIotEquipmentPropService;
import com.boyo.iot.vo.IoTAttrVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 设备属性管理Controller
 *
 * <AUTHOR>
 */
@Api("设备属性管理")
@RestController
@RequestMapping("/iot/property")
@AllArgsConstructor
public class IotEquipmentPropController extends BaseController {
    private final IIotEquipmentPropService iotEquipmentPropService;

    /**
     * 查询设备属性管理列表
     */
    @ApiOperation("查询设备属性管理列表")
    @GetMapping("/list")
    public TableDataInfo list(IotEquipmentProp iotEquipmentProp) {
        startPage();
        List<IoTAttrVO> list = iotEquipmentPropService.selectIotEquipmentPropList(iotEquipmentProp);
        return getDataTable(list);
    }

    /**
     * 获取设备属性管理详细信息
     */
    @ApiOperation("获取设备属性管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(iotEquipmentPropService.getById(id));
    }

    /**
     * 新增设备属性管理
     */
    @ApiOperation("新增设备属性管理")
    @PostMapping
    public AjaxResult add(@RequestBody List<IotEquipmentProp> iotEquipmentProps) {
        return toBooleanAjax(iotEquipmentPropService.saveOrUpdateBatch(iotEquipmentProps));
    }

    /**
     * 修改设备属性管理
     */
    @ApiOperation("修改设备属性管理")
    @PutMapping
    public AjaxResult edit(@RequestBody IotEquipmentProp iotEquipmentProp) {
        return toBooleanAjax(iotEquipmentPropService.updateById(iotEquipmentProp));
    }

    /**
     * 删除设备属性管理
     */
    @ApiOperation("删除设备属性管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(iotEquipmentPropService.removeByIds(Arrays.asList(ids)));
    }
}
