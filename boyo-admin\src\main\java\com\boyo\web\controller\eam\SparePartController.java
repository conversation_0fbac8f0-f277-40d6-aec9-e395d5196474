package com.boyo.web.controller.eam;

import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.eam.domain.SparePart;
import com.boyo.eam.service.ISparePartService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 备件总账表(SparePart)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-04 11:12:31
 */
@Api("备件总账表")
@RestController
@RequestMapping("/equip/sparePart")
@AllArgsConstructor
public class SparePartController extends BaseController{
    /**
     * 服务对象
     */
    private final ISparePartService sparePartService;

    /**
     * 查询备件总账表列表
     *
     */
    @ApiOperation("查询备件总账表列表")
    @GetMapping("/list")
    public TableDataInfo list(SparePart sparePart) {
        startPage();
        List<SparePart> list = sparePartService.selectSparePartList(sparePart);
        return getDataTable(list);
    }
    
    /**
     * 获取备件总账表详情
     */
    @ApiOperation("获取备件总账表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(sparePartService.getById(id));
    }

    /**
     * 新增备件总账表
     */
    @ApiOperation("新增备件总账表")
    @PostMapping
    public AjaxResult add(@RequestBody SparePart sparePart) {
        sparePart.setOpenid(super.generateOpenid());
        sparePart.setCreateBy(SecurityUtils.getUsername());
        sparePart.setCreateTime(new Date());
        return toBooleanAjax(sparePartService.save(sparePart));
    }

    /**
     * 修改备件总账表
     */
    @ApiOperation("修改备件总账表")
    @PutMapping
    public AjaxResult edit(@RequestBody SparePart sparePart) {
        sparePart.setUpdateBy(SecurityUtils.getUsername());
        sparePart.setUpdateTime(new Date());
        return toBooleanAjax(sparePartService.updateById(sparePart));
    }

    /**
     * 删除备件总账表
     */
    @ApiOperation("删除备件总账表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(sparePartService.removeByIds(Arrays.asList(ids)));
    }

}
