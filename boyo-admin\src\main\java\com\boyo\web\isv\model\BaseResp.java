package com.boyo.web.isv.model;

public class BaseResp {

    /**
     * 调用结果码
     * <p>
     * 具体可用错误码参考：https://support.huaweicloud.com/accessg-marketplace/zh-cn_topic_0070649057.html
     */
    private String resultCode;

    /**
     * 调用结果描述
     * <p>
     * 注意：如果备注包含中文内容 ，请将中文转换成unicode编码，例如：“中文”可以转换成“\u4e2d\u6587”
     */
    private String resultMsg;

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultMsg() {
        return resultMsg;
    }

    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
    }
}
