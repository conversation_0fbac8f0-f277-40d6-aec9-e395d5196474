package com.boyo.web.controller.eam;

import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.eam.domain.EquipInspectionTemplItem;
import com.boyo.eam.service.IEquipInspectionTemplItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 点检-项目(EquipInspectionTemplItem)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-29 10:21:50
 */
@Api("点检-项目")
@RestController
@RequestMapping("/equip/equipInspectionTemplItem")
@AllArgsConstructor
public class EquipInspectionTemplItemController extends BaseController{
    /**
     * 服务对象
     */
    private final IEquipInspectionTemplItemService equipInspectionTemplItemService;

    /**
     * 查询点检-项目列表
     *
     */
    @ApiOperation("查询点检-项目列表")
    @GetMapping("/list")
    public TableDataInfo list(EquipInspectionTemplItem equipInspectionTemplItem) {
        startPage();
        List<EquipInspectionTemplItem> list = equipInspectionTemplItemService.selectEquipInspectionTemplItemList(equipInspectionTemplItem);
        return getDataTable(list);
    }
    
    /**
     * 获取点检-项目详情
     */
    @ApiOperation("获取点检-项目详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(equipInspectionTemplItemService.getById(id));
    }

    /**
     * 新增点检-项目
     */
    @ApiOperation("新增点检-项目")
    @PostMapping
    public AjaxResult add(@RequestBody EquipInspectionTemplItem equipInspectionTemplItem) {
        return toBooleanAjax(equipInspectionTemplItemService.save(equipInspectionTemplItem));
    }

    /**
     * 修改点检-项目
     */
    @ApiOperation("修改点检-项目")
    @PutMapping
    public AjaxResult edit(@RequestBody EquipInspectionTemplItem equipInspectionTemplItem) {
        return toBooleanAjax(equipInspectionTemplItemService.updateById(equipInspectionTemplItem));
    }

    /**
     * 删除点检-项目
     */
    @ApiOperation("删除点检-项目")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(equipInspectionTemplItemService.removeByIds(Arrays.asList(ids)));
    }

}
