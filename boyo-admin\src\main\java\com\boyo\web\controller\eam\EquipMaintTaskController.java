package com.boyo.web.controller.eam;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.eam.domain.*;
import com.boyo.eam.service.*;
import com.boyo.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 维保任务管理(EquipMaintTask)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-15 09:18:32
 */
@Api("维保任务管理")
@RestController
@RequestMapping("/equip/equipMaintTask")
@AllArgsConstructor
public class EquipMaintTaskController extends BaseController{
    /**
     * 服务对象
     */
    private final IEquipMaintTaskService equipMaintTaskService;
    private final IEquipMaintTaskItemService equipMaintTaskItemService;
    private final IEquipMaintTaskItemDetailService equipMaintTaskItemDetailService;

    private final IEquipMaintTemplService equipMaintTemplService;
    private final IEquipMaintTemplItemService equipMaintTemplItemService;
    private final IEquipMaintTemplItemDetailService equipMaintTemplItemDetailService;
    private final IEquipMaintRecordService equipMaintRecordService;

    private final ISysUserService sysUserService;
    private final IEquipLedgerService equipLedgerService;
    /**
     * 查询维保任务管理列表
     *
     */
    @ApiOperation("查询维保任务管理列表")
    @GetMapping("/list")
    public TableDataInfo list(EquipMaintTask equipMaintTask) {
        List<EquipMaintTask> list = equipMaintTaskService.selectEquipMaintTaskList(equipMaintTask);
        return getDataTable(list);
    }
    
    /**
     * 获取维保任务管理详情
     */
    @ApiOperation("获取维保任务管理详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        EquipMaintTask equipMaintTask = equipMaintTaskService.getById(id);

        List<EquipMaintTaskItem> itemList = equipMaintTaskItemService.list(
            Wrappers.<EquipMaintTaskItem>lambdaQuery()
                .eq(EquipMaintTaskItem::getEquipMaintTaskOpenid, equipMaintTask.getOpenid())
        );
        for (EquipMaintTaskItem emti:itemList){
            emti.setDetailList(equipMaintTaskItemDetailService.list(
                Wrappers.<EquipMaintTaskItemDetail>lambdaQuery()
                    .eq(EquipMaintTaskItemDetail::getEquipMaintTaskItemOpenid,emti.getOpenid())
            ));
        }
        equipMaintTask.setItemList(itemList); //设置维保任务中的项目
        // 设置设备名称
        String equipOpenids = equipMaintTask.getEquipLedgerOpenid();
        if(equipOpenids!=null&& !"".equals(equipOpenids)){
            String[] split = equipOpenids.split(",");
            List<EquipLedger> equipLedgerList = equipLedgerService.list(
                    Wrappers.<EquipLedger>lambdaQuery()
                            .in(EquipLedger::getOpenid, split)
            );
            StringBuffer buffer = new StringBuffer();
            for (EquipLedger equip:equipLedgerList){
                buffer.append(equip.getName()).append(",");
            }
            buffer.deleteCharAt(buffer.length()-1);
            equipMaintTask.setEquipNames(buffer.toString());
        }
        // 设置维保人员
        equipMaintTask.setStaffName(sysUserService.selectUserById(Long.parseLong(String.valueOf(equipMaintTask.getSysUserId()))).getNickName());
        return AjaxResult.success(equipMaintTask);
    }

    /**
     * 新增维保任务管理
     */
    @ApiOperation("新增维保任务管理")
    @Transactional
    @PostMapping("/batch")
    public AjaxResult addBatch(@RequestBody EquipMaintTask equipMaintTask) {
        equipMaintTask.setCreateBy(SecurityUtils.getUsername());
        equipMaintTask.setCreateTime(new Date());
        String templOpenid = equipMaintTask.getEquipMaintTemplOpenid();
        List<EquipMaintTemplItem> templItemList = equipMaintTemplItemService.list(
                Wrappers.<EquipMaintTemplItem>lambdaQuery()
                        .eq(EquipMaintTemplItem::getEquipMaintTemplOpenid, templOpenid)
        );
        List<EquipMaintTaskItem> taskItemList = new ArrayList<>();
        for (EquipMaintTemplItem emti:templItemList){
            EquipMaintTaskItem taskItem = new EquipMaintTaskItem();
            List<EquipMaintTemplItemDetail> detailList = equipMaintTemplItemDetailService.list(
                    Wrappers.<EquipMaintTemplItemDetail>lambdaQuery()
                            .eq(EquipMaintTemplItemDetail::getEquipMaintTemplItemOpenid, emti.getOpenid())
            );
            List<EquipMaintTaskItemDetail> taskDetailList = new ArrayList<>();
            for (EquipMaintTemplItemDetail emtid:detailList){
                EquipMaintTaskItemDetail taskDetail = new EquipMaintTaskItemDetail();
                taskDetail.setDetail(emtid.getDetail());
                taskDetail.setValue(emtid.getValue());
                taskDetail.setType(emtid.getType());
                taskDetail.setMaxNum(emtid.getMaxNum());
                taskDetail.setMinNum(emtid.getMinNum());
                taskDetail.setUnit(emtid.getUnit());
                taskDetail.setStandard(emtid.getStandard());
                taskDetail.setCreateBy(SecurityUtils.getUsername());
                taskDetail.setCreateTime(new Date());
                taskDetailList.add(taskDetail);
            }
            taskItem.setDetailList(taskDetailList);
            taskItem.setItem(emti.getItem());
            taskItem.setHour(emti.getHour());
            taskItem.setHourUnit(emti.getHourUnit());
            taskItem.setCreateBy(SecurityUtils.getUsername());
            taskItem.setCreateTime(new Date());
            taskItemList.add(taskItem);
        }
        equipMaintTask.setItemList(taskItemList);


        //计划日期
        Date date = equipMaintTask.getDate();
        //计划结束日期
        Date createEndDate = equipMaintTask.getCreateEndDate();
        //周期：0仅此一次，1每周，2每月，3每隔N天（若从维保任务管理中进行添加，则为0）
        String cycle = equipMaintTask.getCycle();
        //间隔天数
        Integer day = equipMaintTask.getDay();

        List<Date> dateList = new ArrayList<>();
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);

        dateList.add(cal.getTime());
        if ("1".equals(cycle)){
            while(true){
                cal.add(Calendar.WEEK_OF_YEAR,1);
                // 如果日期大于计划结束日期
                if (cal.getTimeInMillis()>createEndDate.getTime()){
                    break;
                }
                dateList.add(cal.getTime());
            }
        }else if ("2".equals(cycle)){
            while(true){
                cal.add(Calendar.MONTH,1);
                // 如果日期大于计划结束日期
                if (cal.getTimeInMillis()>createEndDate.getTime()){
                    break;
                }
                cal.set(Calendar.DAY_OF_MONTH,Integer.parseInt(DateUtil.format(date,"dd")));
                if (cal.get(Calendar.DAY_OF_MONTH)==Integer.parseInt(DateUtil.format(date,"dd"))){
                    dateList.add(cal.getTime());
                }else{
                    cal.add(Calendar.MONTH,-1);
                }
            }
        }else if ("3".equals(cycle)){
            while(true){
                cal.add(Calendar.DAY_OF_MONTH,day);
                // 如果日期大于计划结束日期
                if (cal.getTimeInMillis()>createEndDate.getTime()){
                    break;
                }
                dateList.add(cal.getTime());
            }
        }
        String[] split = equipMaintTask.getSysUserId().split(",");
        for(Date date1:dateList){
            String newPlanCode = getNewPlanCode();
            for (int i = 0;i<split.length;i++){
                equipMaintTask.setSysUserId(split[i]);
                equipMaintTask.setDate(date1);
                equipMaintTask.setPlanCode(newPlanCode);
                boolean b = add(equipMaintTask);
                if (!b){
                    return toBooleanAjax(false);
                }
            }
        }
        return toBooleanAjax(true);
    }

    private Boolean add(EquipMaintTask equipMaintTask) {
        String taskOpenid = super.generateOpenid();
        equipMaintTask.setOpenid(taskOpenid);
        equipMaintTask.setTaskCode(getNewTaskCode());
        equipMaintTask.setState("0");//待处理
        List<EquipMaintTaskItem> items = equipMaintTask.getItemList();
        if (items!=null&&items.size()>0){
            for (EquipMaintTaskItem emti:items){
                String taskItemOpenid = super.generateOpenid();
                emti.setOpenid(taskItemOpenid);
                emti.setEquipMaintTaskOpenid(taskOpenid);
                List<EquipMaintTaskItemDetail> details = emti.getDetailList();
                if (details!=null&&details.size()>0){
                    for (EquipMaintTaskItemDetail emtid:details){
                        String taskItemDetailOpenid = super.generateOpenid();
                        emtid.setOpenid(taskItemDetailOpenid);
                        emtid.setEquipMaintTaskItemOpenid(taskItemOpenid);
                    }
                    equipMaintTaskItemDetailService.saveBatch(details);
                }
            }
            equipMaintTaskItemService.saveBatch(items);
        }
        return equipMaintTaskService.save(equipMaintTask);
    }

    /**
     * 修改维保任务管理
     */
    @ApiOperation("修改维保任务管理")
    @Transactional
    @PutMapping
    public AjaxResult edit(@RequestBody EquipMaintTask equipMaintTask) {
        String taskOpenid = equipMaintTask.getOpenid();
        List<EquipMaintTaskItem> items = equipMaintTask.getItemList();

        //删除旧数据
        removeChild(taskOpenid);
        //插入新数据
        if (items!=null&&items.size()>0){
            for (EquipMaintTaskItem emti:items){
                if (emti.getOpenid()==null||"".equals(emti.getOpenid())){
                    emti.setOpenid(super.generateOpenid());
                }
                emti.setEquipMaintTaskOpenid(taskOpenid);
                List<EquipMaintTaskItemDetail> details = emti.getDetailList();
                if (details!=null&&details.size()>0){
                    String taskItemOpenid = emti.getOpenid();
                    for (EquipMaintTaskItemDetail emtid:details){
                        if (emtid.getOpenid()==null||"".equals(emtid.getOpenid())){
                            emtid.setOpenid(super.generateOpenid());
                        }
                        emtid.setEquipMaintTaskItemOpenid(taskItemOpenid);
                    }
                    equipMaintTaskItemDetailService.saveBatch(details);
                }
            }
            equipMaintTaskItemService.saveBatch(items);
        }

        return toBooleanAjax(equipMaintTaskService.updateById(equipMaintTask));
    }

    /**
     * 删除维保任务管理
     */
    @ApiOperation("删除维保任务管理")
    @Transactional
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        List<EquipMaintTask> taskList = equipMaintTaskService.list(
                Wrappers.<EquipMaintTask>lambdaQuery()
                        .in(EquipMaintTask::getId, ids)
        );
        for (EquipMaintTask emt:taskList){
            removeChild(emt.getOpenid());
        }
        return toBooleanAjax(equipMaintTaskService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 删除模板下额子模块
     * @param taskOpenid
     * @return
     */
    private boolean removeChild(String taskOpenid){
        // 删除子模块
        List<EquipMaintTaskItem> delItems = equipMaintTaskItemService.list(
            Wrappers.<EquipMaintTaskItem>lambdaQuery()
                .eq(EquipMaintTaskItem::getEquipMaintTaskOpenid, taskOpenid)
        );
        if (delItems!=null&&delItems.size()>0){
            for (EquipMaintTaskItem emti:delItems){
                equipMaintTaskItemDetailService.remove(
                    Wrappers.<EquipMaintTaskItemDetail>lambdaQuery()
                        .eq(EquipMaintTaskItemDetail::getEquipMaintTaskItemOpenid,emti.getOpenid())
                );
            }
        }
        return equipMaintTaskItemService.remove(
            Wrappers.<EquipMaintTaskItem>lambdaQuery()
                .eq(EquipMaintTaskItem::getEquipMaintTaskOpenid,taskOpenid)
        );
    }

    /**
     * 获取最新计划编号
     */
    private String getNewPlanCode(){

        String prefix = "EMRP" + DateUtil.format(new Date(),"yyyyMMdd");

        List<EquipMaintTask> equipMaintTaskList = equipMaintTaskService.list(
            Wrappers.<EquipMaintTask>lambdaQuery()
                .like(EquipMaintTask::getPlanCode,prefix)
        );
        int max = 0;
        if (equipMaintTaskList!=null&&equipMaintTaskList.size()>0){
            for (EquipMaintTask emt:equipMaintTaskList){
                String planCode = emt.getPlanCode();
                int num = Integer.parseInt(planCode.replace(prefix,""));
                if (num>max){
                    max = num;
                }
            }
        }
        return prefix+String.format("%02d", max+1);
    }

    /**
     * 获取最新派工单号
     */
    private String getNewTaskCode(){

        String prefix = "RT" + DateUtil.format(new Date(),"yyyyMMdd");

        List<EquipMaintTask> equipMaintTaskList = equipMaintTaskService.list(
                Wrappers.<EquipMaintTask>lambdaQuery()
                        .like(EquipMaintTask::getTaskCode,prefix)
        );
        int max = 0;
        if (equipMaintTaskList!=null&&equipMaintTaskList.size()>0){
            for (EquipMaintTask emt:equipMaintTaskList){
                String taskCode = emt.getTaskCode();
                int num = Integer.parseInt(taskCode.replace(prefix,""));
                if (num>max){
                    max = num;
                }
            }
        }
        return prefix+String.format("%03d", max+1);
    }

    // 移动端接口

    @ApiOperation("查询维保任务管理列表")
    @GetMapping("/pad/list")
    public TableDataInfo padList(EquipMaintTask equipMaintTask) {
        equipMaintTask.setSysUserId(String.valueOf(SecurityUtils.getLoginUser().getUser().getUserId()));
//        equipMaintTask.setSysUserId("130"); //测试
        startPage();
        List<EquipMaintTask> list = equipMaintTaskService.selectEquipMaintTaskList(equipMaintTask);
        return getDataTable(list);
    }


    @ApiOperation("点击开始或者结束")
    @Transactional
    @PutMapping("/pad/beginEnd")
    public AjaxResult beginEnd(@RequestBody Map<String,String> values) {
        String openid = values.get("openid"); // 任务openid
        String state = values.get("state"); // 0是点击的开始，1是点击的结束

        EquipMaintTask equipMaintTask = equipMaintTaskService.getOne(
                Wrappers.<EquipMaintTask>lambdaQuery()
                        .eq(EquipMaintTask::getOpenid, openid)
        );
        if ("0".equals(state)){
            equipMaintTask.setState("1");//进行中
            equipMaintTask.setTaskBeginDate(new Date());
            equipMaintRecordService.insertByTaskOpenid(openid);
        }else if ("1".equals(state)){
            equipMaintTask.setState("2");//已完成
            equipMaintTask.setTaskEndDate(new Date());
            equipMaintRecordService.updateByTaskOpenid(openid);
        }
        return AjaxResult.success(equipMaintTaskService.updateById(equipMaintTask));
    }
}
