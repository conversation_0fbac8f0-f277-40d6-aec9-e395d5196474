package com.boyo.web.isv.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("isv_license")
public class GetLicenseReq {

    private Long id;
    /**
     * 客户在华为云注册账号的唯一标识。
     * 必定返回
     */
    private String customerId;

    /**
     * 客户在华为云注册的账户名。
     * 非必返回
     */
    private String customerName;

    /**
     * 云商店业务ID。
     * 每一次请求，businessId皆不一致。
     * 必返回
     */
    private String businessId;

    /**
     * 云商店订单ID。
     * 必返回
     */
    private String orderId;

    /**
     * 产品规格标识。租户购买包月或包年的产品后，可能会续费，续费支持变更周期类型（例如包月转包年），此时，租户开通的实例instanceId对应的productId会变化，但skuCode不变。
     * 非必返回
     */
    private String skuCode;

    /**
     * 产品标识，同一skuCode下，不同周期类型的productId不同。
     * 例如：ISV发布产品，新增一个规格，会生成一个skuCode，再配置包年价格，包月价格，会生成两个productId。
     * 必返回
     */
    private String productId;

    /**
     * 过期时间。
     * 格式：yyyyMMddHHmmss
     * 非必返回
     */
    private String expireTime;

    /**
     * 周期类型。
     * 非必传，如需此参数，计费类型需选择包周期chargingMode=1，包周期购买场景请求时传该参数。
     * 年："year"
     * 月："month"
     * 非必返回
     */
    private String periodType;

    /**
     * 数量类型的商品定价属性。非必填。
     * 属性名称：数量（支持服务商自定义名称）
     * 单位：个（次）
     * 非必返回
     */
    private Integer amount;

    /**
     * 商品实例开通方式。
     * 非必返回
     */
    private Integer provisionType;

    /**
     * 计费模式。
     * 3：表示按次购买。
     * 非必返回
     */
    private Integer chargingMode;

    /**
     * 周期数量。
     * 非必返回
     */
    private Integer periodNumber;

    /**
     * 扩展参数，如果activity=getLicense，则必须要该数据。
     * 扩展参数格式为json数组字符串通过 urlEncode(base64(saasExtendParams))携带到url参数中。在得到saasExtendParams参数的值后，需要通过base64Decode(urlDecode(saasExtendParams))获取扩展参数json数组。
     * 例如：[{"name":"identificationCode","value":"00000016B5B044F7B51BA6D848"}]
     * 其中identificationCode为订单支付后，买家在服务监管里填写的值，服务商依赖于该值结合订单信息、skuCode、productId等信息生成license。
     * 非必返回
     */
    @TableField(exist = false)
    private String saasExtendParams;

    /**
     * 请求发起时的时间戳，取UTC时间。
     * 格式：yyyyMMddHHmmssSSS
     * 非必返回
     */
    private String timeStamp;

    /**
     * 安全校验令牌。
     * 具体生成逻辑参考：https://support.huaweicloud.com/accessg-marketplace/zh-cn_topic_0070649066.html
     */
    @TableField(exist = false)
    private String authToken;

    private String license;


    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(String expireTime) {
        this.expireTime = expireTime;
    }

    public String getPeriodType() {
        return periodType;
    }

    public void setPeriodType(String periodType) {
        this.periodType = periodType;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public Integer getProvisionType() {
        return provisionType;
    }

    public void setProvisionType(Integer provisionType) {
        this.provisionType = provisionType;
    }

    public Integer getChargingMode() {
        return chargingMode;
    }

    public void setChargingMode(Integer chargingMode) {
        this.chargingMode = chargingMode;
    }

    public Integer getPeriodNumber() {
        return periodNumber;
    }

    public void setPeriodNumber(Integer periodNumber) {
        this.periodNumber = periodNumber;
    }

    public String getSaasExtendParams() {
        return saasExtendParams;
    }

    public void setSaasExtendParams(String saasExtendParams) {
        this.saasExtendParams = saasExtendParams;
    }

    public String getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(String timeStamp) {
        this.timeStamp = timeStamp;
    }

    public String getAuthToken() {
        return authToken;
    }

    public void setAuthToken(String authToken) {
        this.authToken = authToken;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLicense() {
        return license;
    }

    public void setLicense(String license) {
        this.license = license;
    }
}
