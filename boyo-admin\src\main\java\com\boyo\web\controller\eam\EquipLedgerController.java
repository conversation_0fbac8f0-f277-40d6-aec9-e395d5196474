package com.boyo.web.controller.eam;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.eam.domain.*;
import com.boyo.eam.service.IEquipLedgerPropertyService;
import com.boyo.eam.service.IEquipLedgerService;
import com.boyo.eam.service.IEquipLedgerSparePartService;
import com.boyo.framework.annotation.Tenant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 设备台账(EquipLedger)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:06
 */
@Api("设备台账")
@RestController
@RequestMapping("/equip/equipLedger")
@AllArgsConstructor
@Tenant
public class EquipLedgerController extends BaseController{
    /**
     * 服务对象
     */
    private final IEquipLedgerService equipLedgerService;
    private final IEquipLedgerPropertyService equipLedgerPropertyService;
    private final IEquipLedgerSparePartService equipLedgerSparePartService;
    /**
     * 查询设备台账列表
     *
     */
    @ApiOperation("查询设备台账列表")
    @GetMapping("/list")
    public TableDataInfo list(EquipLedger equipLedger) {
        startPage();
        List<EquipLedger> list = equipLedgerService.selectEquipLedgerList(equipLedger);
        return getDataTable(list);
    }

    /**
     * 获取设备台账详情
     */
    @ApiOperation("获取设备台账详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        EquipLedger byId = equipLedgerService.getInfo(id);
        List<EquipLedgerProperty> list = equipLedgerPropertyService.list(
                Wrappers.<EquipLedgerProperty>lambdaQuery()
                        .eq(EquipLedgerProperty::getEquipLedgerOpenid, byId.getOpenid())
        );
        List<EquipLedgerSparePart> list1 = equipLedgerSparePartService.list(
                Wrappers.<EquipLedgerSparePart>lambdaQuery()
                        .eq(EquipLedgerSparePart::getEquipLedgerOpenid, byId.getOpenid())
        );

        byId.setProperties(list);
        if (list1!=null&&list1.size()>0){
            byId.setSparePartStr(eListToStr(list1));
        }
        return AjaxResult.success(byId);
    }

    /**
     * 用逗号拼接部件的openid
     */
    private String eListToStr(List<EquipLedgerSparePart> list){
        StringBuffer buffer = new StringBuffer();
        for(EquipLedgerSparePart e:list){
            buffer.append(e.getSparePartOpenid()).append(",");
        }
        buffer.deleteCharAt(buffer.length()-1);
        return buffer.toString();
    }

    /**
     * 新增设备台账
     */
    @ApiOperation("新增设备台账")
    @Transactional
    @PostMapping
    public AjaxResult add(@RequestBody EquipLedger equipLedger) {
        if (equipLedger.getParentOpenid()==null||"".equals(equipLedger.getParentOpenid())){
            equipLedger.setParentOpenid("-1");
        }
        String equipLedgerOpenid = super.generateOpenid();
        equipLedger.setOpenid(equipLedgerOpenid);

        // 获取台账属性
        List<EquipLedgerProperty> properties = equipLedger.getProperties();
        // 获取台账和部件关联关系
        String sparePartStr = equipLedger.getSparePartStr();

        if (sparePartStr!=null&&!"".equals(sparePartStr)){
            String[] split = sparePartStr.split(",");
            List<EquipLedgerSparePart> spareParts = new ArrayList<>();
            for (int i =0;i<split.length;i++){
                EquipLedgerSparePart e = new EquipLedgerSparePart();
                e.setOpenid(super.generateOpenid());
                e.setEquipLedgerOpenid(equipLedgerOpenid);
                e.setSparePartOpenid(split[i]);
                spareParts.add(e);
            }
            equipLedgerSparePartService.saveBatch(spareParts);
        }

        if (properties!=null&&properties.size()>0){
            for (EquipLedgerProperty o:properties){
                o.setEquipLedgerOpenid(equipLedgerOpenid);
                if (o.getOpenid()==null||"".equals(o.getOpenid())){
                    o.setOpenid(super.generateOpenid());
                }
            }
            equipLedgerPropertyService.saveBatch(properties);
        }

        return toBooleanAjax(equipLedgerService.save(equipLedger));
    }

    /**
     * 修改设备台账
     */
    @ApiOperation("修改设备台账")
    @PutMapping
    public AjaxResult edit(@RequestBody EquipLedger equipLedger) {

        // 移除旧属性和旧部件关联
        equipLedgerPropertyService.removeEquipLedgerProperty(equipLedger);
        equipLedgerSparePartService.removeEquipLedgerSparePart(equipLedger);

        // 获取台账属性
        List<EquipLedgerProperty> properties = equipLedger.getProperties();
        // 获取台账和部件关联关系
        String sparePartStr = equipLedger.getSparePartStr();

        if (sparePartStr!=null&&!"".equals(sparePartStr)){
            String[] split = sparePartStr.split(",");
            List<EquipLedgerSparePart> spareParts = new ArrayList<>();
            for (int i =0;i<split.length;i++){
                EquipLedgerSparePart e = new EquipLedgerSparePart();
                if (e.getEquipLedgerOpenid()==null||"".equals(e.getEquipLedgerOpenid())){
                    e.setEquipLedgerOpenid(equipLedger.getOpenid());
                }
                if (e.getOpenid()==null||"".equals(e.getOpenid())){
                    e.setOpenid(super.generateOpenid());
                }
                e.setSparePartOpenid(split[i]);
                spareParts.add(e);
            }
            equipLedgerSparePartService.saveBatch(spareParts);
        }

        if (properties!=null&&properties.size()>0){
            for (EquipLedgerProperty o:properties){
                if (o.getEquipLedgerOpenid()==null||"".equals(o.getEquipLedgerOpenid())){
                    o.setEquipLedgerOpenid(equipLedger.getOpenid());
                }
                if (o.getOpenid()==null||"".equals(o.getOpenid())){
                    o.setOpenid(super.generateOpenid());
                }
            }
            equipLedgerPropertyService.saveBatch(properties);
        }

        return toBooleanAjax(equipLedgerService.updateById(equipLedger));
    }

    /**
     * 删除设备台账
     */
    @ApiOperation("删除设备台账")
    @Transactional
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        List<EquipLedger> equipLedgerList = equipLedgerService.list(
                Wrappers.<EquipLedger>lambdaQuery()
                        .in(EquipLedger::getId, ids)
        );
        ArrayList<String> openids = new ArrayList<>();
        for (EquipLedger o:equipLedgerList){
            openids.add(o.getOpenid());
        }
        List<EquipLedger> equipList = equipLedgerService.list(
                Wrappers.<EquipLedger>lambdaQuery()
                        .in(EquipLedger::getParentOpenid, openids)
        );
        if (equipList!=null && equipList.size()>0){
            return AjaxResult.error("将要删除的设备下含有子设备，不可删除");
        }
        equipLedgerPropertyService.remove(
                Wrappers.<EquipLedgerProperty>lambdaQuery()
                        .in(EquipLedgerProperty::getEquipLedgerOpenid, openids)
        );
        equipLedgerSparePartService.remove(
                Wrappers.<EquipLedgerSparePart>lambdaQuery()
                        .in(EquipLedgerSparePart::getEquipLedgerOpenid,openids)
        );
        return toBooleanAjax(equipLedgerService.removeByIds(Arrays.asList(ids)));
    }

}
