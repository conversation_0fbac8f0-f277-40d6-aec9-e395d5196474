package com.boyo.web.controller.thirdParty.Huaxia;

import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.SM2;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.iot.domain.HistoryData;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.service.IIotEquipmentService;
import com.boyo.iot.service.IIotTslAttrService;
import com.boyo.iot.util.IoTDBUtil;
import com.boyo.util.IoTMqttUtil;
import com.boyo.web.byycontroller.ByyController;
import com.google.common.util.concurrent.AtomicDouble;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.rpc.StatementExecutionException;
import org.apache.iotdb.session.Session;
import org.apache.iotdb.session.SessionDataSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;


@RestController
@RequestMapping("/thirdparty/huaxiabank")
public class HuaxiaBankController {

    @Resource
    private Session iotSession;

    @Autowired
    private IIotEquipmentService iotEquipmentService;
    @Autowired
    private IIotTslAttrService iotTslAttrService;

    @Resource
    private IoTDBUtil ioTDBUtil;

    @Resource
    private ByyController byyController;


    /**
     * 获取设备信息接口
     * <p>
     * 该接口用于查询指定租户下的设备信息，包括设备数量、设备开关状态、生产状态、生产数量、循环次数等。
     *
     * @param queryBo 查询条件对象，包含查询的时间范围、租户信息等
     * @return AjaxResult 包含设备信息的JSON对象，包括设备数量、开关状态、生产状态、生产数量、循环次数等
     * @throws IoTDBConnectionException    当与IoTDB数据库连接失败时抛出
     * @throws StatementExecutionException 当执行SQL语句失败时抛出
     */
    @GetMapping("/decive/info")
    public AjaxResult deciveInfo(@RequestBody QueryBo queryBo) throws IoTDBConnectionException, StatementExecutionException {

        JSONObject ans = new JSONObject();
        int totalPowerOn = 0;
        int totalPowerOff = 0;
        int totalPowerError = 0;
        int productStatusOn = 0;
        double productCount = 0;
        double cycleCount = 0;

        // 默认租户为大上电器，根据查询条件中的USCC动态获取租户信息
        String tenant = "4b880be617ba45629033ae5af382b6aa"; //大上电器
        if (queryBo.getUscc() != null && ConstantValue.companyMap.containsKey(queryBo.getUscc())) {
            tenant = ConstantValue.companyMap.get(queryBo.getUscc());
        }

        // 检查租户是否存在且数据源可用，若可用则手动切换数据源
        if (IoTMqttUtil.checkEnterprise(tenant) && IoTMqttUtil.tenantProcessor.checkDataSource(tenant)) {
            DynamicDataSourceContextHolder.push(tenant);
        } else {
            return AjaxResult.error("Tenant or data source is not available");
        }

        try {
            // 查询租户下的所有设备路径
            String showSql = "SHOW CHILD PATHS root." + tenant;
            final SessionDataSet sessionDataSet = iotSession.executeQueryStatement(showSql);

            // 解析设备路径，获取设备列表
            List<String> devicesList = new ArrayList<>();
            while (sessionDataSet.hasNext()) {
                String device = sessionDataSet.next().toString();
                final String substring = device.substring(device.lastIndexOf('.') + 1);
                devicesList.add(substring);
            }

            ans.put("deviceCount", devicesList.size());

            // 遍历设备列表，计算每个设备的开关状态、生产状态、生产数量、循环次数等信息
            for (String equipment : devicesList) {
                IotEquipment equipmentByCode = iotEquipmentService.getEquipmentByCode(equipment);

                if (equipmentByCode != null) {
                    // 计算设备的开关状态（Power_ON）
                    List<HistoryData> list = ioTDBUtil.listData(tenant, equipment, "Power_ON", queryBo.getStartTime(), queryBo.getEndTime());
                    if (list != null && !list.isEmpty()) {
                        for (HistoryData historyData : list) {
                            if (historyData.getVal() != null && !"null".equals(historyData.getVal())) {
                                if (historyData.getVal().toString().equalsIgnoreCase("1.0")) {
                                    totalPowerOn++;
                                } else {
                                    totalPowerOff++;
                                }
                            } else {
                                totalPowerError++;
                            }
                        }
                    }

                    // 计算设备的生产状态（Product_Status）
                    List<HistoryData> productStatusList = ioTDBUtil.listData(tenant, equipment, "Product_Status", queryBo.getStartTime(), queryBo.getEndTime());
                    if (productStatusList != null && !productStatusList.isEmpty()) {
                        for (HistoryData historyData : productStatusList) {
                            if (historyData.getVal() != null && historyData.getVal().toString().equalsIgnoreCase("1.0")) {
                                productStatusOn++;
                            }
                        }
                    }

                    // 计算设备的循环次数（Cycle）
                    List<HistoryData> cycleList = ioTDBUtil.listData(tenant, equipment, "Cycle", queryBo.getStartTime(), queryBo.getEndTime());
                    if (cycleList != null && !cycleList.isEmpty()) {
                        for (HistoryData historyData : cycleList) {
                            if (historyData.getVal() != null && !"null".equals(historyData.getVal())) {
                                try {
                                    cycleCount += Double.parseDouble(historyData.getVal().toString());
                                } catch (NumberFormatException e) {
                                    throw new RuntimeException(e);
                                }
                            }
                        }
                    }

                    // 计算设备的生产数量（Number）
                    List<HistoryData> numberList = ioTDBUtil.listData(tenant, equipment, "Number", queryBo.getStartTime(), queryBo.getEndTime());
                    if (numberList != null && !numberList.isEmpty()) {
                        double begin1 = 0, end1 = 0;
                        for (HistoryData historyData : numberList) {
                            if (historyData.getVal() != null && !"null".equals(historyData.getVal())) {
                                try {
                                    begin1 = Double.parseDouble(numberList.get(0).getVal().toString());
                                    end1 = Double.parseDouble(numberList.get(numberList.size() - 1).getVal().toString());
                                } catch (NumberFormatException e) {
                                    throw new RuntimeException(e);
                                }
                                if (end1 > begin1) {
                                    productCount += (end1 - begin1);
                                }
                                break;
                            }
                        }

                    }
                }
            }

            // 将计算结果放入返回的JSON对象中
            ans.put("totalPowerOn", totalPowerOn);
            ans.put("totalPowerOff", totalPowerOff);
            ans.put("productStatus", productStatusOn);
            ans.put("totalPowerError", totalPowerError);
            ans.put("productCount", productCount);
            ans.put("cycleCount", cycleCount);

            return AjaxResult.success(ans);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("查询失败");
        } finally {
            DynamicDataSourceContextHolder.poll();
        }
    }


    @GetMapping("/decive/info/byMiddleground")
    public AjaxResult deciveInfoApi(@RequestBody QueryBo queryBo) {
        if (queryBo.getUscc() == null || !ConstantValue.companyDeviceMap.containsKey(queryBo.getUscc())) {
            return AjaxResult.error("未找到该企业");
        }
        List<String> devicesList = ConstantValue.companyDeviceMap.get(queryBo.getUscc());

        // 初始化线程安全的结果变量
        AtomicInteger deviceCount = new AtomicInteger(devicesList.size());
        AtomicDouble totalPowerOn = new AtomicDouble(0);
        AtomicDouble totalPowerOff = new AtomicDouble(0);
        AtomicDouble totalPowerError = new AtomicDouble(0);
        AtomicDouble productStatusOn = new AtomicDouble(0);
        AtomicDouble productCount = new AtomicDouble(0);
        AtomicDouble cycleCount = new AtomicDouble(0);

        // 创建固定大小的线程池
        int threadPoolSize = Runtime.getRuntime().availableProcessors(); // 根据CPU核心数设置线程池大小
        ExecutorService executorService = Executors.newFixedThreadPool(threadPoolSize);

        // 存储任务的Future列表
        List<Future<?>> futures = new ArrayList<>();

        // 提交任务到线程池
        for (String device : devicesList) {
            futures.add(executorService.submit(() -> {
                try {
                    // 计算产量
                    final JSONArray numberStatus = getHistoryData(device, "Number", queryBo.getStartTime(), queryBo.getEndTime());
                    if (numberStatus != null && numberStatus.size() > 0) {
                        JSONObject begin = numberStatus.getJSONObject(0);
                        JSONObject end = numberStatus.getJSONObject(numberStatus.size() - 1);
                        if (begin != null && end != null && begin.get("val") != null && end.get("val") != null) {
                            try {
                                double beginVal = Double.parseDouble(begin.get("val").toString());
                                double endVal = Double.parseDouble(end.get("val").toString());
                                if (endVal > beginVal) {
                                    productCount.addAndGet(endVal - beginVal);
                                }
                            } catch (Exception e) {

                            }
                        }
                    }

                    // 计算节拍次数
                    final JSONArray cycleStatus = getHistoryData(device, "Cycle", queryBo.getStartTime(), queryBo.getEndTime());
                    if (cycleStatus != null && cycleStatus.size() > 0) {
                        for (int i = 0; i < cycleStatus.size(); i++) {
                            String val = cycleStatus.getJSONObject(i).getStr("val");
                            if (val != null && !val.isEmpty()) {
                                try {
                                    cycleCount.addAndGet(Double.parseDouble(val));
                                } catch (NumberFormatException e) {
                                }
                            }
                        }
                    }

                    // 计算电量
                    final JSONArray powerStatus = getHistoryData(device, "Power_ON", queryBo.getStartTime(), queryBo.getEndTime());
                    if (powerStatus != null && powerStatus.size() > 0) {
                        for (int i = 0; i < powerStatus.size(); i++) {
                            String val = powerStatus.getJSONObject(i).getStr("val");
                            try {
                                if (val != null && !val.isEmpty()) {
                                    if (Integer.parseInt(val) == 1) {
                                        totalPowerOn.addAndGet(1);
                                    } else {
                                        totalPowerOff.addAndGet(1);
                                    }
                                } else {
                                    totalPowerError.addAndGet(1);
                                }
                            } catch (Exception e) {

                            }
                        }
                    }
                    // 计算生产状态
                    final JSONArray productStatus = getHistoryData(device, "Product_Status", queryBo.getStartTime(), queryBo.getEndTime());
                    if (productStatus != null && productStatus.size() > 0) {
                        for (int i = 0; i < productStatus.size(); i++) {
                            try {
                                String val = productStatus.getJSONObject(i).getStr("val");
                                if (val != null && !val.isEmpty() && Integer.parseInt(val) == 1) {
                                    productStatusOn.addAndGet(1);
                                }
                            } catch (Exception e) {

                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }));
        }

        // 等待所有任务完成
        for (Future<?> future : futures) {
            try {
                future.get(); // 阻塞等待任务完成
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
            }
        }

        // 关闭线程池
        executorService.shutdown();

        // 构造返回结果
        JSONObject ans = new JSONObject();
        ans.put("deviceCount", deviceCount.get());
        ans.put("totalPowerOn", totalPowerOn.get());
        ans.put("totalPowerOff", totalPowerOff.get());
        ans.put("productStatus", productStatusOn.get());
        ans.put("totalPowerError", totalPowerError.get());
        ans.put("productCount", productCount.get());
        ans.put("cycleCount", cycleCount.get());

        return AjaxResult.success(ans);
    }


    public JSONArray getHistoryData(String deviceCodes, String tag, String startTime, String endTime) {
        String userCode = "S6731CB0DE4B03B80C2DFAFA2";
        String privateKey = "f069d9b9411cd9c67578697dfa094e4e38d88fda67927d268e5da882f954bc8e";
        RestTemplate restTemplate = new RestTemplate();
        // 请求信息
        String time = String.valueOf(System.currentTimeMillis());
        String message = "deviceCodes=" + deviceCodes + "&endTime=" + endTime + "&startTime=" + startTime + "&tag=" + tag + "&time=" + time + "&userCode=" + userCode;
        Map<String, String> map = new HashMap<>();
        map.put("time", time);
        map.put("userCode", userCode);
        map.put("deviceCodes", deviceCodes);
        map.put("sign", signClone(message, privateKey));
        map.put("sign", signClone(message, privateKey));

        map.put("tag", tag);
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json;charset=UTF-8");
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String, String>> r = new HttpEntity<>(map, headers);
        String url = "https://middleground-api.ningmengdou.com/iot/openapi/getHistory";
        ResponseEntity<JSONObject> response = restTemplate.postForEntity(url, r, JSONObject.class);
        JSONArray dataList = null;
        JSONObject jsonObject = response.getBody();
        if ((jsonObject.getInt("code") == 200)) {
            dataList = jsonObject.getJSONArray("data");
        }
        return dataList;
    }

    private static String signClone(String str, String privateKeyHex) {
        final SM2 sm2 = new SM2(privateKeyHex, null, null);
        if (StrUtil.isNotEmpty(str)) {
            sm2.usePlainEncoding();
            byte[] sign = sm2.sign(str.getBytes(), null);
            return HexUtil.encodeHexStr(sign);
        }
        return "";
    }
}




