//package com.boyo.web.controller.common;
//
//import com.aliyun.oss.OSS;
//import com.aliyun.oss.OSSClientBuilder;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//@RestController
//@RequestMapping("/common/upload")
//public class UploadController {
//    @Value("${aliyunOss.endpoint}")
//    private String endpoint;
//    @Value("${aliyunOss.accessKeyId}")
//    private String accessKeyId;
//    @Value("${aliyunOss.accessKeySecret}")
//    private String accessKeySecret;
//    @Value("${aliyunOss.bucket.project}")
//    private String projectBucket;
//    public void uploadProjectFile(){
//        try {
//            // 创建OSSClient实例。
//            OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
//            // 上传内容到指定的存储空间（bucketName）并保存为指定的文件名称（objectName）。
//            ossClient.putObject(projectBucket, objectName, inputStream);
//            // 关闭OSSClient。
//            ossClient.shutdown();
//            aliyunOssResult.setCode(200);
//            aliyunOssResult.setUrl(urlPrefix+objectName);
//            aliyunOssResult.setMsg("上传成功");
//        } catch (Exception e) {
//            e.printStackTrace();
//            aliyunOssResult.setCode(400);
//            aliyunOssResult.setMsg("上传失败");
//        }
//    }
//}
