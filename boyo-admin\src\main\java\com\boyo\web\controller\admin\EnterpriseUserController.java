package com.boyo.web.controller.admin;

import java.util.List;
import java.util.Arrays;

import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.common.utils.SecurityUtils;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.boyo.system.service.IEnterpriseUserService;

/**
 * 企业用户管理Controller
 *
 * <AUTHOR>
 */
@Api("企业用户管理")
@RestController
@RequestMapping("/system/enterpriseuser")
@AllArgsConstructor
public class EnterpriseUserController extends BaseController {
    private final IEnterpriseUserService enterpriseUserService;

    /**
     * 查询企业用户管理列表
     */
    @ApiOperation("查询企业用户管理列表")
    @GetMapping("/list")
    public TableDataInfo list(EnterpriseUser enterpriseUser) {
        startPage();
        enterpriseUser.setEnterpriseOpenid(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid());
        enterpriseUser.setUserAdmin(0L);
        List<EnterpriseUser> list = enterpriseUserService.selectEnterpriseUserList(enterpriseUser);
        return getDataTable(list);
    }

    /**
     * 获取企业用户管理详细信息
     */
    @ApiOperation("获取企业用户管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(enterpriseUserService.getById(id));
    }

    /**
     * 新增企业用户管理
     */
    @ApiOperation("新增企业用户管理")
    @PostMapping
    public AjaxResult add(@RequestBody EnterpriseUser enterpriseUser) {
        return toBooleanAjax(enterpriseUserService.save(enterpriseUser));
    }

    /**
     * 修改企业用户管理
     */
    @ApiOperation("修改企业用户管理")
    @PutMapping
    public AjaxResult edit(@RequestBody EnterpriseUser enterpriseUser) {
        return toBooleanAjax(enterpriseUserService.updateById(enterpriseUser));
    }

    /**
     * 删除企业用户管理
     */
    @ApiOperation("删除企业用户管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(enterpriseUserService.removeByIds(Arrays.asList(ids)));
    }
    @ApiOperation("修改当前用户基本信息")
    @PostMapping("/updateOwnUser")
    public AjaxResult updateOwnUser(@RequestBody EnterpriseUser enterpriseUser){
        return toBooleanAjax(enterpriseUserService.updateOwnUser(enterpriseUser));
    }
}
