package com.boyo.web.lysso.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.common.core.domain.model.LoginBody;
import com.boyo.common.utils.StringUtils;
import com.boyo.common.utils.uuid.UUID;
import com.boyo.framework.web.service.SysLoginService;
import com.boyo.system.domain.*;
import com.boyo.system.service.*;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/edgp")
@AllArgsConstructor
public class EdgpSsoController extends BaseController {

    @Autowired
    private ITSysEnterpriseService tSysEnterpriseService;
    @Autowired
    private SysLoginService sysLoginService;
    @Autowired
    private final IEnterpriseUserService enterpriseUserService;
    @Autowired
    private IEnterpriseDepartmentService departmentService;
    @Autowired
    private IEnterpriseRoleService enterpriseRoleService;
    @Autowired
    private IEnterpriseRoleFunctionService roleFunctionService;
    @Autowired
    private ISysEnterpriseAuthorityService sysEnterpriseAuthorityService;
    @Autowired
    private IEnterpriseUserRoleService userRoleService;

    private final String url = "https://dmtest.ahapp.net/edgp_admin_interface/apps/web/user/infoByToken";

    private static final String[] function = {"2168","2252","2258","2188","2189","2190","2191","1","100","101",
        "102","103","104","105","106","2364","2365","2337","2338","2352",
        "2339","2340","2354","2355","2356","2341","2342","2343","2344","2345",
        "2346","2347","2357","2348","2349","2350","2351","2361","2362","2363",
        "2383","2384","2385","2386","2387","2388","2391","2392","2394","2195","2196","2197","2198","2199","2207","2212",
            "2213","2216","2219","2225","2226","2227","2228","2229","2231","2234","2236","2237","2240","2241","2242","2243",
            "2244","2313","2314","2315","2382"};

    @PostMapping("/sso/login")
    public AjaxResult ssoLogin(String token, HttpServletRequest request){
        if (StringUtils.isEmpty(token)) {
            return AjaxResult.error("token不能为空");
        }
        String s = HttpUtil.get(url + "?token=" + token);
        if (StringUtils.isNotEmpty(s)){
            JSONObject jsonObject = JSON.parseObject(s);
            Integer code = jsonObject.getInteger("code");
            String roleOpenid = "";
            String userOpenid = "";
            if (code == 200){
                JSONObject data = jsonObject.getJSONObject("data");
                String companyName = data.getString("companyName");
                String phone = data.getString("phone");
                String departmentName = data.getString("departmentName");
                Integer status = data.getInteger("status");
                Integer manager = data.getInteger("manager");
                String name = data.getString("name");
                String email = data.getString("email");
                if (status == 1){
                    return AjaxResult.error("此用户被禁用");
                }
                /** 判断企业是否存在 存在即可直接登录 **/
                TSysEnterprise tSysEnterprise = new TSysEnterprise();
                tSysEnterprise.setEnterpriseName(companyName);
                if(!tSysEnterpriseService.checkExist(tSysEnterprise)){
                    if (manager == 0)
                        return AjaxResult.error("非edgp平台企业管理员不可同步企业");
                    /** 企业不存在就创建企业 **/
                    String enterpriseOpenid = super.generateOpenid();
                    tSysEnterprise.setEnterpriseOpenid(enterpriseOpenid);
                    tSysEnterprise.setEnterpriseName(companyName);
                    tSysEnterprise.setEnterpriseAbbreviation(companyName);
                    tSysEnterprise.setEnterpriseCode(companyName);
                    tSysEnterprise.setEnterpriseContacts(name);
                    tSysEnterprise.setEnterprisePhone(phone);
                    if (StringUtils.isEmpty(email)){
                        email = "<EMAIL>";
                    }
                    tSysEnterprise.setEnterpriseEmail(email);
                    tSysEnterpriseService.save(tSysEnterprise);

                    /** 分配权限**/
                    //分配模块，企业管理，生产管理
                    SysEnterpriseAuthority authority = new SysEnterpriseAuthority();
                    authority.setEnterpriseOpenid(enterpriseOpenid);
                    List<SysEnterpriseAuthority> sysEnterpriseAuthorities = sysEnterpriseAuthorityService.selectSysEnterpriseAuthorityList(authority);
                    if (sysEnterpriseAuthorities == null || sysEnterpriseAuthorities.size() <= 0){
                        Calendar calendar = Calendar.getInstance();
                        // 将当前时间向后偏移15天
                        calendar.add(Calendar.DAY_OF_MONTH, 15);
                        // 将 Calendar 类型转换为 Date 类型
                        Date date = calendar.getTime();
                        //权限为空，进行授权
                        List<SysEnterpriseAuthority> sysEnterpriseAuthority = new ArrayList<>();
                        SysEnterpriseAuthority authority1 = new SysEnterpriseAuthority();
                        authority1.setEnterpriseOpenid(enterpriseOpenid);
                        authority1.setSystemOpenid("DCBB04D3554B42F0A376978D725F50B6"); //企业管理
                        authority1.setSystemValidity(date);
                        SysEnterpriseAuthority authority2 = new SysEnterpriseAuthority();
                        authority2.setEnterpriseOpenid(enterpriseOpenid);
                        authority2.setSystemOpenid("05149ef65a344abd93a35fdb41d6a63c"); //生产管理
                        authority2.setSystemValidity(date);
                        SysEnterpriseAuthority authority3 = new SysEnterpriseAuthority();
                        authority3.setEnterpriseOpenid(enterpriseOpenid);
                        authority3.setSystemOpenid("140e53bf9a6e41acb8dc2af3d3095819"); //设备管理
                        authority3.setSystemValidity(date);
                        sysEnterpriseAuthority.add(authority1);
                        sysEnterpriseAuthority.add(authority2);
                        sysEnterpriseAuthority.add(authority3);
                        sysEnterpriseAuthorityService.saveBatch(sysEnterpriseAuthority);
                    }
                    //创建管理员
                    EnterpriseRole enterpriseRole = new EnterpriseRole();
                    enterpriseRole.setEnterpriseOpenid(enterpriseOpenid);
                    enterpriseRole.setDataScope("1");
                    enterpriseRole.setRoleName("管理员");
                    enterpriseRole.setRoleDesc("总管理员，最高权限");
                    enterpriseRole.setRoleOpenid(IdUtil.simpleUUID());
                    enterpriseRoleService.saveRoleAdmin(enterpriseRole);
                    //管理员角色分配菜单
                    roleOpenid = enterpriseRoleService.selectEnterpriseRoleList(enterpriseRole).get(0).getRoleOpenid();
                    List<EnterpriseRoleFunction> enterpriseRoleFunctionList = new ArrayList<>();
                    for (String f : function) {
                        EnterpriseRoleFunction roleFunction = new EnterpriseRoleFunction();
                        roleFunction.setRoleOpenid(roleOpenid);
                        roleFunction.setFunctionOpenid(f);
                        roleFunction.setCreateTime(new Date());
                        roleFunction.setUpdateTime(new Date());
                        enterpriseRoleFunctionList.add(roleFunction);
                    }
                    roleFunctionService.saveBatch(enterpriseRoleFunctionList);
                }
                /** 企业存在就检索企业，为后续创建员工和部门做准备 **/
                System.out.println("--- 企业存在，检索企业 ---");
                QueryWrapper<TSysEnterprise> enterpriseQueryWrapper = new QueryWrapper<>();
                enterpriseQueryWrapper.eq("enterprise_name",companyName);
                TSysEnterprise one = tSysEnterpriseService.getOne(enterpriseQueryWrapper);
                /** 查询员工登录账号，如果员工存在即可登录，如果员工不存在需要创建部门和员工 **/
                System.out.println("--- 检索员工 ---");
                QueryWrapper<EnterpriseUser> userQueryWrapper = new QueryWrapper<>();
                userQueryWrapper.eq("user_phone",phone);
                userQueryWrapper.eq("enterprise_openid",one.getEnterpriseOpenid());
                EnterpriseUser user = enterpriseUserService.getOne(userQueryWrapper);
                if (user == null){
                    /** 如果员工不存在，则需要创建员工，首先需要查询部门是否存在，不存在就创建部门，存在就给员工赋值部门id **/
                    System.out.println("--- 员工不存在，创建员工 ---");
                    QueryWrapper<EnterpriseDepartment> deptWrapper = new QueryWrapper<>();
                    deptWrapper.eq("enterprise_openid",one.getEnterpriseOpenid());
                    deptWrapper.eq("department_name",companyName);

                    /** 创建员工 **/
                    EnterpriseDepartment createdDept = departmentService.getOne(deptWrapper);
                    EnterpriseUser createUser = new EnterpriseUser();
                    createUser.setUserName(name);
                    createUser.setDepartmentOpenid(createdDept.getDepartmentOpenid());
                    createUser.setEnterpriseOpenid(one.getEnterpriseOpenid());
                    userOpenid = this.generateOpenid();
                    createUser.setUserOpenid(userOpenid);
                    createUser.setUserPassword("$2a$10$FVfE/Lv7gtWv1S9DP5qPzuPAXhD09Vr6bm6uxH2WVJAuVWBb2.rvG");
                    createUser.setUserPhone(phone);
                    createUser.setUserEmail(email);
                    createUser.setUserStatus("1");
                    createUser.setUserFullName(name);
                    enterpriseUserService.save(createUser);
                    System.out.println("--- 创建员工完成 ---");
                }
                /** 判断这个员工是否具有管理员权限 **/
                EnterpriseUser userOne = enterpriseUserService.getOne(userQueryWrapper);
                EnterpriseUserRole enterpriseUserRole = new EnterpriseUserRole();
                enterpriseUserRole.setUserOpenid(userOne.getUserOpenid());
                List<EnterpriseUserRole> enterpriseUserRoles = userRoleService.selectEnterpriseUserRoleList(enterpriseUserRole);
                if (enterpriseUserRoles == null || enterpriseUserRoles.size() == 0){
                    //给这个员工赋予管理员权限
                    List<String> roleList = new ArrayList<>();
                    roleList.add(roleOpenid);
                    userOne.setRoleList(roleList);
                    enterpriseUserService.updateEnterpriseUserById(userOne);
                }

                /** 初始化数据库 **/
                if (StringUtils.isEmpty(one.getEnterpriseDatabaseUrl())){
                    System.out.println("--- 初始化数据库 ---");
                    tSysEnterpriseService.initDatabase(one.getEnterpriseOpenid());
                }

                /** 登录 **/
                System.out.println("--- 准备工作完成开始登录 ---");
                LoginBody loginBody = new LoginBody();
                loginBody.setCode("");
                loginBody.setPassword("123456");
                loginBody.setEnterpriseCode(one.getEnterpriseCode());
                loginBody.setUuid(UUID.fastUUID().toString());
                loginBody.setUsername(phone);
                String iotToken = sysLoginService.loginFromLingYang(loginBody, request);
                LoginInfo loginInfo = new LoginInfo();
                loginInfo.setToken(iotToken);
                loginInfo.setEnterpriseCode(companyName);
                loginInfo.setUsername(name);
                return AjaxResult.success("验证成功，正在登录...",loginInfo);
            }
            return AjaxResult.error("无效token");
        } else {
            return AjaxResult.error("无效token");
        }

    }

class LoginInfo{

        private String token;

        private String enterpriseCode;

        private String username;

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public String getEnterpriseCode() {
            return enterpriseCode;
        }

        public void setEnterpriseCode(String enterpriseCode) {
            this.enterpriseCode = enterpriseCode;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }
}


}
