package com.boyo.web.controller.iot;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.text.Convert;
import com.boyo.common.exception.CustomException;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.iot.domain.HistoryData;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.domain.IotTslAttr;
import com.boyo.iot.service.IIotEquipmentService;
import com.boyo.iot.util.IoTDBUtil;
import com.boyo.master.domain.BaseDict;
import com.boyo.master.domain.WorkTime;
import com.boyo.master.service.IBaseDictService;
import com.boyo.master.service.IBaseWorkTimeService;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.rpc.StatementExecutionException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

@Api("IoT生产模块")
@RestController
@RequestMapping("/iot/production")
@AllArgsConstructor
public class IotPorductionController {
    private final IBaseDictService baseDictService;
    private final IIotEquipmentService iotEquipmentService;
    private final IoTDBUtil ioTDBUtil;

    private IBaseWorkTimeService workTimeService;


    /**
     * 获取设备稼动率
     *
     * @param tag           计算点位
     * @param start         开始日期
     * @param end           结束日期
     * @param equipmentCode 设备编码，为空为查询全部
     * @param shift         班次  空：全部  day:白班  night:夜班
     * @return
     */
    @GetMapping("/listDayCropRate")
    public AjaxResult listDayCropRate(String tag, String start, String end, String equipmentCode, String shift) {
        JSONObject result = new JSONObject();
        //获取白班时间
        String dayStartTime = "08:00:00";
        String dayEndTime = "20:00:00";
        //晚班时间
        String nightStartTime = "20:00:00";
        String nightEndTime = "08:00:00";

        List<WorkTime> listWorkTime =null;
        try {
            listWorkTime = workTimeService.list();
        } catch (Exception e) {
//            e.printStackTrace();
        }
        if (ObjectUtil.isNotEmpty(listWorkTime)) {
            for (WorkTime temp : listWorkTime) {
                if ("SHIFT_DAY".equals(temp.getBaseCode())) {
                    dayStartTime = temp.getStartTime();
                    dayEndTime = temp.getEndTime();
                } else if ("SHIFT_NIGHT".equals(temp.getBaseCode())) {
                    nightStartTime = temp.getStartTime();
                    nightEndTime = temp.getEndTime();
                }
            }
        }

        //原先逻辑，不用了
//        BaseDict baseDict = new BaseDict();
//        baseDict.setBaseType("SHIFT_TIME");
//        List<BaseDict> shiftTimes = baseDictService.selectBaseDictList(baseDict);
//
//        for (BaseDict temp : shiftTimes) {
//            if (temp.getBaseCode().equalsIgnoreCase("SHIFT_DAY")) {
//                day = temp.getBaseDesc();
//            } else if (temp.getBaseCode().equalsIgnoreCase("SHIFT_NIGHT")) {
//                night = temp.getBaseDesc();
//            }
//        }
//        获取所有设备清单
        IotEquipment iotEquipment = new IotEquipment();
        iotEquipment.setEquipmentCode(equipmentCode);
        List<IotEquipment> list = iotEquipmentService.selectIotEquipmentList(iotEquipment);
        if (list != null && list.size() > 0) {

        } else {
            throw new CustomException("暂无可用设备");
        }

//        查询一天的
        if (start.equalsIgnoreCase(end)) {
//            白班
            if (StrUtil.isEmpty(shift) || shift.equalsIgnoreCase("day")) {
                String dayStart = start + " " + dayStartTime ;
                String dayEnd = start + " " + dayEndTime ;
                // 初始化标志变量，用于表示当前是否在工作时间内
                boolean in = false;
                // 当开始日期与当前日期相同时，判断当前时间是否在日班工作时间内
                if (start.equalsIgnoreCase(DateUtil.formatDate(new Date()))) {
                    // 如果当前时间在日班开始到结束时间内，更新标志变量和结束时间
                    if (DateUtil.isIn(new Date(), DateUtil.parseDateTime(dayStart), DateUtil.parseDateTime(dayEnd))) {
                        in = true;
                        dayEnd = DateUtil.formatDateTime(new Date());
                    }
                }
                // 判断设备编码是否为空，如果为空则赋予默认值
                if (StrUtil.isEmpty(equipmentCode)) {
                    equipmentCode = "**";
                }
                JSONArray array = getStatusTimeService(equipmentCode, tag, SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), dayStart, dayEnd, false);
                int sort = 0;
                for (IotEquipment temp : list) {
                    JSONObject object = new JSONObject();
                    if (!result.containsKey(start)) {
                        result.put(start, new JSONObject());
                    }
                    object = result.getJSONObject(start);
                    if (!object.containsKey(temp.getEquipmentName())) {
                        object.put(temp.getEquipmentName(), new JSONObject());
                        object.getJSONObject(temp.getEquipmentName()).put("sort", sort);
                        sort++;
                    }
                    object = object.getJSONObject(temp.getEquipmentName());
                    for (int i = 0; i < array.size(); i++) {
                        JSONObject obj = array.getJSONObject(i);
                        if (obj.getString("key").equalsIgnoreCase(temp.getEquipmentCode())) {
                            obj.put("in", in);
                            obj.put("long", DateUtil.between(new Date(), DateUtil.parseDateTime(dayStart), DateUnit.MINUTE));
                            object.put("day", obj);
                            break;
                        }
                    }
                }
            }
            //            夜班
            if (StrUtil.isEmpty(shift) || shift.equalsIgnoreCase("night")) {
                String nightStart = start + " " + nightStartTime;
                String nightEnd = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), 1)) + " " + nightEndTime;
                boolean in = false;
                if (start.equalsIgnoreCase(DateUtil.formatDate(new Date()))) {
                    if (DateUtil.isIn(new Date(), DateUtil.parseDateTime(nightStart), DateUtil.parseDateTime(nightEnd))) {
                        nightEnd = DateUtil.formatDateTime(new Date());
                        in = true;
                    }
                }
                if (StrUtil.isEmpty(equipmentCode)) {
                    equipmentCode = "**";
                }
                JSONArray array = getStatusTimeService(equipmentCode, tag, SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), nightStart, nightEnd, false);
                int sort = 0;
                for (IotEquipment temp : list) {
                    JSONObject object = new JSONObject();
                    if (!result.containsKey(start)) {
                        result.put(start, new JSONObject());
                    }
                    object = result.getJSONObject(start);
                    if (!object.containsKey(temp.getEquipmentName())) {
                        object.put(temp.getEquipmentName(), new JSONObject());
                        object.getJSONObject(temp.getEquipmentName()).put("sort", sort);
                        sort++;
                    }
                    object = object.getJSONObject(temp.getEquipmentName());
                    for (int i = 0; i < array.size(); i++) {
                        JSONObject obj = array.getJSONObject(i);
                        if (obj.getString("key").equalsIgnoreCase(temp.getEquipmentCode())) {
                            obj.put("in", in);
                            obj.put("long", DateUtil.between(new Date(), DateUtil.parseDateTime(nightStart), DateUnit.MINUTE));
                            object.put("night", obj);
                            break;
                        }
                    }
                }
//                result.put(start + "@night", getStatusTimeService(deviceStr, tag, SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), nightStart, nightEnd, false));
            }
        } else {
//            查询多天
            long days = DateUtil.betweenDay(DateUtil.parseDate(start), DateUtil.parseDate(end), true) + 1;
            for (int i = 0; i < days; i++) {
                if (StrUtil.isEmpty(shift) || shift.equalsIgnoreCase("day")) {
                    String dayStart = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)) + " " + dayStartTime;
                    String dayEnd = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)) + " " + dayEndTime;
                    if (DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)).equalsIgnoreCase(DateUtil.formatDate(new Date()))) {
                        if (DateUtil.isIn(new Date(), DateUtil.parseDateTime(dayStart), DateUtil.parseDateTime(dayEnd))) {
                            dayEnd = DateUtil.formatDateTime(new Date());
                        }
                    }
                    if (StrUtil.isEmpty(equipmentCode)) {
                        equipmentCode = "**";
                    }
                    JSONArray array = getStatusTimeService(equipmentCode, tag, SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), dayStart, dayEnd, false);
                    for (IotEquipment temp : list) {
                        JSONObject object = new JSONObject();
                        if (!result.containsKey(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)))) {
                            result.put(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)), new JSONObject());
                        }
                        object = result.getJSONObject(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)));
                        if (!object.containsKey(temp.getEquipmentName())) {
                            object.put(temp.getEquipmentName(), new JSONObject());
                        }
                        object = object.getJSONObject(temp.getEquipmentName());
                        for (int j = 0; j < array.size(); j++) {
                            JSONObject obj = array.getJSONObject(j);
                            if (obj.getString("key").equalsIgnoreCase(temp.getEquipmentCode())) {
                                object.put("day", obj);
                                break;
                            }
                        }
                    }
//                    result.put(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)) + "@day", getStatusTimeService(deviceStr, tag, SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), dayStart, dayEnd, false));
                }
                //            夜班
                if (StrUtil.isEmpty(shift) || shift.equalsIgnoreCase("night")) {
                    String nightStart = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)) + " " + nightStartTime;
                    String nightEnd = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), (i + 1))) + " " + nightEndTime;
                    if (DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)).equalsIgnoreCase(DateUtil.formatDate(new Date()))) {
                        if (DateUtil.isIn(new Date(), DateUtil.parseDateTime(nightStart), DateUtil.parseDateTime(nightEnd))) {
                            nightEnd = DateUtil.formatDateTime(new Date());
                        }
                    }
                    JSONArray array = getStatusTimeService(equipmentCode, tag, SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), nightStart, nightEnd, false);
                    for (IotEquipment temp : list) {
                        JSONObject object = new JSONObject();
                        if (!result.containsKey(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)))) {
                            result.put(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)), new JSONObject());
                        }
                        object = result.getJSONObject(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)));
                        if (!object.containsKey(temp.getEquipmentName())) {
                            object.put(temp.getEquipmentName(), new JSONObject());
                        }
                        object = object.getJSONObject(temp.getEquipmentName());
                        for (int j = 0; j < array.size(); j++) {
                            JSONObject obj = array.getJSONObject(j);
                            if (obj.getString("key").equalsIgnoreCase(temp.getEquipmentCode())) {
                                object.put("night", obj);
                                break;
                            }
                        }
                    }
//                    result.put(DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(start), i)) + "@night", getStatusTimeService(deviceStr, tag, SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), nightStart, nightEnd, false));
                }
            }

        }

        return AjaxResult.success(result);
    }

    /**
     * 按班次获取数据差值
     *
     * @param tag           点位
     * @param start         开始日期
     * @param end           结束日期
     * @param equipmentCode 设备编码，为空查询全部
     * @param shift         班次  空：全部  day:白班  night:夜班
     * @param detail        是否显示详情
     * @return
     */
    @GetMapping("/listShiftDifference")
    public AjaxResult listShiftDifference(String tag, String start, String end, String equipmentCode, String shift, boolean detail) {
        JSONObject result = new JSONObject();
        BaseDict baseDict = new BaseDict();
        baseDict.setBaseType("SHIFT_TIME");
        List<BaseDict> shiftTimes = baseDictService.selectBaseDictList(baseDict);
        String day = "08:00";
        String night = "20:00";
        for (BaseDict temp : shiftTimes) {
            if (temp.getBaseCode().equalsIgnoreCase("SHIFT_DAY")) {
                day = temp.getBaseDesc();
            } else if (temp.getBaseCode().equalsIgnoreCase("SHIFT_NIGHT")) {
                night = temp.getBaseDesc();
            }
        }
//        白班时长
        long daytime = DateUtil.between(DateUtil.parseDateTime("2022-01-01 " + day + ":00"),
                DateUtil.parseDateTime("2022-01-01 " + night + ":00"), DateUnit.HOUR, true);
//        夜班时长
        long nighttime = 24 - daytime;
        IotEquipment iotEquipment = new IotEquipment();
        List<IotEquipment> list = iotEquipmentService.selectIotEquipmentList(iotEquipment);
        Map<String, Double> multiples = new HashMap<>();
        if (list != null && list.size() > 0) {
            for (IotEquipment temp : list) {
                List<IotTslAttr> attrList = temp.getAttrList();
                multiples.put(temp.getEquipmentCode(), 1d);
                if (attrList != null && attrList.size() > 0) {
                    for (IotTslAttr attr : attrList) {
                        if (attr.getAttrCode().equalsIgnoreCase(tag)) {
                            multiples.put(temp.getEquipmentCode(), attr.getAttrMultiple());
                        }
                    }
                }
            }
        } else {
            throw new CustomException("暂无可用设备");
        }
        if (StrUtil.isEmpty(equipmentCode)) {
            equipmentCode = "**";
        }
        if (StrUtil.isEmpty(shift) || shift.equalsIgnoreCase("day")) {
//            计算白班
            String dayStart = start + " " + day + ":00";
            String dayEnd = end + " " + night + ":00";
            JSONArray array = ioTDBUtil.getMaxAndMinByShift(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), equipmentCode, tag, dayStart, dayEnd, daytime + "h");
            double sum = 0d;
            for (int i = 0; i < array.size(); i++) {
                if (array.getJSONObject(i).getString("min").equalsIgnoreCase("null")) {
                    array.getJSONObject(i).put("min", 0);
                }
                if (array.getJSONObject(i).getString("max").equalsIgnoreCase("null")) {
                    array.getJSONObject(i).put("max", 0);
                }
                array.getJSONObject(i).put("device", array.getJSONObject(i).getString("Device").split("\\.")[2]);
                if (multiples.containsKey(array.getJSONObject(i).getString("device"))) {
                    sum = sum + (Convert.toDouble(array.getJSONObject(i).get("max")) - Convert.toDouble(array.getJSONObject(i).get("min"))) * multiples.get(array.getJSONObject(i).getString("device"));
                }
            }
            result.put("daysum", NumberUtil.round(sum, 2));
            if (detail) {
                result.put("daylist", array);
            }
//            result.put("day",ioTDBUtil.getMaxAndMinByShift(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(),equipmentCode,tag,dayStart,dayEnd,daytime+"h"));
//            System.out.println(JSONObject.toJSONString(ioTDBUtil.getMaxAndMinByShift(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(),equipmentCode,tag,dayStart,dayEnd,daytime+"h")));
        }
        if (StrUtil.isEmpty(shift) || shift.equalsIgnoreCase("night")) {
//            计算夜班
            String nightStart = start + " " + night + ":00";
            String nightEnd = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(end), 1)) + " " + day + ":00";
//            result.put("night",ioTDBUtil.getMaxAndMinByShift(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(),equipmentCode,tag,nightStart,nightEnd,nighttime+"h"));
            JSONArray array = ioTDBUtil.getMaxAndMinByShift(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), equipmentCode, tag, nightStart, nightEnd, nighttime + "h");
            double sum = 0d;
            for (int i = 0; i < array.size(); i++) {
                if (array.getJSONObject(i).getString("min").equalsIgnoreCase("null")) {
                    array.getJSONObject(i).put("min", 0);
                }
                if (array.getJSONObject(i).getString("max").equalsIgnoreCase("null")) {
                    array.getJSONObject(i).put("max", 0);
                }
                array.getJSONObject(i).put("device", array.getJSONObject(i).getString("Device").split("\\.")[2]);
                if (multiples.containsKey(array.getJSONObject(i).getString("device"))) {
                    sum = sum + (Convert.toDouble(array.getJSONObject(i).get("max")) - Convert.toDouble(array.getJSONObject(i).get("min"))) * multiples.get(array.getJSONObject(i).getString("device"));
                }
            }
            result.put("nightsum", NumberUtil.round(sum, 2));
            if (detail) {
                result.put("nightlist", array);
            }
//            System.out.println(JSONObject.toJSONString(ioTDBUtil.getMaxAndMinByShift(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(),equipmentCode,tag,nightStart,nightEnd,nighttime+"h")));
        }
        return AjaxResult.success(result);
    }

    /**
     * @param tag           计算点位
     * @param rq            日期
     * @param equipmentCode 设备编码，为空为查询全部
     * @return
     */
    @GetMapping("/listTagChange")
    public AjaxResult listTagChange(String tag, String rq, String equipmentCode) {
        JSONObject result = new JSONObject();
        BaseDict baseDict = new BaseDict();
        baseDict.setBaseType("SHIFT_TIME");
        List<BaseDict> shiftTimes = baseDictService.selectBaseDictList(baseDict);
        String day = "08:00";
        for (BaseDict temp : shiftTimes) {
            if (temp.getBaseCode().equalsIgnoreCase("SHIFT_DAY")) {
                day = temp.getBaseDesc();
            }
        }
        String start = rq + " " + day + ":00";
        String end = DateUtil.formatDateTime(DateUtil.offsetDay(DateUtil.parseDateTime(start), 1));
//        获取所有设备清单
        IotEquipment iotEquipment = new IotEquipment();
        iotEquipment.setEquipmentCode(equipmentCode);
        List<IotEquipment> list = iotEquipmentService.selectIotEquipmentList(iotEquipment);
        if (list != null && list.size() > 0) {
        } else {
            throw new CustomException("暂无可用设备");
        }
        for (IotEquipment temp : list) {
            JSONObject object = new JSONObject();
            object.put("data", getStatusTime(temp.getEquipmentCode(), tag, SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), start, end));
            object.put("sort", temp.getEquipmentSort());
            result.put(temp.getEquipmentName(), object);
        }
        return AjaxResult.success(result);
    }

    /**
     * 每小时的参数差
     *
     * @param tag
     * @param rq
     * @param equipmentCode
     * @return
     */
    @GetMapping("/listTagDifferenceHours")
    public AjaxResult listTagDifferenceHours(String tag, String rq, String equipmentCode) {
        JSONObject result = new JSONObject();
        BaseDict baseDict = new BaseDict();
        baseDict.setBaseType("SHIFT_TIME");
        List<BaseDict> shiftTimes = baseDictService.selectBaseDictList(baseDict);
        String day = "08:00";
        for (BaseDict temp : shiftTimes) {
            if (temp.getBaseCode().equalsIgnoreCase("SHIFT_DAY")) {
                day = temp.getBaseDesc();
            }
        }
        String start = rq + " " + day + ":00";
        String end = DateUtil.formatDateTime(DateUtil.offsetDay(DateUtil.parseDateTime(start), 1));
//        获取所有设备清单
        IotEquipment iotEquipment = new IotEquipment();
        List<IotEquipment> list = iotEquipmentService.selectIotEquipmentList(iotEquipment);
        if (list != null && list.size() > 0) {
        } else {
            throw new CustomException("暂无可用设备");
        }
        if (StrUtil.isEmpty(equipmentCode)) {
            equipmentCode = "**";
        }
        result.put("hours", getMaxminHourService(equipmentCode, tag, SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), start, end, true));
        return AjaxResult.success(result);
    }

    @GetMapping("/listTagDifferenceDevice")
    public AjaxResult listTagDifferenceDevice(String tag, String rq, String equipmentCode) {
        JSONObject result = new JSONObject();
        BaseDict baseDict = new BaseDict();
        baseDict.setBaseType("SHIFT_TIME");
        List<BaseDict> shiftTimes = baseDictService.selectBaseDictList(baseDict);
        String day = "08:00";
        for (BaseDict temp : shiftTimes) {
            if (temp.getBaseCode().equalsIgnoreCase("SHIFT_DAY")) {
                day = temp.getBaseDesc();
            }
        }
        String start = rq + " " + day + ":00";
        String end = DateUtil.formatDateTime(DateUtil.offsetDay(DateUtil.parseDateTime(start), 1));
//        获取所有设备清单
        IotEquipment iotEquipment = new IotEquipment();
        List<IotEquipment> list = iotEquipmentService.selectIotEquipmentList(iotEquipment);
        if (list != null && list.size() > 0) {
        } else {
            throw new CustomException("暂无可用设备");
        }
        if (StrUtil.isEmpty(equipmentCode)) {
            equipmentCode = "**";
        }
        for (IotEquipment temp : list) {
            result.put(temp.getEquipmentCode(), new JSONObject());
            result.getJSONObject(temp.getEquipmentCode()).put("name", temp.getEquipmentName());
            result.getJSONObject(temp.getEquipmentCode()).put("sort", temp.getEquipmentSort());
            result.getJSONObject(temp.getEquipmentCode()).put("multiple", 1);
            List<IotTslAttr> attrList = temp.getAttrList();
            if (attrList != null && attrList.size() > 0) {
                for (IotTslAttr attr : attrList) {
                    if (attr.getAttrCode().equalsIgnoreCase(tag)) {
                        result.getJSONObject(temp.getEquipmentCode()).put("multiple", attr.getAttrMultiple());
                    }
                }
            }
        }
        JSONArray array = getMaxminService(equipmentCode, tag, SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), start, end, false);
        for (int i = 0; i < array.size(); i++) {
            JSONObject temp = array.getJSONObject(i);
            if (temp.getString("max").equalsIgnoreCase("null")) {
                temp.put("max", "0");
            }
            if (temp.getString("min").equalsIgnoreCase("null")) {
                temp.put("min", "0");
            }
            if (result.containsKey(temp.getString("Device"))) {
                result.getJSONObject(temp.getString("Device")).put("val", NumberUtil.round((Convert.toDouble(temp.getString("max")) - Convert.toDouble(temp.getString("min"))) * result.getJSONObject(temp.getString("Device")).getDoubleValue("multiple"), 2));
            }
        }
        return AjaxResult.success(result);
    }

    @GetMapping("/listTagDifferenceBetween")
    public AjaxResult listTagDifferenceBetween(String tag, String s, String e, String equipmentCode) {
        JSONObject result = new JSONObject();
//        BaseDict baseDict = new BaseDict();
//        baseDict.setBaseType("SHIFT_TIME");
//        List<BaseDict> shiftTimes = baseDictService.selectBaseDictList(baseDict);
//        String day = "08:00";
//        for (BaseDict temp : shiftTimes) {
//            if (temp.getBaseCode().equalsIgnoreCase("SHIFT_DAY")) {
//                day = temp.getBaseDesc();
//            }
//        }
        BaseDict baseDict = new BaseDict();
        baseDict.setBaseType("SHIFT_TIME");
        List<BaseDict> shiftTimes = baseDictService.selectBaseDictList(baseDict);
        String day = "08:00";
        String night = "20:00";
        for (BaseDict temp : shiftTimes) {
            if (temp.getBaseCode().equalsIgnoreCase("SHIFT_DAY")) {
                day = temp.getBaseDesc();
            } else if (temp.getBaseCode().equalsIgnoreCase("SHIFT_NIGHT")) {
                night = temp.getBaseDesc();
            }
        }
        long daytime = DateUtil.between(DateUtil.parseDateTime("2022-01-01 " + day + ":00"),
                DateUtil.parseDateTime("2022-01-01 " + night + ":00"), DateUnit.HOUR, true);
//        夜班时长
        long nighttime = 24 - daytime;
        Long between = DateUtil.between(DateUtil.parse(s), DateUtil.parse(e), DateUnit.DAY);
        IotEquipment iotEquipment = new IotEquipment();
        List<IotEquipment> list = iotEquipmentService.selectIotEquipmentList(iotEquipment);
        if (list != null && list.size() > 0) {
        } else {
            throw new CustomException("暂无可用设备");
        }
        if (StrUtil.isEmpty(equipmentCode)) {
            equipmentCode = "**";
        }
        for (int j = 0; j <= between; j++) {
            String rq = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(s), j));
//            String start = rq + " " + day + ":00";
//            String end = DateUtil.formatDateTime(DateUtil.offsetDay(DateUtil.parseDateTime(start), 1));
//        获取所有设备清单
            for (IotEquipment temp : list) {
                result.put(temp.getEquipmentCode() + rq, new JSONObject());
                result.getJSONObject(temp.getEquipmentCode() + rq).put("name", temp.getEquipmentName());
                result.getJSONObject(temp.getEquipmentCode() + rq).put("sort", temp.getEquipmentSort());
                result.getJSONObject(temp.getEquipmentCode() + rq).put("multiple", 1);
                List<IotTslAttr> attrList = temp.getAttrList();
                if (attrList != null && attrList.size() > 0) {
                    for (IotTslAttr attr : attrList) {
                        if (attr.getAttrCode().equalsIgnoreCase(tag)) {
                            result.getJSONObject(temp.getEquipmentCode() + rq).put("multiple", attr.getAttrMultiple());
                        }
                    }
                }
            }

//            计算白班
            String dayStart = rq + " " + day + ":00";
            String nightStart = rq +" " + night + ":00";
            String dayEnd = DateUtil.formatDate(DateUtil.offsetDay(DateUtil.parseDate(rq), 1)) + " " + day + ":00";
            JSONArray array = ioTDBUtil.getMaxAndMinByShift(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), equipmentCode, tag, dayStart, dayEnd, daytime + "h");
            array.addAll(ioTDBUtil.getMaxAndMinByShift(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), equipmentCode, tag, nightStart, dayEnd, nighttime + "h"));
//            JSONArray array = getMaxminService(equipmentCode, tag, SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), start, end, false);
            for (int i = 0; i < array.size(); i++) {
                array.getJSONObject(i).put("Device", array.getJSONObject(i).getString("Device").split("\\.")[2]);
                JSONObject temp = array.getJSONObject(i);
                if (temp.getString("max").equalsIgnoreCase("null")) {
                    temp.put("max", "0");
                }
                if (temp.getString("min").equalsIgnoreCase("null")) {
                    temp.put("min", "0");
                }
                if (result.containsKey(temp.getString("Device") + rq)) {

                    result.getJSONObject(temp.getString("Device") + rq).put("val",  NumberUtil.round((Convert.toDouble(temp.getString("max")) - Convert.toDouble(temp.getString("min"))) * result.getJSONObject(temp.getString("Device") + rq).getDoubleValue("multiple"), 2).add(Convert.toBigDecimal(result.getJSONObject(temp.getString("Device") + rq).getDoubleValue("val"))));
                    result.getJSONObject(temp.getString("Device") + rq).put("rq", rq);
                }
            }
        }
        return AjaxResult.success(result);
    }

    @GetMapping("/listTagDifferenceDeviceMonth")
    public AjaxResult listTagDifferenceDeviceMonth(String tag, String rq, String equipmentCode) {
        JSONObject result = new JSONObject();
        BaseDict baseDict = new BaseDict();
        baseDict.setBaseType("SHIFT_TIME");
        List<BaseDict> shiftTimes = baseDictService.selectBaseDictList(baseDict);
        String day = "08:00";
        for (BaseDict temp : shiftTimes) {
            if (temp.getBaseCode().equalsIgnoreCase("SHIFT_DAY")) {
                day = temp.getBaseDesc();
            }
        }
        rq = rq.substring(0, 7);
        String start = rq + "-01 " + day + ":00";
        String end = DateUtil.formatDateTime(DateUtil.offsetMonth(DateUtil.parseDateTime(start), 1));
//        获取所有设备清单
        IotEquipment iotEquipment = new IotEquipment();
        List<IotEquipment> list = iotEquipmentService.selectIotEquipmentList(iotEquipment);
        if (list != null && list.size() > 0) {
        } else {
            throw new CustomException("暂无可用设备");
        }
        if (StrUtil.isEmpty(equipmentCode)) {
            equipmentCode = "**";
        }
        for (IotEquipment temp : list) {
            result.put(temp.getEquipmentCode(), new JSONObject());
            result.getJSONObject(temp.getEquipmentCode()).put("name", temp.getEquipmentName());
            result.getJSONObject(temp.getEquipmentCode()).put("sort", temp.getEquipmentSort());
            result.getJSONObject(temp.getEquipmentCode()).put("multiple", 1);
            List<IotTslAttr> attrList = temp.getAttrList();
            if (attrList != null && attrList.size() > 0) {
                for (IotTslAttr attr : attrList) {
                    if (attr.getAttrCode().equalsIgnoreCase(tag)) {
                        result.getJSONObject(temp.getEquipmentCode()).put("multiple", attr.getAttrMultiple());
                    }
                }
            }
        }
        JSONArray array = getMaxminService(equipmentCode, tag, SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), start, end, false);
        for (int i = 0; i < array.size(); i++) {
            JSONObject temp = array.getJSONObject(i);
            if (temp.getString("max").equalsIgnoreCase("null")) {
                temp.put("max", "0");
            }
            if (temp.getString("min").equalsIgnoreCase("null")) {
                temp.put("min", "0");
            }
            if (result.containsKey(temp.getString("Device"))) {
                result.getJSONObject(temp.getString("Device")).put("val", NumberUtil.round((Convert.toDouble(temp.getString("max")) - Convert.toDouble(temp.getString("min"))) * result.getJSONObject(temp.getString("Device")).getDoubleValue("multiple"), 2));
            }
        }
        return AjaxResult.success(result);
    }

    public JSONArray getStatusTimeService(String deviceStr, String tag, String tenant, String start, String end, boolean sum) {
        try {
            JSONArray result = new JSONArray();
            JSONObject object = new JSONObject();
//            object.put("0", 0);
//            object.put("1", 0);
//            object.put("times", 0);
            List<HistoryData> onlyList = new ArrayList<>();
            List<HistoryData> list = ioTDBUtil.getDataListFillWithZero(tenant, deviceStr, tag, start, end);
            for (HistoryData data : list) {
                data.setDevice(data.getDevice().split("\\.")[2]);
            }
            if (list != null && list.size() > 0) {
                HistoryData temp = list.get(0);
                onlyList.add(temp);
                for (int i = 1; i < list.size(); i++) {
//                    if(list.get(i).getDevice().equals("MD12049_1")){
//                        System.out.println(JSONObject.toJSONString(list.get(i)));
//                    }
                    HistoryData obj = list.get(i);
                    if (!obj.getDevice().equalsIgnoreCase(temp.getDevice())) {
                        onlyList.add(list.get(i - 1));
                        object.put(temp.getDevice(), onlyList);
                        onlyList = new ArrayList<>();
                        temp = obj;
                        onlyList.add(obj);
                    }
//                    if(list.get(i).getDevice().equals("MD12049_1")){
//                        System.out.println(onlyList);
//                    }
                    if (NumberUtil.compare(Convert.toDouble(obj.getVal()), Convert.toDouble(temp.getVal())) == 0) {
                    } else {
//                        if (Convert.toStr(Convert.toInt(Convert.toDouble(obj.getVal()))).equalsIgnoreCase("0")) {
//                            object.put("times", object.getIntValue("times") + 1);
//                        }
                        temp = obj;
                        onlyList.add(temp);
                    }
                }
                onlyList.add(list.get(list.size() - 1));
                object.put(temp.getDevice(), onlyList);
//                onlyList.add(list.get(list.size() - 1));
            }

//                System.out.println(JSONObject.toJSONString(onlyList));
            if (object != null) {
                for (String device : object.keySet()) {
                    JSONObject json = new JSONObject();
                    json.put("key", device);
                    json.put("0", 0);
                    json.put("1", 0);
                    json.put("times", 0);
                    List<HistoryData> array = object.getObject(device, List.class);
                    if (array != null && array.size() > 1) {
                        HistoryData temp = array.get(0);
                        for (int i = 1; i < array.size(); i++) {
                            HistoryData obj = array.get(i);
                            double times = 0d;
                            String key = Convert.toStr(Convert.toInt(Convert.toDouble(temp.getVal())));
                            if (json.containsKey(key)) {
                                times = json.getDoubleValue(key);
                            }
                            if (Convert.toInt(Convert.toDouble(temp.getVal())) == 0) {
                                json.put("times", (json.getIntValue("times") + 1));
                            }
                            times = times + DateUtil.between(DateUtil.parseDateTime(temp.getTime()), DateUtil.parseDateTime(obj.getTime()), DateUnit.MINUTE);
                            temp = obj;
                            json.put(key, times);
                        }
                    }
                    result.add(json);
                }
            }
//            if (onlyList != null && onlyList.size() > 1) {
//                HistoryData temp = onlyList.get(0);
//                for (int i = 1; i < onlyList.size(); i++) {
//                    HistoryData obj = onlyList.get(i);
//                    double times = 0d;
//                    String key = Convert.toStr(Convert.toInt(Convert.toDouble(temp.getVal())));
//                    if (!sum) {
//                        key = Convert.toStr(Convert.toInt(Convert.toDouble(temp.getVal())));
//                    }
//                    if (object.containsKey(key)) {
//                        times = object.getDoubleValue(key);
//                    }
//                    times = times + DateUtil.between(DateUtil.parseDateTime(temp.getTime()), DateUtil.parseDateTime(obj.getTime()), DateUnit.MINUTE);
//                    temp = obj;
//                    object.put(key, times);
//                }
//            }

            return result;
        } catch (IoTDBConnectionException ex) {
            ex.printStackTrace();
        } catch (StatementExecutionException ex) {
            ex.printStackTrace();
        }
        return null;
    }

    private List<HistoryData> getStatusTime(String deviceStr, String tag, String tenant, String start, String end) {
        try {
            JSONObject object = new JSONObject();

            List<HistoryData> onlyList = new ArrayList<>();
            List<HistoryData> list = ioTDBUtil.getDataListFillWithZero(tenant, deviceStr, tag, start, end);
            if (list != null && list.size() > 0) {
                HistoryData temp = list.get(0);
                onlyList.add(temp);
                for (HistoryData obj : list) {
                    if (Convert.toStr(obj.getVal()).equals(temp.getVal())) {
                    } else {
                        if (Convert.toStr(Convert.toInt(Convert.toDouble(obj.getVal()))).equalsIgnoreCase("0")) {
                            object.put("times", object.getIntValue("times") + 1);
                        }
                        temp = obj;
                        onlyList.add(temp);
                    }
                }
                onlyList.add(list.get(list.size() - 1));
            }
            return onlyList;
        } catch (IoTDBConnectionException ex) {
            ex.printStackTrace();
        } catch (StatementExecutionException ex) {
            ex.printStackTrace();
        }
        return null;
    }

    private JSONObject getMaxminHourService(String deviceStr, String tag, String tenant, String start, String end, boolean sum) {
        IotEquipment iotEquipment = new IotEquipment();
        List<IotEquipment> list = iotEquipmentService.selectIotEquipmentList(iotEquipment);
        if (list != null && list.size() > 0) {
        } else {
            throw new CustomException("暂无可用设备");
        }
        JSONObject multipleList = new JSONObject();
        for (IotEquipment temp : list) {
//            multipleList.getJSONObject(temp.getEquipmentCode()).put("multiple", 1);
            List<IotTslAttr> attrList = temp.getAttrList();
            if (attrList != null && attrList.size() > 0) {
                for (IotTslAttr attr : attrList) {
                    if (attr.getAttrCode().equalsIgnoreCase(tag)) {
                        multipleList.put(temp.getEquipmentCode(), attr.getAttrMultiple());
//                        multipleList.getJSONObject(temp.getEquipmentCode()).put("multiple", attr.getAttrMultiple());
                    }
                }
            }
        }

        JSONObject result = new JSONObject();
        JSONArray array = ioTDBUtil.getAllMaxAndMinByHour(tenant, deviceStr, tag, start, end);
        for (int j = 0; j < array.size(); j++) {
            String key = "devices[i]" + "_number";
            if (sum) {
                key = "number";
            }
            JSONObject temp = array.getJSONObject(j);
            JSONObject detail = new JSONObject(true);
            if (result.containsKey(key)) {
                detail = result.getJSONObject(key);
            }
            double number = 0;
            if (detail.containsKey(array.getJSONObject(j).getString("time"))) {
                number = detail.getDoubleValue(temp.getString("time"));
            }
            if (StrUtil.isEmpty(temp.getString("min")) || temp.getString("min").equals("null")) {
                temp.put("min", 0);
            }
            if (StrUtil.isEmpty(temp.getString("max")) || temp.getString("max").equals("null")) {
                temp.put("max", 0);
            }
            number = number + (Convert.toDouble(temp.getString("max")) - Convert.toDouble(temp.getString("min"))) * multipleList.getDoubleValue(temp.getString("Device").split("\\.")[2]);
            detail.put(array.getJSONObject(j).getString("time"), NumberUtil.round(number, 1));
            result.put(key, detail);
        }
        return result;
    }

    private JSONArray getMaxminService(String deviceStr, String tag, String tenant, String start, String end, boolean sum) {
        JSONArray array = ioTDBUtil.getMaxAndMin(tenant, deviceStr, tag, start, end);
        for (int i = 0; i < array.size(); i++) {
            array.getJSONObject(i).put("Device", array.getJSONObject(i).getString("Device").split("\\.")[2]);
        }
        return array;
    }
}
