package com.boyo.web.controller.eam;

import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.eam.domain.ObjectInfoProperty;
import com.boyo.eam.service.IObjectInfoPropertyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 物模型属性表(ObjectInfoProperty)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
@Api("物模型属性表")
@RestController
@RequestMapping("/equip/objectInfoProperty")
@AllArgsConstructor
public class ObjectInfoPropertyController extends BaseController{
    /**
     * 服务对象
     */
    private final IObjectInfoPropertyService objectInfoPropertyService;

    /**
     * 查询物模型属性表列表
     *
     */
    @ApiOperation("查询物模型属性表列表")
    @GetMapping("/list")
    public TableDataInfo list(ObjectInfoProperty objectInfoProperty) {
        startPage();
        List<ObjectInfoProperty> list = objectInfoPropertyService.selectObjectInfoPropertyList(objectInfoProperty);
        return getDataTable(list);
    }
    
    /**
     * 获取物模型属性表详情
     */
    @ApiOperation("获取物模型属性表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(objectInfoPropertyService.getById(id));
    }

    /**
     * 新增物模型属性表
     */
    @ApiOperation("新增物模型属性表")
    @PostMapping
    public AjaxResult add(@RequestBody ObjectInfoProperty objectInfoProperty) {
        objectInfoProperty.setOpenid(super.generateOpenid());
        return toBooleanAjax(objectInfoPropertyService.save(objectInfoProperty));
    }

    /**
     * 修改物模型属性表
     */
    @ApiOperation("修改物模型属性表")
    @PutMapping
    public AjaxResult edit(@RequestBody ObjectInfoProperty objectInfoProperty) {
        return toBooleanAjax(objectInfoPropertyService.updateById(objectInfoProperty));
    }

    /**
     * 删除物模型属性表
     */
    @ApiOperation("删除物模型属性表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(objectInfoPropertyService.removeByIds(Arrays.asList(ids)));
    }

}
