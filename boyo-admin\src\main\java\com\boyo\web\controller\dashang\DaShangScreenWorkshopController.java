package com.boyo.web.controller.dashang;

import com.boyo.common.annotation.Log;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.enums.BusinessType;
import com.boyo.master.domain.ScreenWorkshop;
import com.boyo.master.domain.ScreenYield;
import com.boyo.master.service.IScreenWorkshopService;
import com.boyo.master.service.IScreenYieldService;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 大屏车间Controller
 *
 * <AUTHOR>
 * @date 2024-09-09
 */
@Api("车间数据")
@RestController
@RequestMapping("/dashang/workshop")
@AllArgsConstructor
public class DaShangScreenWorkshopController extends BaseController {
    @Autowired
    private IScreenWorkshopService screenWorkshopService;

    @Autowired
    private IScreenYieldService screenYieldService;

    /**
     * 查询大屏车间列表
     */
//    @PreAuthorize("@ss.hasPermi('system:workshop:list')")
    @GetMapping("/list")
    public TableDataInfo list(ScreenWorkshop screenWorkshop)
    {
        startPage();
        List<ScreenWorkshop> list = screenWorkshopService.selectScreenWorkshopList(screenWorkshop);
        return getDataTable(list);
    }

    /**
     * 导出大屏车间列表
     */
//    @PreAuthorize("@ss.hasPermi('system:workshop:export')")
//    @Log(title = "大屏车间", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, ScreenWorkshop screenWorkshop)
//    {
//        List<ScreenWorkshop> list = screenWorkshopService.selectScreenWorkshopList(screenWorkshop);
//        ExcelUtil<ScreenWorkshop> util = new ExcelUtil<ScreenWorkshop>(ScreenWorkshop.class);
//        util.exportExcel(response, list, "大屏车间数据");
//    }

    /**
     * 获取大屏车间详细信息
     */
//    @PreAuthorize("@ss.hasPermi('system:workshop:query')")
    @GetMapping(value = "/{workshopId}")
    public AjaxResult getInfo(@PathVariable("workshopId") Long workshopId) {
        return AjaxResult.success(screenWorkshopService.selectScreenWorkshopByWorkshopId(workshopId));
    }

    /**
     * 新增大屏车间
     */
//    @PreAuthorize("@ss.hasPermi('system:workshop:add')")
    @Log(title = "大屏车间", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ScreenWorkshop screenWorkshop) {
        return toAjax(screenWorkshopService.insertScreenWorkshop(screenWorkshop));
    }

    /**
     * 修改大屏车间
     */
//    @PreAuthorize("@ss.hasPermi('system:workshop:edit')")
    @Log(title = "大屏车间", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ScreenWorkshop screenWorkshop) {
        return toAjax(screenWorkshopService.updateScreenWorkshop(screenWorkshop));
    }

    /**
     * 删除大屏车间
     */
//    @PreAuthorize("@ss.hasPermi('system:workshop:remove')")
    @Log(title = "大屏车间", businessType = BusinessType.DELETE)
    @DeleteMapping("/{workshopIds}")
    public AjaxResult remove(@PathVariable Long[] workshopIds) {

        final int i = screenYieldService.deleteScreenYieldByWorkshopId(workshopIds[0]);
        if (i > 0) {
            final int i1 = screenWorkshopService.deleteScreenWorkshopByWorkshopIds(workshopIds);
            return toAjax(i1);
        } else {
            return toAjax(-1);
        }
    }
}
