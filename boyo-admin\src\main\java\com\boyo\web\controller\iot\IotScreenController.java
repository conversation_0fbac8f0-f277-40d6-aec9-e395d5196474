package com.boyo.web.controller.iot;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.redis.RedisCache;
import com.boyo.common.core.text.Convert;
import com.boyo.common.exception.CustomException;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.common.utils.StringUtils;
import com.boyo.iot.domain.HistoryData;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.domain.IotEquipmentProp;
import com.boyo.iot.domain.IotTslAttr;
import com.boyo.iot.entity.IorealData;
import com.boyo.iot.service.IIofaultService;
import com.boyo.iot.service.IIorealDataService;
import com.boyo.iot.service.IIotEquipmentPropService;
import com.boyo.iot.service.IIotEquipmentService;
import com.boyo.iot.util.IoTDBUtil;
import com.boyo.iot.vo.IoTAttrVO;
import com.boyo.master.domain.BaseDict;
import com.boyo.master.service.IBaseDictService;
import com.boyo.mes.entity.MesModulRecord;
import com.boyo.mes.mapper.MesModulRecordMapper;
import com.boyo.mes.mapper.MesMouldMapper;
import com.boyo.mes.service.IMesModulRecordService;
import com.github.pagehelper.PageHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.val;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.rpc.StatementExecutionException;
import org.aspectj.weaver.loadtime.Aj;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;

@Api("iot可视化看板接口")
@RestController
@RequestMapping("/iot/screen")
@AllArgsConstructor
public class IotScreenController {
    private final IIorealDataService realDataService;
    private final IoTDBUtil ioTDBUtil;
    private final IIofaultService iofaultService;
    private final IIotEquipmentService equipmentService;
    private final IBaseDictService baseDictService;
    private final IIotEquipmentService iotEquipmentService;
    private final IMesModulRecordService mesModulRecordService;
    private final IIotEquipmentPropService iotEquipmentPropService;
    private final RedisCache redisCache;

    @ApiOperation("查询当前最新数据")
    @GetMapping("/getCurrent")
    public AjaxResult getCurrent() {
        return AjaxResult.success(realDataService.selectIorealDataList(null));
    }


    @ApiOperation("查询设备历史数据")
    @GetMapping("/getDataList")
    public AjaxResult getDataList(String device, String tag, String tenant) {

        try {
            ioTDBUtil.listData(tenant, device, tag, DateUtil.formatDateTime(DateUtil.offsetMinute(new Date(), -120)), DateUtil.formatDateTime(new Date()));
        } catch (IoTDBConnectionException e) {
            e.printStackTrace();
        } catch (StatementExecutionException e) {
            e.printStackTrace();
        }
        return AjaxResult.error();
    }

    /**
     * 查询当前最新数据
     * <p>
     * 该接口用于获取系统中最新的数据列表，主要用于前端页面加载或数据刷新时获取最新信息
     *
     * @param iorealData 查询条件对象，用于指定查询的具体条件
     *                   deviceCode 设备编码，用于标识特定的设备
     *                   tag        数据标签，用于标识特定的数据类型或属性
     * @return 返回一个包含最新数据列表的AjaxResult对象，用于前端展示
     */
    @ApiOperation("查询当前最新数据")
    @GetMapping("/getCurrent/real")
    public AjaxResult getCurrent(IorealData iorealData) {

        final List<IorealData> dataList = realDataService.selectIorealDataList(iorealData);
        return AjaxResult.success(dataList);
    }


    /**
     * 查询设备历史数据
     *
     * @param tenant     租户名称，用于区分不同的客户或项目
     * @param deviceCode 设备编码，标识特定的设备
     * @param tag        数据标签，指定所需数据的类型或名称
     * @param start      开始时间，数据查询的起始时间点
     * @param end        结束时间，数据查询的结束时间点
     * @return 返回包含历史数据的AjaxResult对象
     */
    @ApiOperation("查询设备历史数据")
    @GetMapping("/getDataList/history")
    public AjaxResult getDataListHistory(String tenant, String deviceCode, String tag, String start, String end) {
        // 如果未指定开始或结束时间，则默认查询最近120分钟的数据
        if (StrUtil.isBlank(start) && StrUtil.isBlank(end)) {
            start = DateUtil.formatDateTime(DateUtil.offsetMinute(new Date(), -120));//默认查询最近120分钟数据
            end = DateUtil.formatDateTime(new Date());
        }

        try {
            // 调用IoTDBUtil类的listData方法查询设备历史数据
            return AjaxResult.success(ioTDBUtil.listData(tenant, deviceCode, tag, start, end));
        } catch (IoTDBConnectionException e) {
            // 捕获IoTDB连接异常并打印堆栈跟踪
            e.printStackTrace();
        } catch (StatementExecutionException e) {
            // 捕获语句执行异常并打印堆栈跟踪
            e.printStackTrace();
        }
        // 如果查询过程中发生异常，则返回错误结果
        return AjaxResult.error();
    }


    @ApiOperation("查询报警信息")
    @GetMapping("/listIofault")
    public AjaxResult listIofault() {
        PageHelper.startPage(1, 20);
        return AjaxResult.success(iofaultService.selectIofaultList(null));
    }

    /**
     * 英联OEE信息
     *
     * @return
     */
    @GetMapping("/listYlOEE")
    public AjaxResult listYlOEE() {
        Integer current = DateUtil.hour(new Date(), true);
        String first = "";
        String second = "";
        Date currentTime = new Date();
        if (current >= 8 && current < 19) {
            first = DateUtil.formatDate(currentTime) + " 07:59:00";
            second = DateUtil.formatDate(DateUtil.offsetDay(currentTime, -1)) + " 18:59:00";
        } else {
            if (current > 8) {
                first = DateUtil.formatDate(currentTime) + " 18:59:00";
                second = DateUtil.formatDate(currentTime) + " 07:59:00";
            } else {
                first = DateUtil.formatDate(DateUtil.offsetDay(currentTime, -1)) + " 18:59:00";
                second = DateUtil.formatDate(DateUtil.offsetDay(currentTime, -1)) + " 07:59:00";
            }
        }
//
        JSONObject result = new JSONObject();
        result.put("first", ioTDBUtil.getRealOEE("4bd8e784c72b47d8bc90faaa6b9364a8", "", first));
        result.put("second", ioTDBUtil.getRealOEE("4bd8e784c72b47d8bc90faaa6b9364a8", "", second));
        return AjaxResult.success(result);
    }

    @ApiOperation("查询设备历史数据-不补齐数据")
    @GetMapping("/getDataListWithoutFill")
    public AjaxResult getDataListWithoutFill(String device, String tag, String tenant) {
        try {
            return AjaxResult.success(ioTDBUtil.getDataListWithoutFill(tenant, device, tag, DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -2)), DateUtil.formatDateTime(new Date())));
        } catch (IoTDBConnectionException ex) {
            ex.printStackTrace();
        } catch (StatementExecutionException ex) {
            ex.printStackTrace();
        }
        return AjaxResult.error();
    }

    /**
     * 获取每天每个状态的持续时间
     */
    @GetMapping("/getStatusTime")
    public AjaxResult getStatusTime(String device, String tag, String tenant, String rq, boolean sum) {
        if (StrUtil.isEmpty(rq)) {
            rq = DateUtil.formatDate(new Date());
        }
        return AjaxResult.success(getStatusTimeService(device, tag, tenant, rq, sum));
    }

    @GetMapping("/getStatusTimeWeek")
    public AjaxResult getStatusTimeWeek(String device, String tag, String tenant, boolean sum) {
        JSONObject result = new JSONObject();
        for (int i = 6; i > -1; i--) {
            String rq = DateUtil.formatDate(DateUtil.offsetDay(new Date(), -i));
            result.put(rq, getStatusTimeService(device, tag, tenant, rq, sum));
        }
        return AjaxResult.success(result);
    }

    @GetMapping("/getMaxminHour")
    public AjaxResult getMaxminHour(String device, String tag, String tenant, String rq, boolean sum) {
        if (StrUtil.isEmpty(rq)) {
            rq = DateUtil.formatDate(new Date());
        }
        return AjaxResult.success(getMaxminHourService(device, tag, tenant, rq, sum));
    }

    @GetMapping("/getMaxminDay")
    public AjaxResult getMaxminDay(String device, String tag, String tenant, boolean sum) {
        return AjaxResult.success(getMaxminDayService(device, tag, tenant, sum));
    }

    @GetMapping("/listTagDifferenceDevice")
    public AjaxResult listTagDifferenceDevice(String tag, String rq, String tenant) {
        JSONArray result = new JSONArray();
        String day = "08:00";
        if (StrUtil.isEmpty(rq)) {
            rq = DateUtil.formatDate(new Date());
        }
        String start = rq + " " + day + ":00";
        String end = DateUtil.formatDateTime(DateUtil.offsetDay(DateUtil.parseDateTime(start), 1));
//        获取所有设备清单
        String equipmentCode = "**";
        JSONArray array = getMaxminService(equipmentCode, tag, tenant, start, end, false);
        for (int i = 0; i < array.size(); i++) {
            JSONObject temp = array.getJSONObject(i);
            if (temp.getString("max").equalsIgnoreCase("null")) {
                temp.put("max", "0");
            }
            if (temp.getString("min").equalsIgnoreCase("null")) {
                temp.put("min", "0");
            }
            JSONObject object = new JSONObject();
            object.put("device", temp.getString("Device"));
            object.put("val", NumberUtil.round((Convert.toDouble(temp.getString("max")) - Convert.toDouble(temp.getString("min"))), 0));
            result.add(object);
            //            result.getJSONObject(temp.getString("Device")).put("val", NumberUtil.round((Convert.toDouble(temp.getString("max")) - Convert.toDouble(temp.getString("min"))) , 0));
        }
        return AjaxResult.success(result);
    }

    private JSONArray getMaxminService(String deviceStr, String tag, String tenant, String start, String end, boolean sum) {
        JSONArray array = ioTDBUtil.getMaxAndMin(tenant, deviceStr, tag, start, end);
        for (int i = 0; i < array.size(); i++) {
            array.getJSONObject(i).put("Device", array.getJSONObject(i).getString("Device").split("\\.")[2]);
        }
        return array;
    }
//
//    private JSONArray getFirstLastService(String deviceStr, String tag, String tenant, String start, String end, boolean sum) {
//        JSONArray array = ioTDBUtil.getFirstAndLast(tenant, deviceStr, tag, start, end);
//        for (int i = 0; i < array.size(); i++) {
//            array.getJSONObject(i).put("Device", array.getJSONObject(i).getString("Device").split("\\.")[2]);
//        }
//        return array;
//    }

    public JSONObject getMaxminHourService(String deviceStr, String tag, String tenant, String rq, boolean sum) {
        JSONObject result = new JSONObject();
        String[] devices = deviceStr.split(";");
        for (int i = 0; i < devices.length; i++) {
            JSONArray array = ioTDBUtil.getMaxAndMinByHour(tenant, devices[i], tag, rq + " 00:00:00", rq + " 23:59:59");
            for (int j = 0; j < array.size(); j++) {
                String key = devices[i] + "_number";
                if (sum) {
                    key = "number";
                }
                JSONObject temp = array.getJSONObject(j);
                JSONObject detail = new JSONObject(true);
                if (result.containsKey(key)) {
                    detail = result.getJSONObject(key);
                }
                double number = 0;
                if (detail.containsKey(array.getJSONObject(j).getString("time"))) {
                    number = detail.getDoubleValue(temp.getString("time"));
                }
                if (StrUtil.isEmpty(temp.getString("min")) || temp.getString("min").equals("null")) {
                    temp.put("min", 0);
                }
                if (StrUtil.isEmpty(temp.getString("max")) || temp.getString("max").equals("null")) {
                    temp.put("max", 0);
                }
                number = number + Convert.toDouble(temp.getString("max")) - Convert.toDouble(temp.getString("min"));
                detail.put(array.getJSONObject(j).getString("time"), number);
                result.put(key, detail);
            }
        }
        return result;
    }

    public JSONObject getMaxminDayService(String deviceStr, String tag, String tenant, boolean sum) {
        JSONObject result = new JSONObject();
        String[] devices = deviceStr.split(";");
        String start = DateUtil.format(new Date(), "yyyy-MM");
        String end = DateUtil.format(DateUtil.offsetMonth(new Date(), 1), "yyyy-MM");
        for (int i = 0; i < devices.length; i++) {
            JSONArray array = ioTDBUtil.getMaxAndMinByDay(tenant, devices[i], tag, start + "-01 00:00:00", end + "-01 00:00:00");
            for (int j = 0; j < array.size(); j++) {
                String key = devices[i] + "_number";
                if (sum) {
                    key = "number";
                }
                JSONObject temp = array.getJSONObject(j);
                JSONObject detail = new JSONObject(true);
                if (result.containsKey(key)) {
                    detail = result.getJSONObject(key);
                }
                double number = 0;
                if (detail.containsKey(array.getJSONObject(j).getString("time"))) {
                    number = detail.getDoubleValue(temp.getString("time"));
                }
                if (StrUtil.isEmpty(temp.getString("min")) || temp.getString("min").equals("null")) {
                    temp.put("min", 0);
                }
                if (StrUtil.isEmpty(temp.getString("max")) || temp.getString("max").equals("null")) {
                    temp.put("max", 0);
                }
                number = number + Convert.toDouble(temp.getString("max")) - Convert.toDouble(temp.getString("min"));
                detail.put(array.getJSONObject(j).getString("time"), number);
                result.put(key, detail);
            }

        }
        return result;
    }

    public JSONObject getStatusTimeService(String deviceStr, String tag, String tenant, String rq, boolean sum) {
        try {
            JSONObject object = new JSONObject();
            String[] devices = deviceStr.split(";");
            for (int j = 0; j < devices.length; j++) {
                String device = devices[j];
                List<HistoryData> onlyList = new ArrayList<>();
                List<HistoryData> list = ioTDBUtil.getDataListWithoutFill(tenant, device, tag, rq + " 00:00:00", rq + " 23:59:59");
                if (list != null && list.size() > 0) {
                    HistoryData temp = list.get(0);
                    onlyList.add(temp);
                    for (HistoryData obj : list) {
                        if (Convert.toStr(obj.getVal()).equals(temp.getVal())) {
                            continue;
                        } else {
                            temp = obj;
                            onlyList.add(temp);
                        }
                    }
                    onlyList.add(list.get(list.size() - 1));
                }
                if (onlyList != null && onlyList.size() > 1) {
                    HistoryData temp = onlyList.get(0);
                    for (int i = 1; i < onlyList.size(); i++) {
                        HistoryData obj = onlyList.get(i);
                        double times = 0d;
                        String key = Convert.toStr(temp.getVal());
                        if (!sum) {
                            key = device + "_" + Convert.toStr(temp.getVal());
                        }
                        if (object.containsKey(key)) {
                            times = object.getDoubleValue(key);
                        }
                        times = times + DateUtil.between(DateUtil.parseDateTime(temp.getTime()), DateUtil.parseDateTime(obj.getTime()), DateUnit.MINUTE);
                        temp = obj;
                        object.put(key, times);
                    }
                }
            }

            return object;
        } catch (IoTDBConnectionException ex) {
            ex.printStackTrace();
        } catch (StatementExecutionException ex) {
            ex.printStackTrace();
        }
        return null;
    }

    @ApiOperation("获取物联网设备管理详细信息")
    @GetMapping(value = "/getDataHistoryV2")
    public AjaxResult getDataHistoryV2(String tenant, String device, String start, String end) {
        Date s, e;
        if (StrUtil.isEmpty(start)) {
            s = DateUtil.offset(new Date(), DateField.HOUR_OF_DAY, -1);
        } else {
            s = DateUtil.parse(start);
        }
        if (StrUtil.isEmpty(end)) {
            e = new Date();
        } else {
            e = DateUtil.parse(end);
        }
        try {
            JSONObject result = new JSONObject();
//            if(list != null && list.size() > 0){
//                IotTslAttr attr = new IotTslAttr();
            IotEquipment equipment = equipmentService.getEquipmentByCode(device);
            if (equipment != null) {
                List<IotTslAttr> attrs = equipmentService.getEquipmentDetail(Convert.toInt(equipment.getId())).getAttrList();
                result.put("attrs", attrs);
                List<String> codes = new ArrayList<>();
                for (IotTslAttr attr : attrs) {
                    codes.add(attr.getAttrCode());
                }
                JSONArray list = ioTDBUtil.listDataHistoryV2(tenant, device, codes, DateUtil.formatDateTime(s), DateUtil.formatDateTime(e));

                for (IotTslAttr attr : attrs) {
                    if (attr != null && (attr.getAttrType().equalsIgnoreCase("double") || attr.getAttrType().equalsIgnoreCase("integer"))) {
                        for (int i = 0; i < list.size(); i++) {
                            JSONObject temp = list.getJSONObject(i);
                            for (String tag : temp.keySet()) {
                                if (attr.getAttrCode().equalsIgnoreCase(tag)) {
                                    try {
                                        temp.put(tag, NumberUtil.round(Convert.toDouble(temp.getString(tag)) * attr.getAttrMultiple(), 2));
                                        list.set(i, temp);
                                        break;
                                    } catch (Exception e1) {

                                    }

                                }
                            }
                        }
//                            for (JSONObject data : list) {
//                                if (ObjectUtil.isNotNull(data.getVal()) && NumberUtil.isNumber(Convert.toStr(data.getVal()))) {
//                                    data.setVal(NumberUtil.round(Convert.toDouble(data.getVal()) * attr.getAttrMultiple(), 2));
//                                }
//                            }
                    }
                }
                result.put("list", list);
            }
            return AjaxResult.success(result);
        } catch (IoTDBConnectionException ex) {
            ex.printStackTrace();
        } catch (StatementExecutionException ex) {
            ex.printStackTrace();
        }
        return AjaxResult.error();
    }

    public static Date getMonthStart() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH, 0);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        Date time = cal.getTime();
        return time;
    }

    @ApiOperation("查询设备历史报警信息")
    @GetMapping("/getDataList2")
    public AjaxResult getDataList2(String device, String tenant) throws Exception {
        String tag = "*";
        List<IotWarn> warnList = new ArrayList<>();
        String[] deviceStr = device.split(";");//分割获取设备
        for (int i = 0; i < deviceStr.length; i++) {
            if (deviceStr[i].equals("802957_1") || deviceStr[i].equals("802956_1") || deviceStr[i].equals("802954_1")
                    || deviceStr[i].equals("802958_1") || deviceStr[i].equals("802960_1") || deviceStr[i].equals("802961_1")
                    || deviceStr[i].equals("803083_1") || deviceStr[i].equals("M10495_1")) {//判断设备类型
                JSONArray jsonArray = null;
                try {
                    jsonArray = ioTDBUtil.listDataHistory(tenant, deviceStr[i], tag, DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -1)), DateUtil.formatDateTime(new Date()));
                } catch (IoTDBConnectionException e) {
                    e.printStackTrace();
                } catch (StatementExecutionException e) {
                    e.printStackTrace();
                }
                for (int j = 0; j < jsonArray.size(); j++) {
                    IotWarn warn = new IotWarn();
                    JSONObject jsonObject = jsonArray.getJSONObject(j);
                    String time = jsonObject.getString("time");
                    if (jsonObject.getString("M210)").contains("1.0")) {
                        warn.setTime(time);
                        warn.setWarn1("导轨润滑异常");
                        warn.setDevice(deviceStr[i]);
                        warnList.add(warn);
                    }
                    if (jsonObject.getString("M249)").contains("1.0")) {
                        warn.setTime(time);
                        warn.setWarn1("辅助定位信号异常");
                        warn.setDevice(deviceStr[i]);
                        warnList.add(warn);
                    }
                    if (jsonObject.getString("M253)").contains("1.0")) {
                        warn.setTime(time);
                        warn.setWarn1("3滑台信号异常");
                        warn.setDevice(deviceStr[i]);
                        warnList.add(warn);
                    }
                    if (jsonObject.getString("M254)").contains("1.0")) {
                        warn.setTime(time);
                        warn.setWarn1("4滑台信号异常");
                        warn.setDevice(deviceStr[i]);
                        warnList.add(warn);
                    }
                    if (jsonObject.getString("M250)").contains("1.0")) {
                        warn.setTime(time);
                        warn.setWarn1("工作台信号异常");
                        warn.setDevice(deviceStr[i]);
                        warnList.add(warn);
                    }
                    if (jsonObject.getString("M294)").contains("1.0")) {
                        warn.setTime(time);
                        warn.setWarn1("自动加工未完成");
                        warn.setDevice(deviceStr[i]);
                        warnList.add(warn);
                    }
                }
            } else {
                JSONArray jsonArray = null;
                try {
                    jsonArray = ioTDBUtil.listDataHistory(tenant, deviceStr[i], tag, DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -1)), DateUtil.formatDateTime(new Date()));
                } catch (IoTDBConnectionException e) {
                    e.printStackTrace();
                } catch (StatementExecutionException e) {
                    e.printStackTrace();
                }
                for (int j = 0; j < jsonArray.size(); j++) {
                    IotWarn warn = new IotWarn();
                    JSONObject jsonObject = jsonArray.getJSONObject(j);
                    String time = jsonObject.getString("time");
                    if (jsonObject.getString("F61)").contains("1.0")) {//判断报警类型
                        warn.setTime(time);
                        warn.setWarn1("导轨润滑异常");
                        warn.setDevice(deviceStr[i]);
                        warnList.add(warn);
                    }
                    if (jsonObject.getString("F62)").contains("1.0")) {
                        warn.setTime(time);
                        warn.setWarn1("冷却污水箱液位低");
                        warn.setDevice(deviceStr[i]);
                        warnList.add(warn);
                    }
                    if (jsonObject.getString("F108)").contains("1.0")) {
                        warn.setTime(time);
                        warn.setWarn1("工作台信号异常");
                        warn.setDevice(deviceStr[i]);
                        warnList.add(warn);
                    }
                    if (jsonObject.getString("F199)").contains("1.0")) {
                        warn.setTime(time);
                        warn.setWarn1("超时报警");
                        warn.setDevice(deviceStr[i]);
                        warnList.add(warn);
                    }
                }
            }
        }
        List<IotWarn> iotWarns = new ArrayList<>();
        for (int i = 0; i < warnList.size(); i++) {
            IotWarn iotWarn = warnList.get(i - 1);
            if (warnList.get(i).equals(iotWarn)) {
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date date = format.parse(warnList.get(i).getTime());
                Date date1 = format.parse(iotWarn.getTime());
                long minutes = date.getTime() - date1.getTime();
                if (minutes != 60000) {
                    iotWarns.add(iotWarn);
                }
            }
        }
        return AjaxResult.success(iotWarns);
    }

    @GetMapping("/listTagDifferenceMonth")
    public AjaxResult listTagDifferenceMonth(String device, String rq, String tenant) {
        JSONArray result = new JSONArray();
        String tag = "Number";
        String nights = "05:00";
        String nighte = "10:00";
        String days = "17:00";
        String daye = "22:00";
        String deviceStr[] = device.split(";");
        for (int x = 0; x < 30; x++) {
            rq = DateUtil.formatDate(DateUtil.offsetDay(IotScreenController.getMonthStart(), x));
            String startNight = rq + " " + nights + ":00";
            String endNight = rq + " " + nighte + ":00";
            JSONObject object = new JSONObject();
            for (int i = 0; i < deviceStr.length; i++) {
                try {
                    double number = 0;
                    String dayTime = "";
                    String[] time;
                    JSONArray jsonArray = ioTDBUtil.listDataHistory(tenant, deviceStr[i], tag, startNight, endNight);
                    for (int j = 0; j < jsonArray.size(); j++) {
                        if (!jsonArray.getJSONObject(j).getString("Number)").equals("null")) {
                            String val = jsonArray.getJSONObject(j).getString("Number)");
                            time = jsonArray.getJSONObject(j).getString("time").split(" ");
                            dayTime = time[0];
                            double temp = Double.parseDouble(val);
                            if (temp > number) {
                                number = temp;
                            }
                        }
                    }
                    object.put("Daynumber", number);
                    object.put("Time", dayTime);
                } catch (IoTDBConnectionException e) {
                    e.printStackTrace();
                } catch (StatementExecutionException e) {
                    e.printStackTrace();
                }
            }
            String startDay = rq + " " + days + ":00";
            String endDay = rq + " " + daye + ":00";
            for (int i = 0; i < deviceStr.length; i++) {
                try {
                    double number = 0;
                    String[] time = new String[10];
                    JSONArray jsonArray = ioTDBUtil.listDataHistory(tenant, deviceStr[i], tag, startDay, endDay);
                    for (int j = 0; j < jsonArray.size(); j++) {
                        if (!jsonArray.getJSONObject(j).getString("Number)").equals("null")) {
                            String val = jsonArray.getJSONObject(j).getString("Number)");
                            double temp = Double.parseDouble(val);
                            if (temp > number) {
                                number = temp;
                            }
                        }
                    }
                    object.put("Nightnumber", number);
                    object.put("device", deviceStr[i]);
                    result.add(object);
                } catch (IoTDBConnectionException e) {
                    e.printStackTrace();
                } catch (StatementExecutionException e) {
                    e.printStackTrace();
                }
            }
        }
        return AjaxResult.success(result);
    }

    @GetMapping("/getHshData")
    public AjaxResult getHshData() {
        String tenant = "a9393c1445f7432fb64712fb302bd576";
        List<HshData> listHsh = new ArrayList<>();
        String end = DateUtil.format(new Date(), "yyyy-MM-dd hh:mm:ss");
        IotEquipment iotEquipment = new IotEquipment();
        iotEquipment.setTslId(9l);
        MesModulRecord mesModul = new MesModulRecord();

        DecimalFormat df = new DecimalFormat("#.00");

        List<IotEquipment> list = equipmentService.selectIotEquipmentList(iotEquipment);
        for (IotEquipment equipment : list) {
            mesModul.setEquipmentId(Convert.toInt(equipment.getId()));
            List<MesModulRecord> mesModulRecords = mesModulRecordService.listCurrentModule1(mesModul);
            if (mesModulRecords.size() > 0) {
                String start = DateUtil.formatDateTime(mesModulRecords.get(0).getStartTime());
                HshData hshData = new HshData();
                hshData.setDeviceId(equipment.getId());
                hshData.setDeviceName("HSH008-" + Convert.toStr(equipment.getEquipmentSort()));
                JSONArray hslNumber = ioTDBUtil.getMaxAndMinByDay(tenant, equipment.getEquipmentCode(), "Number", start, end);
                if (hslNumber.size() > 0) {
                    double max;
                    double min;
                    if (!StrUtil.isEmpty(hslNumber.getJSONObject(0).getString("min")) && !hslNumber.getJSONObject(0).getString("min").equals("null")) {
                        min = Convert.toDouble(hslNumber.getJSONObject(0).getString("min"));
                    } else {
                        min = 0;
                    }
                    if (!StrUtil.isEmpty(hslNumber.getJSONObject(0).getString("max")) && !hslNumber.getJSONObject(0).getString("max").equals("null")) {
                        max = Convert.toDouble(hslNumber.getJSONObject(0).getString("max"));
                    } else {
                        max = 0;
                    }
                    int number = (int) (max - min);
                    hshData.setNumber(number * mesModulRecords.get(0).getProductionMultiple());
                    hshData.setProduction(mesModulRecords.get(0).getProductionName());
                    hshData.setModel(mesModulRecords.get(0).getModulName());
                    IorealData query = new IorealData();
                    IorealData query1 = new IorealData();
                    query.setDeviceCode(equipment.getEquipmentCode());
                    query1.setDeviceCode(equipment.getEquipmentCode());
                    query1.setTag("Work_Status");
                    query.setTag("Cycle");
                    List<IorealData> iorealData = realDataService.selectIorealDataList(query);
                    List<IorealData> iorealData1 = realDataService.selectIorealDataList(query1);
                    hshData.setWorkStatus(Convert.toInt(iorealData1.get(0).getVal()));
                    hshData.setCycle(df.format(Convert.toDouble(iorealData.get(0).getVal()) / 10));
                    listHsh.add(hshData);
                }
            }
        }
        return AjaxResult.success(listHsh);
    }

    @ApiOperation("当天设备稼动率")
    @GetMapping("/listCropRate")
    public AjaxResult listCropRate(String rq) {
        String tenant = "a9393c1445f7432fb64712fb302bd576";
        String day = "08:00";
        if (StrUtil.isEmpty(rq)) {
            rq = DateUtil.formatDate(new Date());
        }
        IotEquipment iotEquipment = new IotEquipment();
        iotEquipment.setTslId(9l);
        String start = rq + " " + day + ":00";
        List<IotEquipment> list = equipmentService.selectIotEquipmentList(iotEquipment);
        String s = DateUtil.formatDate(DateUtil.offsetDay(new Date(), 1));
        String end = s + " " + day + ":00";
        List<CropRate> cropRateList = new ArrayList<>();
        for (IotEquipment equipment : list) {
            CropRate cropRate = new CropRate();
            IotEquipmentProp iotEquipmentProp = new IotEquipmentProp();
            iotEquipmentProp.setEquipmentId(equipment.getId());
            iotEquipmentProp.setAttrId(30L);
            List<IoTAttrVO> ioTAttrVOS = iotEquipmentPropService.selectIotEquipmentPropList(iotEquipmentProp);
            cropRate.setEnumlist(ioTAttrVOS.get(1).getEnumList().replace("", ""));
            try {
                double work = 0;
                double wait = 0;
                double workTime = 0;
                int waitTime = 0;
                JSONArray productStatusList = ioTDBUtil.listDataHistory(tenant, equipment.getEquipmentCode(), "Product_Status", start, end);
                for (int i = 0; i < productStatusList.size(); i++) {
                    if (!"null".equals(productStatusList.getJSONObject(i).getString("Product_Status)")) && productStatusList.getJSONObject(i).getString("Product_Status)") != null) {
                        if (Convert.toDouble(productStatusList.getJSONObject(i).getString("Product_Status)")) == 1.0) {
                            work++;
                        } else {
                            wait++;
                        }
                    }
                }
                JSONArray workStatusList = ioTDBUtil.listDataHistory(tenant, equipment.getEquipmentCode(), "Work_Status", start, end);
                for (int i = 0; i < workStatusList.size(); i++) {
                    if (!"null".equals(workStatusList.getJSONObject(i).getString("Work_Status)")) && workStatusList.getJSONObject(i).getString("Work_Status)") != null) {
                        if (Convert.toDouble(workStatusList.getJSONObject(i).getString("Work_Status)")) == 1.0) {
                            workTime++;
                        } else {
                            waitTime++;
                        }
                    }
                }
                IorealData query = new IorealData();
                query.setDeviceCode(equipment.getEquipmentCode());
                query.setTag("Product_Status");
                List<IorealData> iorealData = realDataService.selectIorealDataList(query);
                cropRate.setProductStatus(Convert.toInt(iorealData.get(0).getVal()));
                cropRate.setId(equipment.getId());
                cropRate.setStart(work);
                cropRate.setOut(wait);
                cropRate.setWait(waitTime);
                cropRateList.add(cropRate);
            } catch (IoTDBConnectionException e) {
                e.printStackTrace();
            } catch (StatementExecutionException e) {
                e.printStackTrace();
            }
        }
        return AjaxResult.success(cropRateList);
    }

    @GetMapping("/realTimeDevices")
    public AjaxResult realTimeDevices() throws ParseException {
        List<Integer> list = new ArrayList<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        List<IorealData> iorealDataList = realDataService.selectIorealDataList(null);
        int totalNumber;
        List<IotEquipment> equipments = equipmentService.list(null);
        totalNumber = equipments.size();
        int workDevice = 0;
        int unWorkDevice = 0;
        int alarmDevice = 0;
        for (IorealData iorealData : iorealDataList) {
            LocalDate date = LocalDate.parse(simpleDateFormat.format(iorealData.getUpdateTime()));
            LocalDate date1 = LocalDate.parse(simpleDateFormat.format(new Date()));
            int days = (int) ChronoUnit.DAYS.between(date1, date);
            if (days <= 1) {
                if (iorealData.getTag() == "Product_Status") {
                    if (iorealData.getVal() == "1") {
                        workDevice++;
                    } else {
                        unWorkDevice++;
                    }
                }
                if (iorealData.getTag() == "Alarm") {
                    if (iorealData.getVal() == "1") {
                        alarmDevice++;
                    }
                }
            }
        }
        list.add(totalNumber);
        list.add(workDevice);
        list.add(unWorkDevice);
        list.add(alarmDevice);
        return AjaxResult.success(list);
    }
}

class IotWarn {
    private String device;
    private String warn1;
    private String time;

    public String getWarn1() {
        return warn1;
    }

    public void setWarn1(String warn1) {
        this.warn1 = warn1;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }
}

@Data
@NoArgsConstructor
@AllArgsConstructor
class HshData {
    private Long deviceId;
    private String deviceName;
    private String production;
    private String model;
    private String cycle;
    private int number;
    private int workStatus;

}

@Data
@NoArgsConstructor
@AllArgsConstructor
class CropRate {
    private double out;
    private double start;
    private Long id;
    private int wait;
    private String enumlist;
    private int productStatus;
}