package com.boyo.web.controller.haihui;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.text.Convert;
import com.boyo.master.entity.PurchaseOrders;
import com.boyo.master.entity.SaleOrder;
import com.boyo.master.service.impl.PurchaseOrdersServiceImpl;
import com.boyo.master.service.impl.SaleOrderServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

@RestController
@RequestMapping("/haihui/screen/bank")
public class HaiHuiDapingBankController {
    @Autowired
    private SaleOrderServiceImpl saleOrderService;
    @Autowired
    private PurchaseOrdersServiceImpl purchaseOrdersService;

    private String enterpriseId = "1845014766829256705";

    @GetMapping("/saleOrder/info")
    private AjaxResult saleOrderInfo() {
        JSONObject ans = new JSONObject();
        IPage page = new Page<>(1, 10000);
        Map<String, String> params = new HashMap<>();
        params.put("enterpriseId", enterpriseId);
        List<String> monthList = new ArrayList();
        List<Integer> orderCountList = new ArrayList();
        List<Double> orderMoneyList = new ArrayList();

        final DateTime dateNow = DateUtil.date();
        for (int i = 0; i < 6; i++) {
            monthList.add(DateUtil.format(DateUtil.offsetMonth(dateNow, -i), "MM") + "月");
            final DateTime start = DateUtil.beginOfMonth(DateUtil.offsetMonth(dateNow, -i));
            final DateTime end = DateUtil.endOfMonth(DateUtil.offsetMonth(dateNow, -i));
            params.put("dbilldateStart", DateUtil.format(start, "yyyy-MM-dd"));
            params.put("dbilldateEnd", DateUtil.format(end, "yyyy-MM-dd"));

            final Object dataAssetList = saleOrderService.getDataAssetList(page, params);
            final List records = page.getRecords();
            int total = records.size();
            orderCountList.add(total);
            double totalMoney = 0;
            for (Object record : records) {
                SaleOrder oneSaleOrder = (SaleOrder) record;
                totalMoney += oneSaleOrder.getNsummny().doubleValue();
            }
            orderMoneyList.add(totalMoney);
        }
        ans.put("monthList", monthList);
        ans.put("orderCountList", orderCountList);
        ans.put("orderMoneyList", orderMoneyList);
        return AjaxResult.success(ans);

    }


    @GetMapping("/purchaseOrder/info")
    private AjaxResult purchaseOrderInfo() {
        JSONObject ans = new JSONObject();
        IPage page = new Page<>(1, 10000);
        Map<String, String> params = new HashMap<>();
        params.put("enterpriseId", enterpriseId);
        List<String> monthList = new ArrayList();
        List<Integer> orderCountList = new ArrayList();
        List<Double> orderMoneyList = new ArrayList();

        final DateTime dateNow = DateUtil.date();
        for (int i = 0; i < 6; i++) {
            monthList.add(DateUtil.format(DateUtil.offsetMonth(dateNow, -i), "MM") + "月");
            final DateTime start = DateUtil.beginOfMonth(DateUtil.offsetMonth(dateNow, -i));
            final DateTime end = DateUtil.endOfMonth(DateUtil.offsetMonth(dateNow, -i));
            params.put("dbilldateStart", DateUtil.format(start, "yyyy-MM-dd"));
            params.put("dbilldateEnd", DateUtil.format(end, "yyyy-MM-dd"));

            final Object dataAssetList = purchaseOrdersService.getDataAssetList(page, params);
            final List records = page.getRecords();
            int total = records.size();
            orderCountList.add(total);
            double totalMoney = 0;
            for (Object record : records) {
                PurchaseOrders purchaseOrders = (PurchaseOrders) record;
                totalMoney += purchaseOrders.getNoriginaltaxpricemny().doubleValue();
            }
            orderMoneyList.add(totalMoney);
        }
        ans.put("monthList", monthList);
        ans.put("orderCountList", orderCountList);
        ans.put("orderMoneyList", orderMoneyList);
        return AjaxResult.success(ans);


    }


}
