package com.boyo.web.controller.master;

import java.util.List;
import java.util.Arrays;

import com.boyo.master.vo.ModelAreaVO;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.boyo.master.domain.ModelArea;
import com.boyo.master.service.IModelAreaService;

/**
 * 主数据-区域管理Controller
 *
 * <AUTHOR>
 */
@Api("主数据-区域管理")
@RestController
@RequestMapping("/master/area")
@AllArgsConstructor
public class ModelAreaController extends BaseController {
    private final IModelAreaService modelAreaService;

    /**
     * 查询主数据-区域管理列表
     */
    @ApiOperation("查询主数据-区域管理列表")
    @GetMapping("/list")
    public TableDataInfo list(ModelArea modelArea) {
        startPage();
        List<ModelAreaVO> list = modelAreaService.selectModelAreaList(modelArea);
        return getDataTable(list);
    }

    /**
     * 获取主数据-区域管理详细信息
     */
    @ApiOperation("获取主数据-区域管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(modelAreaService.getById(id));
    }

    /**
     * 新增主数据-区域管理
     */
    @ApiOperation("新增主数据-区域管理")
    @PostMapping
    public AjaxResult add(@RequestBody ModelArea modelArea) {
        modelArea.setAreaOpenid(super.generateOpenid());
        return toBooleanAjax(modelAreaService.save(modelArea));
    }

    /**
     * 修改主数据-区域管理
     */
    @ApiOperation("修改主数据-区域管理")
    @PutMapping
    public AjaxResult edit(@RequestBody ModelArea modelArea) {
        return toBooleanAjax(modelAreaService.updateById(modelArea));
    }

    /**
     * 删除主数据-区域管理
     */
    @ApiOperation("删除主数据-区域管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(modelAreaService.removeByIds(Arrays.asList(ids)));
    }
}
