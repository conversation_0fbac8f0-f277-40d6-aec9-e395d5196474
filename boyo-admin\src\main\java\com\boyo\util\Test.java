package com.boyo.util;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.boyo.common.core.text.Convert;
import com.boyo.iot.domain.HistoryData;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.mapper.IotEquipmentMapper;
import com.boyo.iot.util.IoTDBUtil;
import com.boyo.mes.entity.*;
import com.boyo.mes.mapper.ProductOrderMapper;
import com.boyo.mes.service.*;
import com.boyo.processor.TenantProcessor;
import com.boyo.system.domain.TSysEnterprise;
import com.boyo.system.service.ITSysEnterpriseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;

//@Component
public class Test {
    @Autowired
    private TenantProcessor tenantProcessor;

    @Autowired
    private ITSysEnterpriseService sysEnterpriseService;

    @Autowired
    private IProductOrderDetailService productOrderDetailService;

    @Autowired
    private  IProductOrderService productOrderService;

    @Autowired
    private  IotEquipmentMapper equipmentMapper;

    @Autowired
    private  IoTDBUtil ioTDBUtil;

    @Autowired
    private IProcessGroupDetailService groupDetailService;

    @Autowired
    private TFactoryOrderService tFactoryOrderService;

    @Autowired
    private IProductOrderDetailService orderDetailMapper;

    @Autowired
    private IProductOrderService orderService;

    @Autowired
    private IWorkReportService workReportService;

//
//    @Scheduled(cron = "0 */1 * * * ?")
//    //自动完成工序执行单
//    public void autoCompleteProcess() {
//        DynamicDataSourceContextHolder.push("master");
//        QueryWrapper<TSysEnterprise> enterpriseQueryWrapper = new QueryWrapper<>();
//        enterpriseQueryWrapper.eq("factory_status", 1);
//        List<TSysEnterprise> list = sysEnterpriseService.list(enterpriseQueryWrapper);//查询出开通数字工厂的企业
//        for (TSysEnterprise tSysEnterprise : list) {
//            String enterpriseCode = tSysEnterprise.getEnterpriseOpenid();
//            //          手动切换数据源
//            if (checkEnterprise(enterpriseCode) && tenantProcessor.checkDataSource(enterpriseCode)) {
//                //          手动切换数据源
//                DynamicDataSourceContextHolder.push(enterpriseCode);
//                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                QueryWrapper<ProductOrder> productOrderQueryWrapper = new QueryWrapper<>();
//                productOrderQueryWrapper.ne("order_status", 2);
//                List<ProductOrder> tempOrderlist = productOrderService.list(productOrderQueryWrapper);//查出未完成的工单
//                for (ProductOrder productOrder : tempOrderlist) {
//                    QueryWrapper<ProductOrderDetail> queryWrapper = new QueryWrapper<>();
//                    queryWrapper.ne("status", 2);//查出未完成的工序
//                    List<ProductOrderDetail> temporderDetails = productOrderDetailService.list(queryWrapper);
//                    for (ProductOrderDetail orderDetail : temporderDetails) {
//                        QueryWrapper<ProductOrderDetail> productOrderDetails = new QueryWrapper<>();
//                        productOrderDetails.select("sum(product_num) as total");
//                        productOrderDetails.eq("order_id", productOrder.getId()).eq("process_id", orderDetail.getProcessId());
//                        Map<String, Object> map = productOrderDetailService.getMap(productOrderDetails);
//                        if (map != null) {
//                            BigDecimal sumCount = (BigDecimal) map.get("total");
//                            BigDecimal productionNum = BigDecimal.valueOf(productOrder.getProductionNum());
//                            UpdateWrapper<ProductOrderDetail> updateWrapper = new UpdateWrapper<>();
//                            if (sumCount.equals(productionNum)) {
//                                updateWrapper.set("status", 1).eq("id", orderDetail.getId())
//                                        .eq("order_id", orderDetail.getOrderId())
//                                        .eq("start_time", orderDetail.getStartTime())
//                                        .eq("task_id",orderDetail.getTaskId())
//                                        .eq("process_id",orderDetail.getProcessId());
//                                productOrderDetailService.update(updateWrapper);
//                            }
//                        }
//                    }
//                }
//            }
//        }
//    }
    /**
     * 订单状态自动判断
     */
    @Scheduled(cron = "0 */1 * * * ?")
    public void autoList() throws ParseException {
        DynamicDataSourceContextHolder.push("master");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("yyyy-MM-dd");
        QueryWrapper<TSysEnterprise> enterpriseQueryWrapper = new QueryWrapper<>();
        enterpriseQueryWrapper.eq("factory_status", 1);
        List<TSysEnterprise> enterpriseList = sysEnterpriseService.list(enterpriseQueryWrapper);
        for (TSysEnterprise tSysEnterprise : enterpriseList) {

            String enterpriseCode = tSysEnterprise.getEnterpriseOpenid();
            //          手动切换数据源
            if (checkEnterprise(enterpriseCode) && tenantProcessor.checkDataSource(enterpriseCode)) {
                //          手动切换数据源
                DynamicDataSourceContextHolder.push(enterpriseCode);
                List<TFactoryOrder> list = tFactoryOrderService.list();
                for (TFactoryOrder tFactoryOrder : list) {
                    if (tFactoryOrder.getStartTime() == null) {
                        QueryWrapper<ProductOrder> taskQueryWrapper = new QueryWrapper<>();
                        taskQueryWrapper.eq("task_id", tFactoryOrder.getId());
                        List<ProductOrder> list1 = productOrderService.list(taskQueryWrapper);
                        if (list1.size() > 0) {
                            UpdateWrapper<TFactoryOrder> updateWrapper = new UpdateWrapper<>();
                            updateWrapper
                                    .set("start_time", simpleDateFormat.format(new Date()))
                                    .set("order_status", 1)
                                    .eq("id", tFactoryOrder.getId());
                            tFactoryOrderService.update(updateWrapper);
                        }
                    }
                    LocalDate date = LocalDate.parse(simpleDateFormat1.format(simpleDateFormat.parse(simpleDateFormat.format(new Date()))));
                    LocalDate date1 = LocalDate.parse(simpleDateFormat1.format(tFactoryOrder.getCreateTime()));
                    int days = (int) ChronoUnit.DAYS.between(date1, date);
                    if (days > tFactoryOrder.getDeliveryCycle()) {
                        UpdateWrapper<TFactoryOrder> updateWrapper = new UpdateWrapper<>();
                        updateWrapper
                                .set("overdue", "逾期" + Convert.toStr(days - tFactoryOrder.getDeliveryCycle()) + "天")
                                .eq("id", tFactoryOrder.getId());
                        tFactoryOrderService.update(updateWrapper);
                    } else if (days < tFactoryOrder.getDeliveryCycle()) {
                        UpdateWrapper<TFactoryOrder> updateWrapper = new UpdateWrapper<>();
                        updateWrapper
                                .set("overdue", "距离交付" + Convert.toStr(tFactoryOrder.getDeliveryCycle() - days) + "天")
                                .eq("id", tFactoryOrder.getId());
                        tFactoryOrderService.update(updateWrapper);
                    } else {
                        UpdateWrapper<TFactoryOrder> updateWrapper = new UpdateWrapper<>();
                        updateWrapper
                                .set("overdue", "逾期")
                                .eq("id", tFactoryOrder.getId());
                        tFactoryOrderService.update(updateWrapper);
                    }
                    QueryWrapper<ProductOrder> taskQueryWrapper = new QueryWrapper<>();
                    taskQueryWrapper.eq("task_id", tFactoryOrder.getId());
                    List<ProductOrder> productOrderList = orderService.list(taskQueryWrapper);
                    int processId = 0;
                    int sort = 0;
                    int finishFlag = 0;
                    for (ProductOrder productOrder : productOrderList) {
                        finishFlag += Convert.toInt(productOrder.getOrderStatus());
                        QueryWrapper<ProcessGroupDetail> groupDetailQueryWrapper = new QueryWrapper<>();
                        groupDetailQueryWrapper.eq("group_id", productOrder.getProcessGroupId());// 查找工单中的工序组id
                        List<ProcessGroupDetail> groupList = groupDetailService.list(groupDetailQueryWrapper);
                        boolean flag = false;
                        for (ProcessGroupDetail processGroupDetail : groupList) {
                            int temp = 0;
                            temp = processGroupDetail.getSortNum();
                            if (temp > processId) {
                                sort = temp;
                            }
                            QueryWrapper<ProcessGroupDetail> groupDetailQueryWrapper1 = new QueryWrapper<>();
                            groupDetailQueryWrapper1.eq("sort_num", sort);
                            List<ProcessGroupDetail> groupList1 = groupDetailService.list(groupDetailQueryWrapper);//查找出最后一道工序的processid
                            for (ProcessGroupDetail groupDetail : groupList1) {
                                processId = groupDetail.getProcessId();//将工序id赋值给process_id
                            }

                            QueryWrapper<ProductOrderDetail> orderDetailQueryWrapper = new QueryWrapper<>();
                            orderDetailQueryWrapper.eq("process_id", processGroupDetail.getProcessId());
                            orderDetailQueryWrapper.eq("order_id", productOrder.getId());
                            List<ProductOrderDetail> productDetailList = orderDetailMapper.list(orderDetailQueryWrapper);
                            if (productDetailList.size() == 0) {
                                flag = true;
                            }
                        }
                        if (!flag) {
                            QueryWrapper<ProductOrderDetail> orderDetailQueryWrapper = new QueryWrapper<>();
                            orderDetailQueryWrapper.select("sum(product_num) as total");
                            orderDetailQueryWrapper.eq("process_id", processId);
                            orderDetailQueryWrapper.eq("order_id", productOrder.getId());
                            Map<String, Object> map = orderDetailMapper.getMap(orderDetailQueryWrapper);
                            QueryWrapper<WorkReport> workReportQueryWrapper = new QueryWrapper<>();
                            workReportQueryWrapper.select("sum(waste_num) as total");
                            workReportQueryWrapper.eq("process_id", processId);
                            workReportQueryWrapper.eq("order_id", productOrder.getId());
                            Map<String, Object> map1 = workReportService.getMap(workReportQueryWrapper);
                            BigDecimal sumWaste = null;
                            if (map1 != null) {
                                sumWaste = (BigDecimal) map1.get("total");
                            }
                            if (map != null) {
                                BigDecimal sumCount = (BigDecimal) map.get("total");
                                BigDecimal diff = sumCount.subtract(sumWaste);
                                UpdateWrapper<TFactoryOrder> updateWrapper = new UpdateWrapper<>();
                                updateWrapper.set("finish_num", diff).eq("id", productOrder.getTaskId());
                                tFactoryOrderService.update(updateWrapper);
                            }
                        }
                    }
                    UpdateWrapper<TFactoryOrder> tFactoryOrderUpdateWrapper = new UpdateWrapper<>();
                    if (productOrderList.size() > 0) {
                        if (finishFlag == productOrderList.size() * 2) {
                            tFactoryOrderUpdateWrapper.set("order_status", 2).eq("id", productOrderList.get(0).getTaskId());
                            tFactoryOrderUpdateWrapper.set("end_time", simpleDateFormat.format(new Date())).eq("id", productOrderList.get(0).getTaskId());
                            tFactoryOrderService.update(tFactoryOrderUpdateWrapper);
                        } else {
                            tFactoryOrderUpdateWrapper.set("order_status", 1).eq("id", productOrderList.get(0).getTaskId());
                            tFactoryOrderService.update(tFactoryOrderUpdateWrapper);
                        }
                    }
                }
            }
        }
    }
    /**
     * 自动判断任务单是否完成
     * @return
     */
    @Scheduled(cron = "0 */1 * * * ?")
    public void autoCompletelist() {
        DynamicDataSourceContextHolder.push("master");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        QueryWrapper<TSysEnterprise> enterpriseQueryWrapper = new QueryWrapper<>();
        enterpriseQueryWrapper.eq("factory_status", 1);
        List<TSysEnterprise> enterpriseList = sysEnterpriseService.list(enterpriseQueryWrapper);
        for (TSysEnterprise tSysEnterprise : enterpriseList) {

            String enterpriseCode = tSysEnterprise.getEnterpriseOpenid();
            //          手动切换数据源
            if (checkEnterprise(enterpriseCode) && tenantProcessor.checkDataSource(enterpriseCode)) {
                //          手动切换数据源
                DynamicDataSourceContextHolder.push(enterpriseCode);
                QueryWrapper<ProductOrder> tempQueryWapper = new QueryWrapper<>();
                tempQueryWapper.eq("order_status", 1);
                List<ProductOrder> list = productOrderService.list(tempQueryWapper); //查找工单是执行中的
                for (ProductOrder order : list) {
                    int processId = 0;
                    int sort = 0;
                    QueryWrapper<ProcessGroupDetail> groupDetailQueryWrapper = new QueryWrapper<>();
                    groupDetailQueryWrapper.eq("group_id", order.getProcessGroupId()); // 查找工单中的工序组id
                    List<ProcessGroupDetail> groupList = groupDetailService.list(groupDetailQueryWrapper);
                    boolean flag = false;
                    for (ProcessGroupDetail processGroupDetail : groupList) {
                        int temp = 0;
                        if (processGroupDetail.getSortNum() != null) {
                            temp = processGroupDetail.getSortNum();
                        }
                        if (temp > processId) {
                            sort = temp;
                        }
                        QueryWrapper<ProcessGroupDetail> groupDetailQueryWrapper1 = new QueryWrapper<>();
                        groupDetailQueryWrapper1.eq("sort_num", sort);
                        List<ProcessGroupDetail> groupList1 = groupDetailService.list(groupDetailQueryWrapper);//查找出最后一道工序的processid
                        for (ProcessGroupDetail groupDetail : groupList1) {
                            processId = groupDetail.getProcessId();//将工序id赋值给process_id
                        }

                        QueryWrapper<ProductOrderDetail> orderDetailQueryWrapper = new QueryWrapper<>();
                        orderDetailQueryWrapper.eq("process_id", processGroupDetail.getProcessId());
                        orderDetailQueryWrapper.eq("order_id", order.getId());
                        List<ProductOrderDetail> productDetailList = productOrderDetailService.list(orderDetailQueryWrapper);
                        if (productDetailList.size() == 0) {
                            flag = true;
                        }
                    }
                    if (!flag) {
                        QueryWrapper<WorkReport> orderDetailQueryWrapper = new QueryWrapper<>();
                        orderDetailQueryWrapper.select("sum(report_num) as total");
                        orderDetailQueryWrapper.eq("process_id", processId);
                        orderDetailQueryWrapper.eq("order_id", order.getId());
                        Map<String, Object> map = workReportService.getMap(orderDetailQueryWrapper);
                        if (map != null) {
                            BigDecimal sumCount = (BigDecimal) map.get("total");
                            BigDecimal productionNum = BigDecimal.valueOf(order.getOrderAmounts());
                            UpdateWrapper<ProductOrder> updateWrapper = new UpdateWrapper<>();
                            if (sumCount.equals(productionNum)) {  //最后一道工序的产量，如果等于预设值，则任务完成。
                                updateWrapper
                                        .set("order_status", 2)
                                        .eq("id", order.getId())
                                        .set("order_finish_num", sumCount);
                                if (order.getCompleteTime() == null) {
                                    updateWrapper.set("complete_time", simpleDateFormat.format(new Date()));
                                }
                            } else {
                                updateWrapper.set("order_status", 1).eq("id", order.getId());
                                updateWrapper.set("complete_time", null)
                                        .set("order_finish_num", sumCount);
                                ;
                            }
                            productOrderService.update(updateWrapper);
                        }
                    }
                }
            }
        }
    }

    /***
     * 工单完成数量表
     */
    @Scheduled(cron = "0 */1 * * * ?")
    public void list() throws Exception {
        DynamicDataSourceContextHolder.push("master");
        QueryWrapper<TSysEnterprise> enterpriseQueryWrapper = new QueryWrapper<>();
        enterpriseQueryWrapper.eq("factory_status", 1);
        List<TSysEnterprise> enterpriseList = sysEnterpriseService.list(enterpriseQueryWrapper);
        for (TSysEnterprise tSysEnterprise : enterpriseList) {

            String enterpriseCode = tSysEnterprise.getEnterpriseOpenid();
            //          手动切换数据源
            if (checkEnterprise(enterpriseCode) && tenantProcessor.checkDataSource(enterpriseCode)) {
                //          手动切换数据源
                DynamicDataSourceContextHolder.push(enterpriseCode);
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String tenant = enterpriseCode;
                List<ProductOrderDetail> list = productOrderDetailService.selectProductOrderDetailList(null);
                for (int i = 0; i < list.size(); i++) {
                    QueryWrapper<IotEquipment> equipmentQueryWrapper = new QueryWrapper<>();
                    equipmentQueryWrapper.eq("id", list.get(i).getEquipmentId());
                    List<IotEquipment> iotEquipmentList = equipmentMapper.selectList(equipmentQueryWrapper);
                    if (iotEquipmentList.size() > 0) {
                        String equipmentCode = equipmentMapper.selectList(equipmentQueryWrapper).get(0).getEquipmentCode();
                        List<HistoryData> productNum = null;
                        double highNum = 0;
                        double lowNum = 0;
                        int totalNum = 0;
                        if (list.get(i).getStopTime() == null || list.get(i).getStopTime().equals("null")) {//订单结束就查订单结束时间，否则就是实时
                            productNum = ioTDBUtil.listData(tenant, equipmentCode, "number", simpleDateFormat.format(list.get(i).getStartTime()), simpleDateFormat.format(new Date()));
                        } else {
                            productNum = ioTDBUtil.listData(tenant, equipmentCode, "number", simpleDateFormat.format(list.get(i).getStartTime()), simpleDateFormat.format(list.get(i).getStopTime()));
                        }
                        for (int j = 0; j < productNum.size(); j++) {//计算工序的设备产量
                            if (productNum.get(j).getVal() != null && !productNum.get(j).getVal().equals("null")) {
                                Object val = productNum.get(j).getVal();
                                lowNum = Convert.toDouble(val);
                                break;
                            }
                        }
                        for (int j = productNum.size() - 1; j > 0; j--) {
                            if (productNum.get(j).getVal() != null && !productNum.get(j).getVal().equals("null")) {
                                highNum = Convert.toDouble(productNum.get(j).getVal());
                                break;
                            }
                        }
                        totalNum = (int) (highNum - lowNum);
                        list.get(i).setProductNum(totalNum);
                        UpdateWrapper<ProductOrderDetail> updateWrapper = new UpdateWrapper<>();
                        updateWrapper.set("product_num", totalNum);
                        updateWrapper.eq("id", list.get(i).getId());
                        productOrderDetailService.update(updateWrapper);
                    }
                }
            }
        }
    }

//    @Scheduled(cron = "0 */1 * * * ?")
//    public void workOrderNum() {
//        DynamicDataSourceContextHolder.push("master");
//        QueryWrapper<TSysEnterprise> enterpriseQueryWrapper = new QueryWrapper<>();
//        enterpriseQueryWrapper.eq("factory_status", 1);
//        List<TSysEnterprise> enterpriseList = sysEnterpriseService.list(enterpriseQueryWrapper);
//        for (TSysEnterprise tSysEnterprise : enterpriseList) {
//
//            String enterpriseCode = tSysEnterprise.getEnterpriseOpenid();
//            //          手动切换数据源
//            if (checkEnterprise(enterpriseCode) && tenantProcessor.checkDataSource(enterpriseCode)) {
//                //          手动切换数据源
//                DynamicDataSourceContextHolder.push(enterpriseCode);
//                List<TFactoryOrder> list = tFactoryOrderService.list(null);
//                for (TFactoryOrder tFactoryOrder : list) {
//                    QueryWrapper<ProductOrder> orderQueryWrapper = new QueryWrapper<>();
//                    orderQueryWrapper.eq("task_id", tFactoryOrder.getId());
//                    List<ProductOrder> orderList = orderService.list(orderQueryWrapper);
//                    int totalNum = 0;
//                    for (ProductOrder productOrder : orderList) {
//                        totalNum += productOrder.getProductionNum();
//                    }
//                    if (totalNum == tFactoryOrder.getNumber()) {
//                        UpdateWrapper<ProductOrder> updateWrapper = new UpdateWrapper<>();
//                        updateWrapper.set("create_status", "Y").eq("task_id", tFactoryOrder.getId());
//                        orderService.update(updateWrapper);
//                    } else {
//                        UpdateWrapper<ProductOrder> updateWrapper = new UpdateWrapper<>();
//                        updateWrapper.set("create_status", "N").eq("task_id", tFactoryOrder.getId());
//                        orderService.update(updateWrapper);
//                    }
//                }
//            }
//        }
//    }

    private boolean checkEnterprise(String enterpriseCode) {
        if (existList.containsKey(enterpriseCode)) {
            return true;
        } else {
            if (errorList.containsKey(enterpriseCode) && Convert.toInt(errorList.get(enterpriseCode)) < 100) {
//                重新获取企业信息
                errorList.put(enterpriseCode, Convert.toStr(Convert.toInt(errorList.get(enterpriseCode)) + 1));
                return false;
            } else {
                DynamicDataSourceContextHolder.push("master");
                QueryWrapper<TSysEnterprise> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("enterprise_openid", enterpriseCode);
                TSysEnterprise enterprise = sysEnterpriseService.getOne(queryWrapper);
                if (ObjectUtil.isNotNull(enterprise) && enterprise.getEnterpriseInit().equals("1")) {
                    existList.put(enterpriseCode, "1");
                    return true;
                } else {
                    errorList.put(enterpriseCode, "1");
                    return false;
                }
            }
        }
    }

    LinkedHashMap<String, String> existList = new LinkedHashMap<String, String>() {
        private static final long serialVersionUID = 1L;

        @Override
        protected boolean removeEldestEntry(Map.Entry<String, String> eldest) {
            return size() > 200;
        }
    };

    LinkedHashMap<String, String> errorList = new LinkedHashMap<String, String>() {
        private static final long serialVersionUID = 1L;

        @Override
        protected boolean removeEldestEntry(Map.Entry<String, String> eldest) {
            return size() > 200;
        }
    };

}
