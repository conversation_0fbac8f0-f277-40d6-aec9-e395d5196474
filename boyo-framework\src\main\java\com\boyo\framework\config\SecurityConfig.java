package com.boyo.framework.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.logout.LogoutFilter;
import org.springframework.web.filter.CorsFilter;
import com.boyo.framework.security.filter.JwtAuthenticationTokenFilter;
import com.boyo.framework.security.handle.AuthenticationEntryPointImpl;
import com.boyo.framework.security.handle.LogoutSuccessHandlerImpl;

/**
 * spring security配置
 *
 * <AUTHOR>
 */
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true)
public class SecurityConfig extends WebSecurityConfigurerAdapter {
    /**
     * 自定义用户认证逻辑
     */
//    @Autowired
//    private UserDetailsService userDetailsService;

    /**
     * 认证失败处理类
     */
    @Autowired
    private AuthenticationEntryPointImpl unauthorizedHandler;

    /**
     * 退出处理类
     */
    @Autowired
    private LogoutSuccessHandlerImpl logoutSuccessHandler;

    /**
     * token认证过滤器
     */
    @Autowired
    private JwtAuthenticationTokenFilter authenticationTokenFilter;

    /**
     * 跨域过滤器
     */
    @Autowired
    private CorsFilter corsFilter;

    /**
     * 解决 无法直接注入 AuthenticationManager
     *
     * @return
     * @throws Exception
     */
    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }

    /**
     * anyRequest          |   匹配所有请求路径
     * access              |   SpringEl表达式结果为true时可以访问
     * anonymous           |   匿名可以访问
     * denyAll             |   用户不能访问
     * fullyAuthenticated  |   用户完全认证可以访问（非remember-me下自动登录）
     * hasAnyAuthority     |   如果有参数，参数表示权限，则其中任何一个权限可以访问
     * hasAnyRole          |   如果有参数，参数表示角色，则其中任何一个角色可以访问
     * hasAuthority        |   如果有参数，参数表示权限，则其权限可以访问
     * hasIpAddress        |   如果有参数，参数表示IP地址，如果用户IP和参数匹配，则可以访问
     * hasRole             |   如果有参数，参数表示角色，则其角色可以访问
     * permitAll           |   用户可以任意访问
     * rememberMe          |   允许通过remember-me登录的用户访问
     * authenticated       |   用户登录后可访问
     */
    @Override
    protected void configure(HttpSecurity httpSecurity) throws Exception {
        httpSecurity
                // 禁用CSRF，因为不使用session
                .csrf().disable()
                // 认证失败处理类
                .exceptionHandling().authenticationEntryPoint(unauthorizedHandler).and()
                // 基于token，所以不需要session
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS).and()

                // 配置权限控制
                .authorizeRequests()
                // 对于需要Basic Auth的接口配置
                .antMatchers("/screen/hengrui/**").hasRole("HENG_RUN")  // 配置Basic Auth认证的接口
                .antMatchers("/thirdparty/huaxiabank/**").hasRole("HUAXIA_BANK")  // 配置Basic Auth认证的接口

                .antMatchers("/login", "/captchaImage", "/customize/getCostomizeData", "/system/apply").permitAll()  // 允许匿名访问的接口
                // 配置静态资源，swagger和其他公共接口也允许匿名访问
                .antMatchers(HttpMethod.GET, "/**/*.html", "/**/*.css", "/**/*.js").permitAll()
                .antMatchers("/wx/bindWechatUser").permitAll()
                .antMatchers("/iot/screen/**").permitAll()
                .antMatchers("/isv/produce").permitAll()
                .antMatchers("/lingyang/**").permitAll()
                .antMatchers("/edgp/**").permitAll()
                .antMatchers("/iot/equipment/getEquipmentDetail").permitAll()
                .antMatchers("/iot/data/getDataList1").permitAll()
                .antMatchers("/boyo/enterprise/getEnterpriseScreen").permitAll()
                .antMatchers("/profile/**").anonymous()
                .antMatchers("/common/download**").anonymous()
                .antMatchers("/common/download/resource**").anonymous()
                .antMatchers("/swagger-ui.html").anonymous()
                .antMatchers("/swagger-resources/**").anonymous()
                .antMatchers("/webjars/**").anonymous()
                .antMatchers("/*/api-docs").anonymous()
                .antMatchers("/druid/**").anonymous()
                .antMatchers("/boyo/enterprise/list").permitAll()
                .antMatchers("/factory/order/**").permitAll()
                .antMatchers("/common/upload").permitAll()
                .antMatchers("/master/material/list").permitAll()
                .antMatchers("/mes/processDetailVo/**").permitAll()
                .antMatchers("/iot/lvzhou/**").permitAll()
                .antMatchers("/iot/byy/**").permitAll()
                .antMatchers("/iot/equipment/**").permitAll()
                .antMatchers("/qiansen/**").permitAll()
                .antMatchers("/mes/transportOrder/transportList").permitAll()
                .antMatchers("/sso/login/aio").permitAll()
                .antMatchers("/sso/login/funing").permitAll()
                .antMatchers("/dashang/**").permitAll()
                .antMatchers("/haihui/data/asset/**").permitAll()
                .antMatchers("/openapi/**").permitAll()
//                .antMatchers("/thirdparty/huaxiabank/**").permitAll()
                .and()
                // 启用Basic Auth认证
                .httpBasic()
                .and()
                .headers().frameOptions().disable();

        httpSecurity.logout().logoutUrl("/logout").logoutSuccessHandler(logoutSuccessHandler);
        // 添加JWT filter
        httpSecurity.addFilterBefore(authenticationTokenFilter, UsernamePasswordAuthenticationFilter.class);
        // 添加CORS filter
        httpSecurity.addFilterBefore(corsFilter, JwtAuthenticationTokenFilter.class);
        httpSecurity.addFilterBefore(corsFilter, LogoutFilter.class);
    }


    /**
     * 强散列哈希加密实现
     */
    @Bean
    public BCryptPasswordEncoder bCryptPasswordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * 身份认证接口
     */
//    @Override
//    protected void configure(AuthenticationManagerBuilder auth) throws Exception
//    {
//        auth.userDetailsService(userDetailsService).passwordEncoder(bCryptPasswordEncoder());
//    }
    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        auth.inMemoryAuthentication()
                .withUser("hengrun")
                .password(bCryptPasswordEncoder().encode("@HengRun_pass"))
                .roles("HENG_RUN"); // 你可以定义一个`THIRD_PARTY`角色，用来限定只能由第三方用户访问
        auth.inMemoryAuthentication()
                .withUser("huaxiaBank")
                .password(bCryptPasswordEncoder().encode("huaxiaBank22@#123"))
                .roles("HUAXIA_BANK");
    }

}
