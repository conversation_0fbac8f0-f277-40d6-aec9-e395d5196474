package com.boyo.web.controller.iot;

import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.iot.domain.IotTsl;
import com.boyo.iot.service.IIotTslService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;


/**
 * IoT物模型Controller
 *
 * <AUTHOR>
 */
@Api("IoT物模型")
@RestController
@RequestMapping("/iot/tsl")
@AllArgsConstructor
public class IotTslController extends BaseController {
    private final IIotTslService iotTslService;

    /**
     * 查询IoT物模型列表
     */
    @ApiOperation("查询IoT物模型列表")
    @GetMapping("/list")
    public TableDataInfo list(IotTsl iotTsl) {
        startPage();
        List<IotTsl> list = iotTslService.selectIotTslList(iotTsl);
        return getDataTable(list);
    }

    /**
     * 获取IoT物模型详细信息
     */
    @ApiOperation("获取IoT物模型详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(iotTslService.getById(id));
    }

    /**
     * 新增IoT物模型
     */
    @ApiOperation("新增IoT物模型")
    @PostMapping
    public AjaxResult add(@RequestBody IotTsl iotTsl) {
        return toBooleanAjax(iotTslService.save(iotTsl));
    }

    /**
     * 修改IoT物模型
     */
    @ApiOperation("修改IoT物模型")
    @PutMapping
    public AjaxResult edit(@RequestBody IotTsl iotTsl) {
        return toBooleanAjax(iotTslService.updateById(iotTsl));
    }

    /**
     * 删除IoT物模型
     */
    @ApiOperation("删除IoT物模型")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(iotTslService.removeByIds(Arrays.asList(ids)));
    }
}
