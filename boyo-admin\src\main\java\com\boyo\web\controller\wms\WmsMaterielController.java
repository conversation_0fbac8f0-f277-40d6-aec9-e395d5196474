package com.boyo.web.controller.wms;

import java.util.List;
import java.util.Arrays;

import com.boyo.wms.vo.WmsMaterielVO;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.boyo.wms.entity.WmsMateriel;
import com.boyo.wms.service.IWmsMaterielService;

/**
 * 出入库计划明细Controller
 *
 * <AUTHOR>
 */
@Api("出入库计划明细")
@RestController
@RequestMapping("/wms/materiel")
@AllArgsConstructor
public class WmsMaterielController extends BaseController {

    private final IWmsMaterielService wmsMaterielService;

    /**
     * 查询出入库计划明细列表
     */
    @ApiOperation("查询出入库计划明细列表")
    @GetMapping("/list")
    public TableDataInfo list(WmsMateriel wmsMateriel) {
        startPage();
        List<WmsMaterielVO> list = wmsMaterielService.selectWmsMaterielList(wmsMateriel);
        return getDataTable(list);
    }

    /**
     * 获取出入库计划明细详细信息
     */
    @ApiOperation("获取出入库计划明细详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(wmsMaterielService.getById(id));
    }

    /**
     * 新增出入库计划明细
     */
    @ApiOperation("新增出入库计划明细")
    @PostMapping
    public AjaxResult add(@RequestBody WmsMateriel wmsMateriel) {
        return toBooleanAjax(wmsMaterielService.save(wmsMateriel));
    }

    /**
     * 修改出入库计划明细
     */
    @ApiOperation("修改出入库计划明细")
    @PutMapping
    public AjaxResult edit(@RequestBody WmsMateriel wmsMateriel) {
        return toBooleanAjax(wmsMaterielService.updateById(wmsMateriel));
    }

    /**
     * 删除出入库计划明细
     */
    @ApiOperation("删除出入库计划明细")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(wmsMaterielService.removeByIds(Arrays.asList(ids)));
    }
}
