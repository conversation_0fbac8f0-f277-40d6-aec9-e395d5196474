package com.boyo.web.controller.eam;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.utils.DateUtils;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.common.utils.poi.ExcelUtil;
import com.boyo.eam.domain.*;
import com.boyo.eam.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.beans.Transient;
import java.util.*;

/**
 * 点检-项目(EquipInspectionSpotItem)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-15 16:16:30
 */
@Api("点检-项目")
@RestController
@RequestMapping("/equip/equipInspectionSpotItem")
@AllArgsConstructor
public class EquipInspectionSpotItemController extends BaseController{
    /**
     * 服务对象
     */
    private final IEquipInspectionSpotItemService equipInspectionSpotItemService;
    private final IEquipInspectionSpotService equipInspectionSpotService;
    private final IMediaService mediaService;
    private final IEquipInspectionRecordService inspectionRecordService;

    private final IEquipLedgerService equipLedgerService;


    /**
     * 查询点检-项目列表
     *
     */
    @ApiOperation("查询点检-项目列表")
    @GetMapping("/list")
    public TableDataInfo list(EquipInspectionSpotItem equipInspectionSpotItem) {
        startPage();
        List<EquipInspectionSpotItem> list = equipInspectionSpotItemService.selectEquipInspectionSpotItemList(equipInspectionSpotItem);
        return getDataTable(list);
    }

    /**
     * 获取点检-项目详情
     */
    @ApiOperation("获取点检-项目详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(equipInspectionSpotItemService.getById(id));
    }

    /**
     * 新增点检-项目
     */
    @ApiOperation("新增点检-项目")
    @PostMapping
    public AjaxResult add(@RequestBody EquipInspectionSpotItem equipInspectionSpotItem) {
        return toBooleanAjax(equipInspectionSpotItemService.save(equipInspectionSpotItem));
    }

    /**
     * 修改点检-项目
     */
    @ApiOperation("修改点检-项目")
    @PutMapping
    public AjaxResult edit(@RequestBody EquipInspectionSpotItem equipInspectionSpotItem) {
        return toBooleanAjax(equipInspectionSpotItemService.updateById(equipInspectionSpotItem));
    }

    /**
     * 删除点检-项目
     */
    @ApiOperation("删除点检-项目")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(equipInspectionSpotItemService.removeByIds(Arrays.asList(ids)));
    }


    // 移动端

    /**
     * 查询点检-项目列表
     *
     */
    @ApiOperation("查询点检-项目列表")
    @GetMapping("/pad/list")
    public TableDataInfo padList(EquipInspectionSpotItem equipInspectionSpotItem) {
        startPage();
        List<EquipInspectionSpotItem> list = equipInspectionSpotItemService.selectEquipInspectionSpotItemList(equipInspectionSpotItem);
        return getDataTable(list);
    }

    /**
     * 获取点检-项目详情
     */
    @ApiOperation("获取点检-项目详情")
    @GetMapping(value = "/pad/{id}")
    public AjaxResult padGetInfo(@PathVariable("id") Integer id) {
        EquipInspectionSpotItem item = equipInspectionSpotItemService.getItemAndRecord(id);
        if (item!=null){
            // 通过记录中的图片id查询图片地址
            String mediaIds = item.getMediaId();
            if (mediaIds!=null && !"".equals(mediaIds)){
                List<String> mediaUrls = new ArrayList<>();
                String[] mediaIdArray = mediaIds.split(",");
                List<Media> mediaList = mediaService.list(
                        Wrappers.<Media>lambdaQuery()
                                .in(Media::getId, mediaIdArray)
                );
                for (Media media:mediaList){
                    mediaUrls.add(media.getUrl());
                }
                item.setMediaUrls(mediaUrls);
            }
            return AjaxResult.success(item);
        }
        return AjaxResult.error("未找到id");
    }

//临时导入，需优化
    @ApiOperation("导入点检")
    @PostMapping("/importAdd")
    @Transient
    public AjaxResult importAdd(MultipartFile file) throws Exception {
        ExcelUtil<EquipInspectionSpotItem> util = new ExcelUtil<>(EquipInspectionSpotItem.class);
        List<EquipInspectionSpotItem> equipInspectionSpotItemList = util.importExcel(file.getInputStream());

        for (EquipInspectionSpotItem item:equipInspectionSpotItemList){
//            item.setCreateBy(SecurityUtils.getUsername());
            item.setCreateTime(new Date());
            final Date inspectionDate = item.getInspectionDate();

            item.setOpenid(inspectionDate.getYear()+""+inspectionDate.getMonth()+""+inspectionDate.getDay()+ UUID.randomUUID().toString().substring(0,7));
            item.setEquipInspectionSpotOpenid(UUID.randomUUID().toString());
            final int i0 = equipInspectionSpotItemService.insertEquipInspectionRecord(item);

            EquipInspectionRecord equipInspectionRecord = new EquipInspectionRecord();
            equipInspectionRecord.setInspectionDate(item.getInspectionDate());
            equipInspectionRecord.setPass("通过".equals(item.getPass()) ? "1" : "0");
            equipInspectionRecord.setRemark(item.getRecordRemark());
            equipInspectionRecord.setMediaId(item.getMediaId());
//            equipInspectionRecord.setCreateBy(SecurityUtils.getUsername());
            equipInspectionRecord.setCreateTime(new Date());

            equipInspectionRecord.setEquipInspectionSpotItemOpenid(item.getOpenid()); //关联

            Boolean aBoolean = inspectionRecordService.insertEquipInspectionRecord(equipInspectionRecord);


            EquipInspectionSpot equipInspectionSpot =new EquipInspectionSpot();
            equipInspectionSpot.setOpenid(item.getEquipInspectionSpotOpenid());
            equipInspectionSpot.setCode(item.getSpotCode());
            equipInspectionSpot.setEquipLedgerOpenid(UUID.randomUUID().toString().replace("-", ""));
            equipInspectionSpot.setCreateTime(new Date());

            final int i1 = equipInspectionSpotService.insertEquipInspectionSpotList(equipInspectionSpot);

            EquipLedger equipLedger = new EquipLedger();
            equipLedger.setOpenid(equipInspectionSpot.getEquipLedgerOpenid());
            equipLedger.setCreateTime(new Date());
            equipLedger.setCode(item.getSpotCode());
            equipLedger.setName(item.getEquipName());
            final int i2 = equipLedgerService.insertEquipLedger(equipLedger);

//            if(i0>0&&i1>0&&i2>0&&aBoolean){
//
//            }
        }
        return AjaxResult.success();
    }

}
