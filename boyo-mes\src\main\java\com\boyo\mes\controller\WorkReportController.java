package com.boyo.mes.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.common.utils.poi.ExcelUtil;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.service.IIotEquipmentService;
import com.boyo.mes.entity.*;
import com.boyo.mes.mapper.ProductOrderDetailMapper;
import com.boyo.mes.mapper.ProductOrderMapper;
import com.boyo.mes.service.IProcessGroupService;
import com.boyo.mes.service.IProductOrderService;
import com.boyo.mes.service.IProductProcessService;
import com.boyo.mes.service.IWorkReportService;
import com.boyo.system.service.IEnterpriseUserService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static jdk.nashorn.internal.runtime.regexp.joni.Config.log;

/**
 * 报工记录(WorkReport)表控制层
 *
 * <AUTHOR>
 * @since 2023-03-03 16:45:28
 */
@Api("报工记录")
@RestController
@Slf4j
@RequestMapping("/mes/workReport")
@AllArgsConstructor
public class WorkReportController extends BaseController {
    /**
     * 服务对象
     */
    private final IWorkReportService workReportService;

    private final IProductProcessService productProcessService;
    private final IIotEquipmentService iotEquipmentService;
    private final IEnterpriseUserService enterpriseUserService;

    @Autowired
    private final IProductOrderService productOrderService;

    /**
     * 查询报工记录列表
     */
    @ApiOperation("查询报工记录列表")
    @GetMapping("/list")
    public TableDataInfo list(WorkReport workReport) {
        startPage();
        List<WorkReport> list = workReportService.selectWorkReportList(workReport);
        return getDataTable(list);
    }

    /**
     * 获取报工记录详情
     */
    @ApiOperation("获取报工记录详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(workReportService.getById(id));
    }

    /**
     * 新增报工记录
     */
    @ApiOperation("导入报工记录")
    @PostMapping("/importAdd")
    public AjaxResult importAdd(MultipartFile file) throws Exception {
        ExcelUtil<WorkReport> util = new ExcelUtil<>(WorkReport.class);
        List<WorkReport> workReportList = util.importExcel(file.getInputStream());

        if (CollectionUtils.isEmpty(workReportList)) {
            return AjaxResult.error("导入数据为空");
        }
        int successCount = 0;
        int failCount = 0;
        // 构建映射表
        Map<String, Integer> processMap = productProcessService.selectProductProcessList(new ProductProcess())
                .stream().collect(Collectors.toMap(ProductProcess::getProcessName, ProductProcess::getId));

        Map<String, Long> equipmentMap = iotEquipmentService.selectIotEquipmentListOnly(new IotEquipment())
                .stream().collect(Collectors.toMap(IotEquipment::getEquipmentName, IotEquipment::getId));

        List<EnterpriseUser> userListlist = enterpriseUserService.selectEnterpriseUserList(new EnterpriseUser());
        Map<String, Long> userMap = new HashMap<>();
        for (EnterpriseUser enterpriseUser : userListlist) {
            userMap.put(enterpriseUser.getUserName(), enterpriseUser.getId());
        }

        StringBuilder failMsg = new StringBuilder();
        List<WorkReport> validReports = new ArrayList<>();

        for (WorkReport workReport : workReportList) {
            try {
                // 查询订单
                ProductOrder query = new ProductOrder();
                query.setOrderNum(workReport.getOrderNum());
                final List<ProductOrder> productOrders = productOrderService.selectProductOrderList(query);
                if (productOrders == null || productOrders.isEmpty()) {
                    log.warn("订单编号未找到: {}", workReport.getOrderNum());
                    failMsg.append("订单编号未找到: ").append(workReport.getOrderNum()+workReport.getProcessName()+workReport.getEquipmentName()+workReport.getUserName()).append("<br>");
                    failCount++;
                    continue;
                }
                workReport.setOrderId(productOrders.get(0).getId());

                // 解析并设置其它字段
                Integer processId = processMap.get(workReport.getProcessName());
                Long equipmentId = equipmentMap.get(workReport.getEquipmentName());
                Long userId = userMap.get(workReport.getUserName());

                if (processId == null || equipmentId == null || userId == null) {
                    log.warn("工序映射缺失: {}", workReport);
                    failMsg.append("工序映射缺失: ").append(workReport.getOrderNum()+workReport.getProcessName()+workReport.getEquipmentName()).append("<br>");
                    failCount++;
                    continue;
                }

                workReport.setProcessId(processId);
                workReport.setEquipmentId(equipmentId.intValue());
                workReport.setUserId(userId.intValue());
                workReport.setQualityInspector(workReport.getUserName());

                validReports.add(workReport);
                successCount++;
            } catch (Exception e) {
                log.error("处理记录异常: {}", workReport, e);
                failMsg.append("处理记录异常: ").append(workReport.getOrderNum()+workReport.getProcessName()+workReport.getEquipmentName()+workReport.getUserName()).append("<br>");
                failCount++;
            }
        }

        // 批量保存
        if (!validReports.isEmpty()) {
            workReportService.saveBatch(validReports);
        }

        return AjaxResult.success(String.format("导入成功：%d 条，失败：%d 条  失败的：%s", successCount, failCount,failMsg.toString()));
    }


    /**
     * 修改报工记录
     */
    @ApiOperation("修改报工记录")
    @PutMapping
    public AjaxResult edit(@RequestBody WorkReport workReport) {
        return toBooleanAjax(workReportService.updateById(workReport));
    }

    /**
     * 删除报工记录
     */
    @ApiOperation("删除报工记录")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(workReportService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 获取订单报工详情
     *
     * @return
     */
    @GetMapping("/getOrderReportDetail")
    public AjaxResult getOrderReportDetail(Integer id) {
        return AjaxResult.success(workReportService.getOrderReportDetail(id));
    }

}
