package com.boyo.web.isv.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.web.isv.mapper.IsvProduceMapper;
import com.boyo.web.isv.model.*;
import com.boyo.web.isv.service.IsvProduceService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

@Service
public class IsvProduceServiceImpl implements IsvProduceService {

    @Resource
    private IsvProduceMapper isvProduceMapper;

    @Override
    public GetLicenseInfo getLicense(GetLicenseReq getLicenseReq) {
        GetLicenseInfo getLicenseInfo = new GetLicenseInfo();

        //判断数据库中是否已经存在相应客户的license
        GetLicenseReq customer = isvProduceMapper.selectOne(new QueryWrapper<GetLicenseReq>().eq("customer_id", getLicenseReq.getCustomerId()));
        if (ObjectUtils.isNotEmpty(customer)){
            getLicenseInfo.setInstanceId(customer.getBusinessId());
            getLicenseInfo.setLicense(customer.getLicense());
            return getLicenseInfo;
        }
        /*
         * 实例ID，服务商提供的唯一标识。 建议此ID直接使用该订单首次请求时云商店传入的businessId，以确保instanceId的唯一性。 说明：
         * 云商店每次请求时的businessId皆不一致，如取businessId作为instanceId，取订单首次调用时的businessId即可。伙伴也可以根据自己的逻辑来生成instanceId，保证唯一性。
         * 若通过其他方式生成instanceId，请确保该参数的全局唯一性，例如使用UUID生成。避免生成的instanceId发生重复，导致用户开通License实例失败。
         */
        getLicenseInfo.setInstanceId(getLicenseReq.getBusinessId());
        // 以下代码仅是示例；伙伴生成license的算法需要根据自己的业务逻辑来决定，可以结合入参的productId、skuCode、orderId等参数。
        String license = createLicense(32);
        getLicenseInfo.setLicense(license);

        getLicenseReq.setLicense(license);
        isvProduceMapper.insert(getLicenseReq);
        return getLicenseInfo;
    }

    @Override
    public boolean refreshLicense(RefreshLicenseReq refreshLicenseReq) {
        // 伙伴在这里处理续费的逻辑
        return true;
    }

    @Override
    public boolean expireLicense(ExpireLicenseReq expireLicenseReq) {
        // 伙伴在这里处理过期的逻辑
        return true;
    }

    @Override
    public boolean releaseLicense(ReleaseLicenseReq releaseLicenseReq) {
        // 伙伴在这里处理资源释放的逻辑
        GetLicenseReq instanceOrder = isvProduceMapper.selectOne(new QueryWrapper<GetLicenseReq>()
                .eq("business_id", releaseLicenseReq.getInstanceId())
                .eq("order_id",releaseLicenseReq.getOrderId()));
        if (ObjectUtils.isNotEmpty(instanceOrder)) {
            int i = isvProduceMapper.deleteById(instanceOrder.getId());
            return i==1?true:false;
        }
        return false;
    }

    /**
     * 生成随机长度license，以下代码仅是示例；伙伴生成license的算法需要根据自己的业务逻辑来决定，可以结合入参的productId、skuCode、orderId等参数。
     *
     * @param n license长度
     * @return 随机license
     */
    private static String createLicense(int n) {
        List<Character> chars =
                Arrays.asList('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U',
                        'V', 'W', 'X', 'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'm', 'n', 'p', 'q', 'r',
                        's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9');

        Random random = new Random(); // 创建random对象

        StringBuffer buff = new StringBuffer(); // StringBuffer
        for (int i = 0; i < n; i++) {
            int it = random.nextInt(chars.size()); // 使用random生成随机数,且范围在chars.size()范围内
            buff.append(chars.get(it)); // 通过int下标获取chars中随机字符(数字，大写字母或者小写字母)
        }
        return buff.toString();
    }
}
