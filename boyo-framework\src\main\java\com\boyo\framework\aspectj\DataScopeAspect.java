package com.boyo.framework.aspectj;

import java.lang.reflect.Method;
import java.util.List;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.boyo.common.core.domain.BoyoBaseEntity;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.framework.datasource.DynamicDataSourceContextHolder;
import com.boyo.system.domain.EnterpriseRole;
import com.boyo.system.domain.EnterpriseUserRole;
import com.boyo.system.service.IEnterpriseDepartmentService;
import com.boyo.system.service.IEnterpriseUserRoleService;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import com.boyo.common.annotation.DataScope;
import com.boyo.common.core.domain.BaseEntity;
import com.boyo.common.core.domain.entity.SysRole;
import com.boyo.common.core.domain.entity.SysUser;
import com.boyo.common.core.domain.model.LoginUser;
import com.boyo.common.utils.ServletUtils;
import com.boyo.common.utils.StringUtils;
import com.boyo.common.utils.spring.SpringUtils;
import com.boyo.framework.web.service.TokenService;

/**
 * 数据过滤处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class DataScopeAspect {
    /**
     * 全部数据权限
     */
    public static final String DATA_SCOPE_ALL = "1";

    /**
     * 自定数据权限
     */
    public static final String DATA_SCOPE_CUSTOM = "2";

    /**
     * 部门数据权限
     */
    public static final String DATA_SCOPE_DEPT = "3";

    /**
     * 部门及以下数据权限
     */
    public static final String DATA_SCOPE_DEPT_AND_CHILD = "4";

    /**
     * 仅本人数据权限
     */
    public static final String DATA_SCOPE_SELF = "5";

    /**
     * 数据权限过滤关键字
     */
    public static final String DATA_SCOPE = "dataScope";

    // 配置织入点
    @Pointcut("@annotation(com.boyo.common.annotation.DataScope)")
    public void dataScopePointCut() {
    }

    @Before("dataScopePointCut()")
    public void doBefore(JoinPoint point) throws Throwable {
        handleDataScope(point);
    }

    protected void handleDataScope(final JoinPoint joinPoint) {
        // 获得注解
        DataScope controllerDataScope = getAnnotationLog(joinPoint);
        if (controllerDataScope == null) {
            return;
        }
        // 获取当前的用户
        LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
//
        if (StringUtils.isNotNull(loginUser)) {
            EnterpriseUser currentUser = loginUser.getEnterpriseUser();
            // 如果是超级管理员，则不过滤数据
            if (StringUtils.isNotNull(currentUser) && !currentUser.getUserAdmin().equals(1L)) {
                dataScopeFilter(joinPoint, currentUser, controllerDataScope.deptAlias(),
                        controllerDataScope.userAlias(), controllerDataScope.columns());
            }
        }
    }

    /**
     * 数据范围过滤
     *
     * @param joinPoint 切点
     * @param user      用户
     * @param userAlias 别名
     */
    public static void dataScopeFilter(JoinPoint joinPoint, EnterpriseUser user, String deptAlias, String userAlias, String columns) {
//        System.out.println(joinPoint.getSignature().getDeclaringTypeName());
        boolean crm = false;
        if(joinPoint.getSignature().getDeclaringTypeName().contains("com.boyo.crm")){
            crm = true;
        }
        StringBuilder sqlString = new StringBuilder();
        EnterpriseUserRole temp = new EnterpriseUserRole();
        temp.setUserOpenid(user.getUserOpenid());
        user.getDepartmentOpenid();
        List<EnterpriseUserRole> roleList = SpringUtils.getBean(IEnterpriseUserRoleService.class).selectEnterpriseUserRoleList(temp);
        for (EnterpriseUserRole role : roleList) {
            String dataScope = role.getDataScope();
            if (DATA_SCOPE_ALL.equals(dataScope) || StrUtil.isEmpty(dataScope)) {
                sqlString = new StringBuilder();
                break;
            }
//            else if (DATA_SCOPE_CUSTOM.equals(dataScope))
//            {
//                sqlString.append(StringUtils.format(
//                        " OR {}.dept_id IN ( SELECT dept_id FROM sys_role_dept WHERE role_id = {} ) ", deptAlias,
//                        role.getRoleId()));
//            }
            else if (DATA_SCOPE_DEPT.equals(dataScope) && !crm) {
                if (StringUtils.isNotBlank(userAlias)) {
                    sqlString.append(StringUtils.format(" OR {}.dept_id = '{}' ", userAlias,user.getDepartmentOpenid()));
                }else{
                    sqlString.append(StringUtils.format(" OR dept_id = '{}' ", user.getDepartmentOpenid()));
                }
            } else if (DATA_SCOPE_DEPT_AND_CHILD.equals(dataScope) && !crm) {
                List<String> deptList = SpringUtils.getBean(IEnterpriseDepartmentService.class).getDeptAndChildren(user.getDepartmentOpenid());
                if (StringUtils.isNotBlank(userAlias)) {

                    sqlString.append(StringUtils.format(
                            " OR {}.dept_id IN ('" +
                                    StrUtil.join("','", deptList) +
                                    "')",userAlias,
                            user.getDepartmentOpenid(), user.getDepartmentOpenid()));
                }else {
                    sqlString.append(StringUtils.format(
                            " OR dept_id IN ('" +
                                    StrUtil.join("','", deptList) +
                                    "')",
                            user.getDepartmentOpenid(), user.getDepartmentOpenid()));
                }
            } else if (DATA_SCOPE_SELF.equals(dataScope) && crm) {
                if (StringUtils.isNotBlank(userAlias)) {
                    sqlString.append(StringUtils.format(" OR {}.user_id = {} ", userAlias, user.getUserAdmin()));
                } else if (StrUtil.isNotEmpty(columns)) {
                    String[] cols = columns.split(",");
                    sqlString.append(" OR (");
                    int i = 0;
                    for (String col : cols) {
                        if (i > 0) {
                            sqlString.append(" or ");
                        }
                        sqlString.append(col).append("=").append(user.getId()).append(" or ").append(col).append("='").append(user.getUserOpenid()).append("'").append(" ");
                        i++;
                    }
                    sqlString.append(")");
                    // 数据权限为仅本人且没有userAlias别名不查询任何数据
//                    sqlString.append(StringUtils.format(" OR ({} = {} or create_user_id= {})", columns,user.getId(),user.getId()));
                } else {
                    sqlString.append(" OR 1=0 ");
                }
            }
        }

        if (StringUtils.isNotBlank(sqlString.toString())) {
            Object params = joinPoint.getArgs()[0];
            if (StringUtils.isNotNull(params) && params instanceof BoyoBaseEntity) {
                BoyoBaseEntity baseEntity = (BoyoBaseEntity) params;
                baseEntity.getParams().put(DATA_SCOPE, " AND (" + sqlString.substring(4) + ")");
            }
        }
    }

    /**
     * 是否存在注解，如果存在就获取
     */
    private DataScope getAnnotationLog(JoinPoint joinPoint) {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();

        if (method != null) {
            return method.getAnnotation(DataScope.class);
        }
        return null;
    }
}
