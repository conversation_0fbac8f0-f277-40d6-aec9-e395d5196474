package com.boyo.web.isv.model;

public class RefreshLicenseReq {

    /**
     * 云商店订单ID。
     * 续费操作会产生新的订单，与新购时订单ID不一致，请通过instanceId做资源识别。
     * 必返回
     */
    private String orderId;

    /**
     * 实例ID。
     * 必返回
     */
    private String instanceId;

    /**
     * 产品标识，租户续费或转正产品实例时，如果订购周期类型发生变化，会传入变化后的产品类型对应的productId。
     * 非必返回
     */
    private String productId;

    /**
     * 过期时间。
     * 格式：yyyyMMddHHmmss
     * 必返回
     */
    private String expireTime;

    /**
     * 安全校验令牌。
     * 必返回
     */
    private String authToken;

    /**
     * 请求发起时的时间戳，取UTC时间。
     * 格式：yyyyMMddHHmmssSSS
     * 必返回
     */
    private String timeStamp;

    /**
     * 周期类型。
     * 非必传，如需此参数，计费类型需选择包周期chargingMode=1，包周期购买场景请求时传该参数。
     * 年："year"
     * 月："month"
     */
    private String periodType;

    /**
     * 周期数量。
     * 非必传，如需此参数，计费类型需选择包周期chargingMode=1，包周期购买场景请求时传该参数。
     * 周期数量：1,2,3…
     */
    private Integer periodNumber;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(String expireTime) {
        this.expireTime = expireTime;
    }

    public String getAuthToken() {
        return authToken;
    }

    public void setAuthToken(String authToken) {
        this.authToken = authToken;
    }

    public String getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(String timeStamp) {
        this.timeStamp = timeStamp;
    }

    public String getPeriodType() {
        return periodType;
    }

    public void setPeriodType(String periodType) {
        this.periodType = periodType;
    }

    public Integer getPeriodNumber() {
        return periodNumber;
    }

    public void setPeriodNumber(Integer periodNumber) {
        this.periodNumber = periodNumber;
    }
}
