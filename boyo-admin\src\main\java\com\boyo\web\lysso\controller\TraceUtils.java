package com.boyo.web.lysso.controller;

import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.UUID;

public class TraceUtils {

    public static String newtrace() {
        return UUID.randomUUID().toString().replace("-", "").toUpperCase();
    }

    public static String getTraceId(HttpServletRequest request) {
        String trace;
        if (request != null) {
            trace = request.getHeader("trace");
        } else {
            trace = "";
        }
        return StringUtils.isBlank(trace) ? newtrace().toUpperCase() : trace.toUpperCase().replace("-", "");
    }
}