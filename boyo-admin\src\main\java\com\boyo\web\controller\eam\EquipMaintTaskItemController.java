package com.boyo.web.controller.eam;

import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.eam.domain.EquipMaintTaskItem;
import com.boyo.eam.domain.EquipMaintTaskItemDetail;
import com.boyo.eam.service.IEquipMaintTaskItemDetailService;
import com.boyo.eam.service.IEquipMaintTaskItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 维保任务管理-维保项目(EquipMaintTaskItem)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-15 09:18:33
 */
@Api("维保任务管理-维保项目")
@RestController
@RequestMapping("/equip/equipMaintTaskItem")
@AllArgsConstructor
public class EquipMaintTaskItemController extends BaseController{
    /**
     * 服务对象
     */
    private final IEquipMaintTaskItemService equipMaintTaskItemService;
    private final IEquipMaintTaskItemDetailService equipMaintTaskItemDetailService;
    /**
     * 查询维保任务管理-维保项目列表
     *
     */
    @ApiOperation("查询维保任务管理-维保项目列表")
    @GetMapping("/list")
    public TableDataInfo list(EquipMaintTaskItem equipMaintTaskItem) {
        startPage();
        List<EquipMaintTaskItem> list = equipMaintTaskItemService.selectEquipMaintTaskItemList(equipMaintTaskItem);
        return getDataTable(list);
    }
    
    /**
     * 获取维保任务管理-维保项目详情
     */
    @ApiOperation("获取维保任务管理-维保项目详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(equipMaintTaskItemService.getById(id));
    }

    /**
     * 新增维保任务管理-维保项目
     */
    @ApiOperation("新增维保任务管理-维保项目")
    @PostMapping
    public AjaxResult add(@RequestBody EquipMaintTaskItem equipMaintTaskItem) {
        return toBooleanAjax(equipMaintTaskItemService.save(equipMaintTaskItem));
    }

    /**
     * 修改维保任务管理-维保项目
     */
    @ApiOperation("修改维保任务管理-维保项目")
    @PutMapping
    public AjaxResult edit(@RequestBody EquipMaintTaskItem equipMaintTaskItem) {
        return toBooleanAjax(equipMaintTaskItemService.updateById(equipMaintTaskItem));
    }

    /**
     * 删除维保任务管理-维保项目
     */
    @ApiOperation("删除维保任务管理-维保项目")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(equipMaintTaskItemService.removeByIds(Arrays.asList(ids)));
    }

    // 移动端接口

    /**
     * 查询维保任务管理-维保项目列表
     *
     */
    @ApiOperation("查询维保任务管理-维保项目列表-移动端")
    @GetMapping("/pad/list")
    public TableDataInfo padList(EquipMaintTaskItem equipMaintTaskItem) {
        startPage();
        List<EquipMaintTaskItem> list = equipMaintTaskItemService.selectEquipMaintTaskItemList(equipMaintTaskItem);
        if (list!=null&&list.size()>0){
            for (EquipMaintTaskItem emti:list){
                EquipMaintTaskItemDetail detail = new EquipMaintTaskItemDetail();
                detail.setEquipMaintTaskItemOpenid(emti.getOpenid());
                List<EquipMaintTaskItemDetail> detailList = equipMaintTaskItemDetailService.selectEquipMaintTaskItemDetailList(detail);
                emti.setDetailList(detailList);
            }
        }
        return getDataTable(list);
    }
}
