package com.boyo.web.controller.eam;

import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.eam.domain.EquipWorkshopSection;
import com.boyo.eam.service.IEquipWorkshopSectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 工段表(EquipWorkshopSection)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-21 15:23:24
 */
@Api("工段表")
@RestController
@RequestMapping("/equip/equipWorkshopSection")
@AllArgsConstructor
public class EquipWorkshopSectionController extends BaseController{
    /**
     * 服务对象
     */
    private final IEquipWorkshopSectionService equipWorkshopSectionService;

    /**
     * 查询工段表列表
     *
     */
    @ApiOperation("查询工段表列表")
    @GetMapping("/list")
    public TableDataInfo list(EquipWorkshopSection equipWorkshopSection) {
        startPage();
        List<EquipWorkshopSection> list = equipWorkshopSectionService.selectEquipWorkshopSectionList(equipWorkshopSection);
        return getDataTable(list);
    }
    
    /**
     * 获取工段表详情
     */
    @ApiOperation("获取工段表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(equipWorkshopSectionService.getById(id));
    }

    /**
     * 新增工段表
     */
    @ApiOperation("新增工段表")
    @PostMapping
    public AjaxResult add(@RequestBody EquipWorkshopSection equipWorkshopSection) {
        equipWorkshopSection.setOpenid(generateOpenid());
        return toBooleanAjax(equipWorkshopSectionService.save(equipWorkshopSection));
    }

    /**
     * 修改工段表
     */
    @ApiOperation("修改工段表")
    @PutMapping
    public AjaxResult edit(@RequestBody EquipWorkshopSection equipWorkshopSection) {
        return toBooleanAjax(equipWorkshopSectionService.updateById(equipWorkshopSection));
    }

    /**
     * 删除工段表
     */
    @ApiOperation("删除工段表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(equipWorkshopSectionService.removeByIds(Arrays.asList(ids)));
    }

}
