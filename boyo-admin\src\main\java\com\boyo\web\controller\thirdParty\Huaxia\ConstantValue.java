package com.boyo.web.controller.thirdParty.Huaxia;

import org.apache.poi.ss.formula.functions.T;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ConstantValue {


    public static final Map<String, String> companyMap = new HashMap<String, String>() {{
        put("91620000591232929H", "7ce60720b9c74ca8afe4a186a2e10382");//甘肃林涛表面科技有限公司
        put("91131121734341656U", "value1");//恒润集团有限公司
        put("91370222MA3MK5WF1F", "20ce7f7b569c4053a6255e217b225520");//华铁西屋法维莱（青岛）交通设备有限公司
        put("91370212747210962T", "db5b6a8efb249e18813443ecf2e01c0");//青岛海永昌电子科技股份有限公司
        put("91370281MA3M9BD04G", "4611e39add484b7f947b8e8aaa950885");//青岛新奥包装制品有限公司
        put("91370211MA3CJQ7290", "02d894e438924e04aafc83b0e02a5d86");//山东百德瑞轨道交通科技有限公司
        put("91370283325991455T", "8c8126332cbd49c489ca4906299d85e7");//青岛库曼电器有限公司
        put("9137022233405384XK", "111");//青岛德尔通用环保科技有限公司
        put("91370281065084882B", "57cfc8539eb74c898ea61c906a528fce");//青岛方泽机械科技有限公司
        put("91370214MA3DANCLXE", "865c516387404bd1aa6f1303a1e0e28e");//青岛凯创电器有限公司
        put("91370282770262453W", "b2dc369d4e0e40a3ac1cdd5054037d5b");//青岛龙翔机械制造有限公司
        put("913702810572539659", "2431f6c420304c6e81a1c52a550d1ba6");//青岛海德立餐饮设备股份有限公司
        put("91370285MA3N08EW6A", "436e63fdb47442419465a60d441fd35e");//青岛泰禾祥机械有限公司
        put("9137028206107445X3", "9f4b17fa8f6346afac506326bcad281c");//青岛君联汽车部件有限公司
        put("91370281081406140F", "2e4a7b7e96be4574aef6f1dbab7660f1");//青岛浩晓工贸有限公司
        put("91370214553997453J", "b880be617ba45629033ae5af382b6aa");//青岛大上电器有限公司
        put("913304027477249474", "29f8fb7752134221a86c65875cebb60c");//浙江格兰德机械有限公司
        put("91370212MA3MMYH62F", "740f9f5e79d940d4afbeae6fff138a34");//安禾（山东）医疗科技有限公司
        put("91320923MA7KAYEJXA", "740f9f5e79d940d4afbeae6fff138a34");//江苏雨轩复合新材料有限公司
        put("91370281MA3CTJT401", "3156797c21ad4d0d9f997572d3d4d40f");//青岛天源洁能装备制造有限公司
        put("91370784705999336H", "18201d19a33d4446b7521c3f027bc80a");//潍坊天洁环保科技有限公司
        put("91370211773531196A", "1d3798f0129a455ab55feee176dad3bc");//青岛振海船舶设备有限公司
        put("91341222MA2UH83F4A", "ef98b56d733045c9b9c13edfa02e528f");//太和县薇薇安营养食品有限公司
        put("913702825797893537", "642de3bba8484fe8ab7d7dbf86324310");//青岛宏泰良正电器有限公司
        put("913702810572553039", "ac8291d3d30c4e50b0197c0b6845aed8");//青岛铸辉铜业有限公司

    }};


    public static final Map<String, List<String>> companyDeviceMap = new HashMap<String, List<String>>() {{
        put("91620000591232929H", new java.util.ArrayList<String>() {{
            add("E677A2E7DE4B04D6BB06C6CF7");
            add("E66F904BEE4B03B8071C97C66");
            add("E66F28173E4B03B8071C97C45");
            add("E66F25A73E4B03B8071C97C44");
            add("E66CE9987E4B0DAA192B8B13B");
            add("E66BB01C2E4B0874452034CC9");
            add("E65FBCA01E4B09F5FE26220E4");
            add("E65FBC9B3E4B09F5FE26220DE");
            add("E65FBC9A6E4B09F5FE26220DD");
            add("E65FBC996E4B09F5FE26220DC");
            add("E65FBC97CE4B09F5FE26220DA");
            add("E65FBC950E4B09F5FE26220D7");
            add("E65FBC945E4B09F5FE26220D6");
            add("E65FBC924E4B09F5FE26220D4");
            add("E65FBC918E4B09F5FE26220D3");
            add("E65FBC8EBE4B09F5FE26220CF");
            add("E65FBC8CCE4B09F5FE26220CE");
            add("E6556F7D7E4B09F5FD090112C");
        }});
        put("91370202756930005Q", new java.util.ArrayList<String>() {{
            add("E675FDCFFE4B03B804AA35520");
            add("E675FDCFFE4B03B80B8C735E9");
            add("E675FDCFFE4B03B804AA35521");
            add("E675FDCFFE4B03B80B8C735EA");
            add("E675FDCFFE4B04D6BB06C6CDD");
            add("E675FDCFFE4B03B804AA35522");
            add("E675FDCFFE4B03B80C2DFAFB8");
            add("E675FDCFFE4B03B80B8C735EB");
            add("E675FDCFFE4B04D6BB06C6CDE");
            add("E675FDCFFE4B03B80C2DFAFB9");
            add("E675FDCFFE4B04D6BB06C6CDF");
            add("E675FDCFFE4B03B804AA35523");
            add("E675FDCFFE4B03B80C2DFAFBA");
            add("E675FDCFFE4B03B804AA35524");
            add("E675FDCFFE4B03B80C2DFAFBB");
            add("E675FDCFFE4B03B80B8C735EC");
            add("E675FDCFFE4B03B804AA35525");
            add("E675FDCFFE4B04D6BB06C6CE0");
            add("E675FDD00E4B03B80B8C735ED");
            add("E675FDD00E4B03B804AA35526");
            add("E675FDD00E4B03B80B8C735EE");
            add("E675FDD00E4B04D6BB06C6CE1");
            add("E675FDCFFE4B03B80C2DFAFB6");
            add("E675FDCFFE4B04D6BB06C6CDC");
            add("E675FDCFFE4B03B80C2DFAFB7");
            add("E671B0F95E4B03B8062A1ADD8");
            add("E671B0F84E4B04D6B2B203036");
            add("E671B0F6AE4B04D6B2B203035");
            add("E671B0F59E4B04D6B2B203034");
            add("E671B0F45E4B04D6B2B203033");
            add("E671B0F20E4B03B8062A1ADD7");
            add("E6645B09FE4B0F67AC09CD4F6");
            add("E6645728AE4B0F67AC09CD4F5");
            add("E66456DEFE4B0F67AC09CD4F4");
            add("E66447A63E4B0F67AC09CD4F3");
            add("E66446D57E4B0F67AC09CD4F2");
            add("E66442693E4B0F67AC09CD4ED");
            add("E66386600E4B05C8693B5BF53");
            add("E663865E1E4B05C8693B5BF50");
            add("E663865D7E4B05C8693B5BF4F");
            add("E663865CEE4B05C8693B5BF4E");
            add("E663865BBE4B05C8693B5BF4C");
            add("E663865AEE4B05C8693B5BF4B");
            add("E663865A4E4B05C8693B5BF4A");
            add("E6638659AE4B05C8693B5BF49");
            add("E66386590E4B05C8693B5BF48");
            add("E66386570E4B05C8693B5BF47");
            add("E66386118E4B05C8693B5BF46");
            add("E662B6AEFE4B015E071395FF3");
            add("E662B6AE1E4B015E071395FF2");
            add("E662B6AD2E4B015E071395FF1");
            add("E662B6AC0E4B015E071395FF0");
            add("E662B6A11E4B015E071395FEF");
            add("E662B6A03E4B015E071395FEE");
            add("E662B69F4E4B015E071395FED");
            add("E662B69E5E4B015E071395FEC");
            add("E662B69C5E4B015E071395FEB");
            add("E662B69B4E4B015E071395FEA");
            add("E662B69A5E4B015E071395FE9");
            add("E662B6988E4B015E071395FE8");
            add("E662A232FE4B015E071395FE7");
            add("E662A2323E4B015E071395FE6");
            add("E662A2317E4B015E071395FE5");
            add("E662A230CE4B015E071395FE4");
            add("E662A22FFE4B015E071395FE3");
            add("E662A22ECE4B015E071395FE2");
            add("E662A22DFE4B015E071395FE1");
            add("E662A22CFE4B015E071395FE0");
            add("E662A22A2E4B015E071395FDF");
            add("E662A2294E4B015E071395FDE");
            add("E662A2283E4B015E071395FDD");
            add("E662A225DE4B015E071395FDC");
            add("E662A221AE4B015E071395FDB");
        }});
        put("91131121734341656U", new java.util.ArrayList<String>() {{
            add("E675BAC80E4B03B804AA3551F");
            add("E6757ABE1E4B03B804AA3551C");
            add("E6757ABE2E4B03B804AA3551D");
            add("E6757ABE0E4B03B80C2DFAFB5");
            add("E6757ABE1E4B03B80B8C735E8");
            add("E6757ABDFE4B04D6BB06C6CDB");
            add("E6757ABE0E4B03B804AA3551B");
        }});
        put("91131121MA0D09YK3J", new java.util.ArrayList<String>() {{
            add("E6757ABDFE4B03B80C2DFAFB4");
            add("E6757ABDDE4B03B80B8C735E7");
            add("E6757ABDEE4B04D6BB06C6CDA");
            add("E6757ABDCE4B03B804AA3551A");
            add("E6757ABDDE4B03B80B8C735E6");
            add("E6757ABDBE4B03B80C2DFAFB2");
            add("E6757ABDCE4B03B80C2DFAFB3");
            add("E6757ABDBE4B03B80C2DFAFB1");
        }});
        put("913702145611530153", new java.util.ArrayList<String>() {{
            add("E674FF35BE4B03B80C2DFAFB0");
            add("E674FF34DE4B03B80B8C735E5");
            add("E674FF344E4B03B80C2DFAFAF");
            add("E674FF338E4B03B80B8C735E4");
            add("E674FF32DE4B04D6BB06C6CD9");
            add("E674FF316E4B03B80B8C735E3");
        }});
        put("91370222MA3MK5WF1F", new java.util.ArrayList<String>() {{
            add("E67481E98E4B03B804AA35518");
            add("E6747EFFBE4B03B80C2DFAFAE");
            add("E67456AB8E4B03B804AA35516");
            add("E67453209E4B04D6BB06C6CD7");
            add("E6740249EE4B03B80B8C735DF");
            add("E6740249EE4B03B804AA35513");
            add("E6740249EE4B04D6BB06C6CD4");
            add("E6740249EE4B03B804AA35514");
            add("E6740249EE4B03B80C2DFAFAD");
            add("E6740249EE4B04D6BB06C6CD5");
            add("E66DA73B4E4B0DAA192B8B13F");
        }});
        put("91370212747210962T", new java.util.ArrayList<String>() {{
            add("E673C271BE4B03B80B8C735D9");
            add("E673C271BE4B04D6BB06C6CD1");
            add("E673C271BE4B03B804AA3550F");
            add("E673C271BE4B03B80C2DFAFAB");
            add("E673C271BE4B03B80B8C735DA");
            add("E673C271BE4B03B80B8C735DB");
            add("E673C271BE4B04D6BB06C6CD2");
            add("E673C271BE4B03B804AA35510");
            add("E673C271BE4B03B80B8C735DC");
            add("E673C271BE4B03B804AA35511");
            add("E673C271BE4B04D6BB06C6CD3");
            add("E673C271BE4B03B80C2DFAFAC");
            add("E673C271BE4B03B804AA35512");
            add("E673C271BE4B03B80B8C735DD");
            add("E673BE81FE4B03B80B8C735D8");
            add("E673BE81FE4B04D6BB06C6CCF");
            add("E673BE81FE4B03B804AA3550E");
            add("E673BE81FE4B04D6BB06C6CD0");
            add("E673BE81FE4B03B80C2DFAFA9");
            add("E673BE81FE4B03B80C2DFAFAA");
            add("E673BE81EE4B03B80B8C735D4");
            add("E673BE81EE4B04D6BB06C6CCB");
            add("E673BE81EE4B03B804AA3550A");
            add("E673BE81EE4B04D6BB06C6CCC");
            add("E673BE81EE4B03B804AA3550B");
            add("E673BE81EE4B03B80B8C735D5");
            add("E673BE81EE4B03B80C2DFAFA6");
            add("E673BE81EE4B04D6BB06C6CCD");
            add("E673BE81EE4B03B80B8C735D6");
            add("E673BE81EE4B03B80C2DFAFA7");
            add("E673BE81FE4B03B804AA3550C");
            add("E673BE81FE4B04D6BB06C6CCE");
            add("E673BE81FE4B03B804AA3550D");
            add("E673BE81FE4B03B80B8C735D7");
            add("E673BE81FE4B03B80C2DFAFA8");
            add("E673BE81DE4B03B80B8C735D0");
            add("E673BE81DE4B03B80B8C735D1");
            add("E673BE81EE4B03B80B8C735D2");
            add("E673BE81EE4B04D6BB06C6CC8");
            add("E673BE81EE4B04D6BB06C6CC9");
            add("E673BE81EE4B03B80C2DFAFA3");
            add("E673BE81EE4B03B80C2DFAFA4");
            add("E673BE81EE4B04D6BB06C6CCA");
            add("E673BE81EE4B03B80B8C735D3");
            add("E673BE81EE4B03B80C2DFAFA5");
            add("E673BE81EE4B03B804AA35509");
        }});
        put("91341623MA2NX8PE77", new java.util.ArrayList<String>() {{
            add("E672C7CA9E4B03B80B8C735CE");
            add("E672C7CA9E4B03B80B8C735CF");
            add("E672C7CA9E4B04D6BB06C6CC7");
            add("E672C7CA9E4B03B80C2DFAFA1");
            add("E672C7CA9E4B03B804AA35508");
            add("E672C7CA9E4B03B804AA35506");
            add("E672C7CA9E4B03B80C2DFAFA0");
            add("E672C7CA9E4B03B804AA35507");
            add("E672C7CA9E4B03B80B8C735CD");
        }});
        put("91321391MA26MMFL32", new java.util.ArrayList<String>() {{
            add("E67171C7DE4B03B80467455FD");
            add("E67171C7DE4B03B805CFBC82B");
            add("E67171C7DE4B03B80467455FE");
            add("E67171C7DE4B03B80467455FF");
            add("E67171C7DE4B03B805CFBC82C");
            add("E67171C7CE4B03B80467455F5");
            add("E67171C7CE4B03B805CFBC822");
            add("E67171C7CE4B03B80467455F6");
            add("E67171C7CE4B03B805CFBC823");
            add("E67171C7CE4B03B806EBB0DF3");
            add("E67171C7CE4B03B805CFBC824");
            add("E67171C7CE4B04D6B5698F5FE");
            add("E67171C7CE4B03B806EBB0DF4");
        }});
        put("9137021179080300X3", new java.util.ArrayList<String>() {{
            add("E67171C7CE4B03B80467455F7");
            add("E67171C7CE4B04D6B5698F5FF");
            add("E67171C7CE4B03B806EBB0DF5");
            add("E67171C7CE4B04D6B5698F600");
            add("E67171C7CE4B03B80467455F8");
            add("E67171C7CE4B03B805CFBC825");
            add("E67171C7CE4B03B805CFBC826");
            add("E67171C7CE4B03B80467455F9");
            add("E67171C7CE4B03B806EBB0DF6");
            add("E67171C7DE4B04D6B5698F601");
            add("E67171C7DE4B03B806EBB0DF7");
            add("E67171C7DE4B03B80467455FA");
            add("E67171C7DE4B03B805CFBC827");
        }});
        put("91321391MAD9M72001", new java.util.ArrayList<String>() {{
            add("E67171C7DE4B04D6B5698F602");
            add("E67171C7DE4B03B80467455FB");
            add("E67171C7DE4B03B806EBB0DF8");
            add("E67171C7DE4B03B806EBB0DF9");
            add("E67171C7DE4B03B805CFBC828");
            add("E67171C7DE4B03B805CFBC829");
            add("E67171C7DE4B03B80467455FC");
            add("E67171C7DE4B03B806EBB0DFA");
            add("E67171C7DE4B04D6B5698F603");
            add("E67171C7DE4B03B805CFBC82A");
            add("E67171C7DE4B04D6B5698F604");
            add("E67171C7DE4B04D6B5698F605");
            add("E67171C7DE4B03B806EBB0DFB");
            add("E67171C7CE4B03B806EBB0DF2");
            add("E67171C7CE4B04D6B5698F5FC");
            add("E67171C7CE4B04D6B5698F5FD");
        }});
        put("91371122MA3N8JL37T", new java.util.ArrayList<String>() {{
            add("E676E6911E4B03B80C2DFAFD4");
            add("E676E6905E4B03B804AA35540");
            add("E676E68C9E4B03B80C2DFAFD3");
            add("E676E68C0E4B03B804AA3553F");
            add("E676E68B6E4B03B80B8C73605");
            add("E676E68ADE4B03B80C2DFAFD2");
            add("E676E055BE4B03B804AA3553E");
            add("E676E055BE4B03B80B8C73603");
            add("E676E055BE4B03B80B8C73604");
        }});
        put("91371122689459152H", new java.util.ArrayList<String>() {{
            add("E676E055BE4B04D6BB06C6CF5");
            add("E676E055BE4B04D6BB06C6CF6");
            add("E676E055AE4B03B80C2DFAFC8");
            add("E676E055AE4B03B80B8C735FA");
            add("E676E055AE4B03B80C2DFAFC9");
            add("E676E055AE4B03B804AA35535");
            add("E676E055AE4B03B80B8C735FB");
            add("E676E055AE4B04D6BB06C6CED");
            add("E676E055AE4B04D6BB06C6CEE");
            add("E676E055AE4B03B804AA35536");
            add("E676E055AE4B03B80C2DFAFCA");
        }});
        put("91371122725436442J", new java.util.ArrayList<String>() {{
            add("E676E055AE4B03B80B8C735FC");
            add("E676E055AE4B03B80C2DFAFCB");
            add("E676E055AE4B03B804AA35537");
            add("E676E055AE4B03B80B8C735FD");
            add("E676E055AE4B03B804AA35538");
            add("E676E055AE4B03B80B8C735FE");
            add("E676E055AE4B04D6BB06C6CEF");
            add("E676E055AE4B03B80C2DFAFCC");
            add("E676E055AE4B04D6BB06C6CF0");
            add("E676E055BE4B03B804AA35539");
            add("E676E055BE4B03B80B8C735FF");
            add("E676E055BE4B04D6BB06C6CF1");
            add("E676E055BE4B04D6BB06C6CF2");
            add("E676E055BE4B03B80C2DFAFCD");
            add("E676E055BE4B04D6BB06C6CF3");
            add("E676E055BE4B03B80B8C73600");
        }});
        put("91371122059040832R", new java.util.ArrayList<String>() {{
            add("E676E055BE4B03B80C2DFAFCE");
            add("E676E055BE4B03B80C2DFAFCF");
            add("E676E055BE4B03B804AA3553A");
            add("E676E055BE4B03B804AA3553B");
            add("E676E055BE4B03B804AA3553C");
            add("E676E055BE4B03B80B8C73601");
            add("E676E055BE4B03B80B8C73602");
            add("E676E055BE4B03B80C2DFAFD0");
            add("E676E055BE4B04D6BB06C6CF4");
            add("E676E055BE4B03B804AA3553D");
            add("E676E055BE4B03B80C2DFAFD1");
            add("E676E055AE4B03B80C2DFAFC6");
            add("E676E055AE4B03B804AA35532");
            add("E676E055AE4B03B80B8C735F9");
            add("E676E055AE4B03B80C2DFAFC7");
            add("E676E055AE4B04D6BB06C6CEC");
        }});
        put("91371122MA3NGRAE2E", new java.util.ArrayList<String>() {{
            add("E676E055AE4B03B804AA35533");
            add("E676E055AE4B03B804AA35534");
            add("E670E115BE4B03B8042996602");
            add("E6709E34DE4B03B8071C97C9D");
            add("E6709CAAFE4B03B8071C97C9B");
            add("E66F8A8BAE4B04D6BEF721A32");
            add("E66F8A8BAE4B03B8071C97C64");
            add("E66F8A8BAE4B04D6BEF721A33");
            add("E66F8A8BAE4B03B8071C97C65");
            add("E66F8A8B9E4B03B8071C97C60");
            add("E66F8A8B9E4B04D6BEF721A2D");
            add("E66F8A8B9E4B03B8071C97C61");
            add("E66F8A8B9E4B04D6BEF721A2E");
            add("E66F8A8BAE4B03B8071C97C62");
            add("E66F8A8BAE4B03B8071C97C63");
            add("E66F8A8BAE4B04D6BEF721A2F");
            add("E66F8A8BAE4B04D6BEF721A30");
            add("E66F8A8BAE4B04D6BEF721A31");
            add("E66F8A8B9E4B03B8071C97C5F");
            add("E66F3CFD4E4B03B8071C97C5B");
            add("E66F3CFD4E4B04D6BEF721A2A");
            add("E66F3CFD5E4B04D6BEF721A2B");
            add("E66F3CFD5E4B03B8071C97C5D");
            add("E66F3CFD5E4B04D6BEF721A2C");
            add("E66F3CFD5E4B03B8071C97C5E");
            add("E66F3CFD3E4B04D6BEF721A25");
            add("E66F3CFD3E4B04D6BEF721A26");
            add("E66F3CFD3E4B03B8071C97C56");
            add("E66F3CFD3E4B04D6BEF721A27");
            add("E66F3CFD4E4B03B8071C97C57");
            add("E66F3CFD4E4B03B8071C97C58");
            add("E66F3CFD4E4B04D6BEF721A29");
            add("E66F3CFD2E4B04D6BEF721A20");
            add("E66F3CFD2E4B04D6BEF721A21");
            add("E66F3CFD2E4B03B8071C97C51");
            add("E66F3CFD2E4B03B8071C97C52");
            add("E66F3CFD2E4B04D6BEF721A22");
            add("E66F3CFD3E4B04D6BEF721A23");
            add("E66F3CFD3E4B03B8071C97C54");
            add("E66F3CFD1E4B03B8071C97C4C");
            add("E66F3CFD1E4B04D6BEF721A1C");
            add("E66F3CFD1E4B03B8071C97C4D");
            add("E66F3CFD2E4B03B8071C97C4E");
            add("E66F3CFD2E4B03B8071C97C4F");
            add("E66F3CFD2E4B04D6BEF721A1F");
            add("E66F3CFD2E4B03B8071C97C50");
            add("E66F3CFD0E4B04D6BEF721A17");
            add("E66F3CFD0E4B03B8071C97C47");
            add("E66F3CFD0E4B03B8071C97C48");
            add("E66F3CFD0E4B04D6BEF721A18");
            add("E66F3CFD0E4B04D6BEF721A19");
            add("E66F3CFD1E4B03B8071C97C49");
            add("E66F3CFD1E4B04D6BEF721A1A");
            add("E66F3CFD1E4B03B8071C97C4A");
            add("E66F3CFD1E4B03B8071C97C4B");
            add("E66F3CFD0E4B03B8071C97C46");
            add("E66F3CFD0E4B04D6BEF721A16");
            add("E66CC48E2E4B0DAA192B8B137");
            add("E66CC48E2E4B0DAA192B8B138");
            add("E66CC48E2E4B0DAA192B8B139");
            add("E66CC48E2E4B0DAA192B8B13A");
        }});
        put("9137112259655306XU", new java.util.ArrayList<String>() {{
            add("E66CC48E1E4B0DAA192B8B132");
            add("E66CC48E1E4B0DAA1E864C24A");
            add("E66CC48E1E4B0DAA192B8B133");
            add("E66CC48E1E4B0DAA192B8B134");
            add("E66CC48E1E4B0DAA1E864C24B");
            add("E66CC48E2E4B0DAA192B8B135");
            add("E66CC48E2E4B0DAA1E864C24C");
            add("E66CC48E2E4B0DAA192B8B136");
            add("E66CC48E2E4B0DAA1E864C24D");
            add("E66CC48E2E4B0DAA1E864C24E");
            add("E66CC48E2E4B0DAA1E864C24F");
            add("E66CC48E0E4B0DAA192B8B12C");
            add("E66CC48E0E4B0DAA1E864C244");
            add("E66CC48E0E4B0DAA192B8B12D");
            add("E66CC48E0E4B0DAA192B8B12E");
            add("E66CC48E0E4B0DAA1E864C245");
            add("E66CC48E0E4B0DAA192B8B12F");
            add("E66CC48E1E4B0DAA1E864C246");
            add("E66CC48E1E4B0DAA192B8B130");
            add("E66CC48E1E4B0DAA1E864C247");
            add("E66CC48E1E4B0DAA1E864C248");
            add("E66CC48E1E4B0DAA192B8B131");
            add("E66CC48DFE4B0DAA192B8B126");
            add("E66CC48DFE4B0DAA192B8B127");
            add("E66CC48DFE4B0DAA1E864C23E");
            add("E66CC48DFE4B0DAA192B8B128");
            add("E66CC48DFE4B0DAA1E864C23F");
            add("E66CC48DFE4B0DAA192B8B129");
            add("E66CC48E0E4B0DAA192B8B12A");
            add("E66CC48E0E4B0DAA1E864C240");
            add("E66CC48E0E4B0DAA1E864C241");
            add("E66CC48E0E4B0DAA192B8B12B");
        }});
        put("91371122MA3U4DAR10", new java.util.ArrayList<String>() {{
            add("E66CC48E0E4B0DAA1E864C242");
            add("E66CC48E0E4B0DAA1E864C243");
            add("E66CC48DEE4B0DAA192B8B121");
            add("E66CC48DEE4B0DAA1E864C237");
            add("E66CC48DEE4B0DAA192B8B122");
            add("E66CC48DEE4B0DAA1E864C238");
            add("E66CC48DEE4B0DAA1E864C239");
            add("E66CC48DEE4B0DAA192B8B123");
            add("E66CC48DFE4B0DAA1E864C23A");
            add("E66CC48DFE4B0DAA1E864C23B");
            add("E66CC48DFE4B0DAA192B8B124");
            add("E66CC48DFE4B0DAA1E864C23C");
            add("E66CC48DFE4B0DAA1E864C23D");
            add("E66CC48DFE4B0DAA192B8B125");
            add("E66CC48DEE4B0DAA192B8B120");
        }});
        put("91370214MA3DANCLXE", new java.util.ArrayList<String>() {{
            add("E66C005EEE4B0874452034CCB");
            add("E66C00403E4B0DAA1FD4E0EF3");
            add("E66C003EDE4B0DAA1FD4E0EF2");
            add("E66B9B5F1E4B0874452034CC6");
            add("E66B9B5F1E4B0DAA17B2BF40F");
            add("E66AB5058E4B0DAA1689D4F1D");
        }});
        put("91370282770262453W", new java.util.ArrayList<String>() {{
            add("E66BD6AA0E4B0DAA1FD4E0EF1");
            add("E66BD6A80E4B0874452034CCA");
            add("E66B9B5F2E4B0874452034CC8");
            add("E66AB5057E4B0DAA1FD4E0EE4");
            add("E66AB5057E4B0DAA17B2BF407");
            add("E66AB5058E4B0DAA1689D4F1B");
            add("E66AB5058E4B0874452034CC1");
            add("E66AB5057E4B0DAA17B2BF406");
            add("E66AB5057E4B0DAA1689D4F1A");
            add("E66763A24E4B01B64E1C6713A");
            add("E66763A23E4B01303AEE0D94E");
            add("E66763A23E4B01303AEE0D94F");
        }});
        put("913702810572539659", new java.util.ArrayList<String>() {{
            add("E66B9B5F1E4B0DAA17B2BF410");
            add("E66B9B5F1E4B0DAA1689D4F24");
            add("E66B9B5F1E4B0DAA1FD4E0EEF");
            add("E66B9B5F1E4B0874452034CC7");
            add("E66B9B5F2E4B0DAA17B2BF411");
            add("E66B9B5F2E4B0DAA1FD4E0EF0");
            add("E66B9B5F2E4B0DAA1689D4F26");
            add("E66B9B5F1E4B0DAA1FD4E0EEC");
            add("E66B9B5F1E4B0DAA1689D4F22");
            add("E66B9B5F1E4B0DAA1689D4F23");
            add("E66B9B5F1E4B0DAA1FD4E0EEE");
            add("E66AB5057E4B0DAA17B2BF408");
            add("E66AB5057E4B0874452034CBE");
            add("E66AB5057E4B0874452034CBF");
        }});
        put("9137028206107445X3", new java.util.ArrayList<String>() {{
            add("E66AB5058E4B0DAA17B2BF409");
            add("E66AB5058E4B0874452034CC3");
            add("E66AB5058E4B0DAA1FD4E0EE9");
            add("E66AB5058E4B0DAA1FD4E0EEA");
            add("E66AB5059E4B0DAA17B2BF40B");
            add("E66AB5059E4B0DAA1689D4F1F");
            add("E66AB5057E4B0874452034CC0");
            add("E66AB5057E4B0DAA1FD4E0EE5");
            add("E66AB5057E4B0DAA1FD4E0EE6");
            add("E66AB5058E4B0DAA1689D4F1C");
            add("E66AB5058E4B0DAA1FD4E0EE7");
            add("E66AB5058E4B0DAA1FD4E0EE8");
            add("E66AB5058E4B0874452034CC2");
            add("E66AB5057E4B0DAA17B2BF405");
            add("E66AB5057E4B0DAA1689D4F19");
        }});
        put("91370214553997453J", new java.util.ArrayList<String>() {{
            add("E66AB5056E4B0874452034CBD");
            add("E668768D7E4B0950C17BB123B");
            add("E668768D7E4B0950C8A82C60A");
            add("E668768D7E4B0950C8A82C60B");
            add("E668768D7E4B0950CE3AA4228");
            add("E668768D8E4B0950C17BB123C");
            add("E668768D8E4B0950C17BB123D");
            add("E668768D8E4B0269D011C65A6");
            add("E668768D8E4B0269D011C65A7");
            add("E668768D6E4B0269D011C65A1");
            add("E668768D6E4B0950CE3AA4225");
            add("E668768D6E4B0950CE3AA4226");
            add("E668768D6E4B0950C8A82C608");
            add("E668768D7E4B0269D011C65A3");
            add("E668768D7E4B0950C17BB123A");
            add("E668768D7E4B0950CE3AA4227");
            add("E668768D7E4B0269D011C65A4");
            add("E668768D7E4B0950C8A82C609");
            add("E668768D5E4B0269D011C659F");
            add("E668768D5E4B0950CE3AA4220");
            add("E668768D5E4B0950CE3AA4221");
            add("E668768D5E4B0950C17BB1237");
            add("E668768D5E4B0950CE3AA4222");
            add("E668768D6E4B0950C8A82C606");
            add("E668768D6E4B0950C17BB1238");
            add("E668768D6E4B0950CE3AA4224");
            add("E668768D5E4B0950C17BB1234");
            add("E668768D5E4B0950C17BB1235");
            add("E668768D5E4B0950CE3AA421F");
            add("E66850460E4B013031C5F22EB");
            add("E66850460E4B01303C1606939");
            add("E66850460E4B013035E1C0679");
            add("E66850460E4B013031C5F22EC");
            add("E66850460E4B013035E1C067A");
            add("E66850460E4B01303A93C25CA");
            add("E66850460E4B01303A93C25C9");
            add("E66850460E4B013035E1C0678");
            add("E6685045EE4B01303A93C25C7");
            add("E6685045EE4B013035E1C0677");
            add("E6685045EE4B013031C5F22E9");
            add("E6685045FE4B01303A93C25C8");
            add("E6685045FE4B01303C1606938");
            add("E6685045FE4B013031C5F22EA");
            add("E6685045DE4B01303C1606937");
            add("E6685045CE4B01303A93C25C6");
            add("E6684F0EAE4B01303A93C25C5");
            add("E6684F0EAE4B013035E1C0675");
            add("E6684F0EAE4B013035E1C0676");
            add("E6684F0E9E4B01303A93C25C3");
            add("E6684F0E9E4B01303C1606935");
            add("E6684F0E9E4B013031C5F22E7");
            add("E6684F0EAE4B01303A93C25C4");
            add("E6684F0EAE4B013035E1C0673");
            add("E6684F0EAE4B013035E1C0674");
            add("E6684F0EAE4B013031C5F22E8");
            add("E6684F0EAE4B01303C1606936");
            add("E667E1EB1E4B01303A93C25C0");
            add("E667E1EB1E4B013035E1C0670");
            add("E667E1EB1E4B01303A93C25C1");
            add("E667E1EB1E4B013031C5F22E2");
            add("E667E1EB1E4B013035E1C0671");
            add("E667E1EB2E4B01303C1606932");
            add("E667E1EB0E4B013035E1C066D");
            add("E667E1EB0E4B013035E1C066E");
            add("E667E1EB1E4B013031C5F22E0");
            add("E667E1EB1E4B013031C5F22E1");
            add("E667E1EB1E4B01303A93C25BF");
            add("E667E1EB1E4B013035E1C066F");
            add("E667E1EB1E4B01303C1606931");
            add("E667E18A6E4B013031C5F22DF");
            add("E667E11C7E4B013031C5F22DE");
        }});
        put("913304027477249474", new java.util.ArrayList<String>() {{
            add("E6690E899E4B0874452034CB5");
            add("E6690E899E4B0DAA17B2BF401");
            add("E6690E899E4B0DAA17B2BF402");
            add("E6690E899E4B0DAA1FD4E0EE2");
            add("E6690E899E4B0DAA1689D4F15");
            add("E6690E89AE4B0DAA1FD4E0EE3");
            add("E6690E89AE4B0874452034CB7");
            add("E6690E898E4B0DAA1FD4E0EDE");
            add("E6690E898E4B0DAA1FD4E0EDF");
            add("E6690E898E4B0DAA17B2BF3FE");
            add("E6690E898E4B0874452034CB0");
            add("E6690E898E4B0DAA17B2BF3FF");
            add("E6690E898E4B0874452034CB1");
            add("E6690E899E4B0874452034CB2");
            add("E6690E899E4B0DAA1FD4E0EE0");
            add("E6690E899E4B0874452034CB3");
            add("E6690E899E4B0DAA1689D4F14");
            add("E6690E899E4B0DAA1FD4E0EE1");
            add("E6690E899E4B0874452034CB4");
            add("E6690E897E4B0DAA17B2BF3FB");
            add("E6690E897E4B0874452034CAE");
            add("E6690E897E4B0DAA17B2BF3FC");
            add("E6690E897E4B0DAA1689D4F10");
            add("E6690E897E4B0DAA1FD4E0EDC");
            add("E6690E898E4B0DAA1689D4F11");
            add("E6690E898E4B0DAA1689D4F12");
            add("E6690E898E4B0DAA17B2BF3FD");
            add("E6690E898E4B0874452034CAF");
            add("E6690E898E4B0DAA1689D4F13");
            add("E6690E898E4B0DAA1FD4E0EDD");
            add("E6690E896E4B0DAA17B2BF3F7");
            add("E6690E896E4B0DAA17B2BF3F8");
            add("E6690E896E4B0DAA17B2BF3F9");
            add("E6690E896E4B0DAA1689D4F0D");
            add("E6690E897E4B0874452034CAB");
            add("E6690E897E4B0DAA1FD4E0EDA");
            add("E6690E897E4B0874452034CAC");
            add("E6690E897E4B0DAA17B2BF3FA");
            add("E6690E897E4B0874452034CAD");
            add("E6690E896E4B0DAA1FD4E0ED9");
        }});
        put("91370212MA3MMYH62F", new java.util.ArrayList<String>() {{
            add("E668768D6E4B0950C17BB1239");
            add("E668768D7E4B0269D011C65A2");
            add("E668768D4E4B0950C8A82C604");
            add("E668768D5E4B0269D011C659E");
            add("E65FBC9F6E4B09F5FE26220E3");
            add("E65FBC9DDE4B09F5FE26220E1");
            add("E65FBC9CFE4B09F5FE26220E0");
        }});
        put("91320923MA7KAYEJXA", new java.util.ArrayList<String>() {{
            add("E66A9D34DE4B0874452034CBB");
            add("E66A9D082E4B0DAA17B2BF404");
            add("E668768D6E4B0950C8A82C605");
            add("E66763A24E4B013034A79DDDA");
            add("E66763A24E4B01B64E1C6713B");
            add("E66763A24E4B01B64645C7101");
            add("E66763A24E4B01303AEE0D950");
            add("E66763A25E4B01B64645C7102");
            add("E66763A25E4B013034A79DDDB");
            add("E66763A25E4B01303AEE0D951");
            add("E66763A25E4B01B64E1C6713C");
            add("E66763A23E4B01303AEE0D94C");
            add("E66763A23E4B01303AEE0D94D");
            add("E66763A23E4B01B64645C70FE");
            add("E66763A24E4B013034A79DDD8");
            add("E66763A24E4B01B64645C70FF");
            add("E66763A24E4B01B64645C7100");
            add("E66763A24E4B013034A79DDD9");
            add("E66763A23E4B01B64E1C67139");
            add("E663865EAE4B05C8693B5BF51");
        }});
        put("91370281MA3CTJT401", new java.util.ArrayList<String>() {{
            add("E66CE9980E4B0DAA1E864C253");
            add("E668768D6E4B0950CE3AA4223");
            add("E664DA6ADE4B0DD161A65075A");
            add("E65FBCA0DE4B09F5FE26220E5");
            add("E65FBC9E9E4B09F5FE26220E2");
            add("E65FBC9BFE4B09F5FE26220DF");
            add("E65FBC970E4B09F5FE26220D9");
            add("E65FBC90CE4B09F5FE26220D2");
        }});
        put("91340300395893535A", new java.util.ArrayList<String>() {{
            add("E666BFC20E4B01B64E1C67138");
            add("E666273E1E4B08702F8DF18FF");
            add("E6662654DE4B00AF418EEEBD5");
            add("E66625FE2E4B00AF4FF57E862");
            add("E6661800AE4B08702F8DF18FE");
            add("E6656DF56E4B0DD16EE410606");
            add("E66445BB8E4B0F67AC09CD4EE");
        }});
        put("91320831MA1NX7WQ5J", new java.util.ArrayList<String>() {{
            add("E66A1C1CCE4B0DAA1689D4F17");
            add("E6652ED86E4B072E86436A9D8");
            add("E6652ED44E4B0DD16EE410605");
            add("E6652ECFDE4B072E87B863FC7");
            add("E6652ECBCE4B072E86436A9D7");
            add("E6652E6C3E4B072E87B863FC6");
            add("E6652E664E4B072E86436A9D6");
            add("E6652E5F7E4B072E87B863FC5");
            add("E6652D654E4B0DD16EE410604");
            add("E6652D5E9E4B072E86436A9D5");
            add("E6652D55FE4B072E87B863FC4");
            add("E6652D4DBE4B0DD16FDBAEDCA");
            add("E6652B19CE4B0DD16EE410603");
            add("E6652B12BE4B0DD16EE410602");
            add("E6652B0BBE4B0DD16FDBAEDC9");
            add("E6652A9C7E4B0DD16EE410601");
            add("E664EEFB4E4B0DD160425C05B");
            add("E664EEFADE4B0DD16E8F3BDB6");
            add("E664EEFA2E4B0DD160425C05A");
            add("E664EEF8CE4B0DD16E8F3BDB5");
            add("E664EEF81E4B072E880B9C287");
            add("E664EEF77E4B072E880B9C286");
            add("E664EEF6EE4B0DD160425C059");
            add("E664EEF58E4B0DD16E8F3BDB4");
            add("E664EEF47E4B0DD160425C058");
            add("E664EEF31E4B072E880B9C285");
            add("E664EEF26E4B0DD160425C057");
            add("E664EEF16E4B072E880B9C284");
            add("E664EEF0CE4B072E880B9C283");
            add("E664EEF02E4B072E880B9C282");
            add("E664EEEE4E4B0DD160425C056");
            add("E6646BA27E4B0F67AC09CD4FC");
            add("E6646BA17E4B0F67AC09CD4FB");
            add("E6646BA05E4B0F67AC09CD4FA");
            add("E6646B9F8E4B0F67AC09CD4F9");
            add("E6646B9E8E4B0F67AC09CD4F8");
            add("E6646B9C3E4B0F67AC09CD4F7");
            add("E6583D00DE4B09F5FCFD00C5A");
            add("E6583CFD8E4B09F5FB205EBED");
            add("E6583CC68E4B09F5FB205EBEC");
        }});
        put("91370211773531196A", new java.util.ArrayList<String>() {{
            add("E66CE999EE4B0DAA192B8B13D");
            add("E66CE998EE4B0DAA1E864C254");
            add("E65FBC965E4B09F5FE26220D8");
            add("E65FBC932E4B09F5FE26220D5");
            add("E65FBC902E4B09F5FE26220D1");
            add("E65FBC8F6E4B09F5FE26220D0");
            add("E65FBC8A6E4B09F5FE26220CD");
        }});
        put("91370282776820962E", new java.util.ArrayList<String>() {{
            add("E664460C9E4B0F67AC09CD4F1");
            add("E664460A5E4B0F67AC09CD4F0");
            add("E66445E2CE4B0F67AC09CD4EF");
            add("E65D6E3D6E4B09F5FBD8A48D8");
            add("E65D6E3BDE4B09F5FBD8A48D7");
            add("E65D58D06E4B09F5FBD8A48D6");
            add("E65C09913E4B09F5F2C4D76B0");
            add("E65C0965FE4B09F5F2C4D76AF");
            add("E65C09653E4B09F5F2C4D76AE");
            add("E65C092EEE4B09F5F2C4D76AD");
            add("E65BDF924E4B09F5F2C4D76AC");
            add("E65BDF8F9E4B09F5F2C4D76AB");
            add("E65BDF8B3E4B09F5F2C4D76AA");
            add("E65BDF8A6E4B09F5F2C4D76A9");
        }});
        put("913706006768180644", new java.util.ArrayList<String>() {{
            add("E65B8A812E4B09F5F2C4D76A6");
            add("E65B88025E4B09F5F2C4D76A5");
            add("E65B87F70E4B09F5F2C4D76A4");
            add("E65B87EA5E4B09F5F2C4D76A3");
            add("E65B87DFAE4B09F5F2C4D76A2");
            add("E65B87D3DE4B09F5F2C4D76A1");
            add("E65B87C9EE4B09F5F2C4D76A0");
            add("E65B87619E4B09F5F2C4D769F");
            add("E65B87564E4B09F5F2C4D769E");
            add("E65B874A4E4B09F5F2C4D769D");
            add("E65B873F3E4B09F5F2C4D769C");
            add("E65B87320E4B09F5F2C4D769B");
            add("E65B87262E4B09F5F2C4D769A");
            add("E65B8718CE4B09F5F2C4D7699");
            add("E65B86FA4E4B09F5F2C4D7698");
            add("E65B86ED7E4B09F5F2C4D7697");
            add("E65B86DA3E4B09F5F2C4D7696");
            add("E65B86C09E4B09F5F2C4D7695");
            add("E65B86AD0E4B09F5F2C4D7694");
            add("E65B8681BE4B09F5F2C4D7693");
            add("E65B86530E4B09F5F2C4D7692");
            add("E65B85FE3E4B09F5F2C4D7691");
            add("E65B0EBACE4B09F5F3E672730");
            add("E65B0EAEAE4B09F5F3E67272F");
            add("E65A0BDA4E4B09F5F81D65EE7");
            add("E65A0AB65E4B09F5FEC0C7C0F");
        }});
        put("91371325755431845A", new java.util.ArrayList<String>() {{
            add("E65AB704AE4B09F5F9334980F");
            add("E65AB7040E4B09F5F9334980E");
            add("E65AB6F49E4B09F5F9334980A");
            add("E65AB6F3EE4B09F5F93349809");
            add("E65AB6F34E4B09F5F93349808");
            add("E65AB6F2AE4B09F5F93349807");
            add("E65AB6F1FE4B09F5F93349806");
            add("E65AB6F0BE4B09F5F93349805");
            add("E65AB6EF1E4B09F5F93349804");
            add("E65AB6EE5E4B09F5F93349803");
            add("E65AB6EAEE4B09F5F93349802");
            add("E65AB6E9FE4B09F5F93349801");
            add("E65AB6E93E4B09F5F93349800");
            add("E65AB6E82E4B09F5F933497FF");
            add("E65AB6E74E4B09F5F933497FE");
            add("E65AB6E40E4B09F5F933497FD");
            add("E65AB6E21E4B09F5F933497FC");
            add("E65AB6E06E4B09F5F933497FB");
            add("E65AB6DF6E4B09F5F933497FA");
            add("E65AB6DD8E4B09F5F933497F9");
            add("E65AB6DBFE4B09F5F933497F8");
            add("E65AB6DB2E4B09F5F933497F7");
            add("E65AB6DA2E4B09F5F933497F6");
            add("E65AB6D93E4B09F5F933497F5");
            add("E65AB6D71E4B09F5F933497F4");
            add("E65AB65A7E4B09F5F933497F3");
        }});
        put("91341600072397940M", new java.util.ArrayList<String>() {{
            add("E6648757DE4B0F67AC09CD4FE");
            add("E65B0B547E4B09F5F3E67272E");
            add("E658BAF91E4B09F5FB205EBEE");
            add("E654C8451E4B09F5FFD9FB458");
            add("E654C7A2DE4B09F5FFD9FB455");
            add("E654C791DE4B09F5FFD9FB454");
            add("E654C7718E4B09F5FFD9FB453");
            add("E654C7480E4B09F5FFD9FB452");
            add("E654C7132E4B09F5FFD9FB451");
            add("E654C6F35E4B09F5FFD9FB450");
            add("E654C6B73E4B09F5FFD9FB44F");
            add("E654C39EAE4B09F5F197DBC50");
            add("E654C37C2E4B09F5F197DBC4F");
            add("E654AEA9BE4B09F5F197DBC4E");
            add("E654AE88DE4B09F5F197DBC4D");
            add("E654AE71CE4B09F5F197DBC4C");
            add("E654AE592E4B09F5F197DBC4B");
            add("E654AE329E4B09F5F197DBC4A");
            add("E654ADFE0E4B09F5F197DBC49");
            add("E6549EFC7E4B09F5F197DBC48");
            add("E6549ED49E4B09F5F197DBC47");
            add("E6549EAAFE4B09F5F197DBC46");
            add("E6549E863E4B09F5F197DBC45");
            add("E6549A586E4B09F5F197DBC44");
            add("E6549A48DE4B09F5F197DBC43");
            add("E6549A1E6E4B09F5F197DBC42");
            add("E6549954AE4B09F5F2AA79593");
            add("E6547244EE4B09F5FD2C15093");
            add("E6546ED4BE4B09F5FD2C15092");
            add("E6545B9C0E4B09F5FD2C15091");
            add("E6545B84FE4B09F5FD2C15090");
            add("E6545B661E4B09F5FD2C1508F");
            add("E6545B3C3E4B09F5FD2C1508E");
            add("E6545B1DAE4B09F5FD2C1508D");
            add("E6545AEF9E4B09F5FD2C1508C");
            add("E6545AE25E4B09F5FD2C1508B");
            add("E6545AB66E4B09F5FD2C1508A");
            add("E6545A99FE4B09F5FD2C15089");
            add("E6545A75FE4B09F5FD2C15088");
            add("E6545A543E4B09F5FD2C15087");
            add("E6545A310E4B09F5FD2C15086");
            add("E6545A156E4B09F5FD2C15085");
            add("E65459A7AE4B09F5FD2C15084");
            add("E6544717EE4B09F5FD2C15083");
            add("E65446970E4B09F5FD2C15082");
            add("E654467D9E4B09F5FD2C15081");
            add("E65446710E4B09F5FD2C15080");
            add("E65445C1FE4B09F5FD2C1507F");
            add("E65445ADFE4B09F5FD2C1507E");
            add("E654458EBE4B09F5FD2C1507D");
            add("E6544557FE4B09F5FD2C1507C");
            add("E6544548AE4B09F5FD2C1507B");
            add("E654453AFE4B09F5FD2C1507A");
            add("E65444FD0E4B09F5FD2C15079");
            add("E65444ECCE4B09F5FD2C15078");
            add("E65444CE3E4B09F5FD2C15076");
            add("E65444AD9E4B09F5FD2C15075");
            add("E65444978E4B09F5FD2C15074");
            add("E654446DEE4B09F5FD2C15073");
            add("E65444590E4B09F5FD2C15072");
            add("E6544449CE4B09F5FD2C15071");
            add("E6543551CE4B09F5FD2C15070");
            add("E654353ACE4B09F5FD2C1506F");
            add("E6543527AE4B09F5FD2C1506E");
            add("E6543507EE4B09F5FD2C1506D");
            add("E65434EF7E4B09F5FD2C1506C");
            add("E65434CFBE4B09F5FD2C1506B");
            add("E65434A63E4B09F5FD2C1506A");
            add("E65434867E4B09F5FD2C15069");
            add("E654344EBE4B09F5FD2C15068");
            add("E65433DEBE4B09F5FD2C15067");
            add("E65433872E4B09F5FD2C15066");
            add("E6543371EE4B09F5FD2C15065");
            add("E65430225E4B09F5F16959194");
            add("E6542F9BEE4B09F5F16959193");
            add("E6542F54FE4B09F5F16959192");
            add("E6542F3E5E4B09F5F16959191");
            add("E6542F1F3E4B09F5F56376123");
            add("E6542F063E4B09F5F56376122");
            add("E6542EE80E4B09F5F56376121");
            add("E6542ECB5E4B09F5F56376120");
            add("E6542EB54E4B09F5F5637611F");
            add("E65422001E4B09F5F5637611E");
            add("E65421E71E4B09F5F5637611D");
            add("E6542132DE4B09F5F5637611C");
            add("E6542122DE4B09F5F5637611B");
            add("E65421134E4B09F5F5637611A");
            add("E65420FAAE4B09F5F56376119");
            add("E65420EA0E4B09F5F56376118");
            add("E65420D9EE4B09F5F56376117");
            add("E65420C34E4B09F5F56376116");
            add("E65420AAAE4B09F5F56376115");
            add("E654208DAE4B09F5F56376114");
            add("E654206D8E4B09F5F56376113");
            add("E6542025FE4B09F5F56376112");
        }});
        put("91371300071341670C", new java.util.ArrayList<String>() {{
            add("E653CAFFFE4B09F5F7784FE26");
            add("E653CAEE9E4B09F5F7784FE25");
            add("E653CADF3E4B09F5F7784FE24");
            add("E653CACF5E4B09F5F7784FE22");
            add("E653CAC02E4B09F5F7784FE21");
            add("E653CAA9BE4B09F5F7784FE20");
            add("E653CA9A9E4B09F5F7784FE1E");
            add("E653CA4E0E4B09F5F7784FE1C");
            add("E653CA3DCE4B09F5F7784FE1B");
        }});
        put("91371300MA3C92UE3A", new java.util.ArrayList<String>() {{
            add("E653C862FE4B09F5F7784FE19");
            add("E653C853AE4B09F5F7784FE18");
            add("E653C844EE4B09F5F7784FE17");
            add("E653C82C1E4B09F5F7784FE16");
            add("E653C81C6E4B09F5F7784FE15");
            add("E653C809BE4B09F5F862326D8");
            add("E653C7F98E4B09F5F862326D7");
            add("E653C7EA7E4B09F5F862326D6");
            add("E653C7CD8E4B09F5F862326D5");
            add("E653C7BA6E4B09F5F8A9DB8AA");
        }});
        put("91371325069960536K", new java.util.ArrayList<String>() {{
            add("E653C7986E4B09F5F8A9DB8A9");
            add("E653C7741E4B09F5F8A9DB8A8");
            add("E653C75C1E4B09F5F8A9DB8A7");
            add("E653C732FE4B09F5F8A9DB8A6");
            add("E653C7017E4B09F5F8A9DB8A5");
            add("E653C6992E4B09F5F8A9DB8A4");
            add("E653BA94EE4B09F5F8A9DB8A3");
            add("E653B486FE4B09F5F8A9DB8A2");
        }});
        put("91341222MA2UH83F4A", new java.util.ArrayList<String>() {{
            add("E653CD4C5E4B09F5FE0EC7EA8");
            add("E653CCEA1E4B09F5FE0EC7EA7");
            add("E653CC8D0E4B09F5F7784FE2E");
            add("E653CB486E4B09F5F7784FE2A");
            add("E653CAD9AE4B09F5F7784FE23");
            add("E653CAA08E4B09F5F7784FE1F");
            add("E653C86E4E4B09F5F7784FE1A");
            add("E653C7C72E4B09F5F862326D4");
        }});
        put("91370832MA3ETLG536", new java.util.ArrayList<String>() {{
            add("E675FE914E4B03B804AA3552C");
            add("E675FE914E4B03B80B8C735F4");
            add("E675FE914E4B03B804AA3552D");
            add("E675FE914E4B03B80B8C735F5");
            add("E675FE914E4B03B80B8C735F6");
            add("E675FE914E4B04D6BB06C6CE8");
            add("E675FE914E4B03B80B8C735F7");
            add("E675FE914E4B04D6BB06C6CE9");
            add("E675FE914E4B03B80C2DFAFC2");
            add("E675FE914E4B04D6BB06C6CEA");
            add("E675FE914E4B04D6BB06C6CEB");
            add("E675FE914E4B03B80C2DFAFC3");
            add("E675FE914E4B03B804AA3552E");
            add("E675FE914E4B03B80C2DFAFC4");
            add("E675FE914E4B03B804AA3552F");
            add("E675FE914E4B03B804AA35530");
            add("E675FE914E4B03B80B8C735F8");
            add("E675FE914E4B03B80C2DFAFC5");
            add("E675FE913E4B03B80B8C735EF");
            add("E675FE913E4B03B804AA35527");
            add("E675FE913E4B03B80B8C735F0");
            add("E675FE914E4B04D6BB06C6CE2");
            add("E675FE914E4B03B80B8C735F1");
            add("E675FE914E4B04D6BB06C6CE3");
            add("E675FE914E4B04D6BB06C6CE4");
            add("E675FE914E4B03B80C2DFAFBC");
            add("E675FE914E4B03B804AA35528");
            add("E675FE914E4B04D6BB06C6CE5");
            add("E675FE914E4B03B80C2DFAFBD");
            add("E675FE914E4B03B80C2DFAFBE");
            add("E675FE914E4B03B804AA35529");
            add("E675FE914E4B03B80C2DFAFBF");
            add("E675FE914E4B03B80B8C735F2");
            add("E675FE914E4B03B80B8C735F3");
            add("E675FE914E4B03B804AA3552A");
            add("E675FE914E4B04D6BB06C6CE6");
            add("E675FE914E4B04D6BB06C6CE7");
            add("E675FE914E4B03B80C2DFAFC0");
            add("E675FE914E4B03B804AA3552B");
            add("E675FE914E4B03B80C2DFAFC1");
        }});
        put("91341222MA2MXFF821", new java.util.ArrayList<String>() {{
            add("E6715FDF4E4B03B808682FE42");
            add("E6715FC49E4B03B808682FE41");
            add("E6715F9D5E4B03B8029D1FC5E");
            add("E67136873E4B03B803437122C");
            add("E6713680FE4B03B8042996603");
            add("E671352DCE4B03B803437122B");
        }});
        put("91370281065084882B", new java.util.ArrayList<String>() {{
            add("E6746BCC6E4B03B804AA35517");
            add("E6746BCB0E4B04D6BB06C6CD8");
            add("E6746BC96E4B03B80B8C735E2");
            add("E66F3CFD3E4B03B8071C97C53");
            add("E66F3CFD1E4B04D6BEF721A1B");
            add("E65A9D219E4B09F5F81D65EED");
            add("E65A9D1E9E4B09F5F81D65EEC");
        }});
        put("91370211MA3CJQ7290", new java.util.ArrayList<String>() {{
            add("E66F3CFD4E4B03B8071C97C5A");
            add("E66F3CFD5E4B03B8071C97C5C");
            add("E66F3CFD4E4B03B8071C97C59");
            add("E66F3CFD3E4B04D6BEF721A24");
            add("E66F3CFD0E4B04D6BEF721A15");
            add("E6667B0F9E4B00AF453BEB6FD");
        }});
        put("91370285MA3N08EW6A", new java.util.ArrayList<String>() {{
            add("E66B9B5F2E4B0DAA1689D4F25");
            add("E66B9B5F1E4B0DAA1FD4E0EED");
            add("E668768D7E4B0269D011C65A5");
            add("E66763A25E4B013034A79DDDC");
            add("E65FBC988E4B09F5FE26220DB");
            add("E66AB5059E4B0DAA17B2BF40C");
        }});
        put("91370281081406140F", new java.util.ArrayList<String>() {{
            add("E66AB5058E4B0DAA17B2BF40A");
            add("E66AB5059E4B0DAA1689D4F1E");
            add("E66AB5059E4B0874452034CC4");
            add("E66AB5056E4B0874452034CBC");
            add("E66AB5056E4B0DAA1689D4F18");
            add("E662DE0C3E4B015E071395FF4");
        }});
        put("913702825797893537", new java.util.ArrayList<String>() {{
            add("E668768D8E4B0950CE3AA4229");
            add("E668768D6E4B0950C8A82C607");
            add("E668768D5E4B0950C17BB1236");
            add("E668768D6E4B0269D011C65A0");
            add("E668768D4E4B0950C8A82C603");
            add("E66B57050E4B0DAA1FD4E0EEB");
            add("E66B56F8BE4B0DAA17B2BF40D");
        }});
        put("91340300697395953W", new java.util.ArrayList<String>() {{
            add("E65D42E4EE4B09F5FBD8A48D5");
            add("E65D4059EE4B09F5FBD8A48D4");
            add("E65A73587E4B09F5F81D65EEA");
            add("E65A7356AE4B09F5F81D65EE9");
            add("E65D310A4E4B09F5FBD8A48D3");
            add("E65BB4F00E4B09F5F2C4D76A7");
        }});
        put("9137022233405384XK", new java.util.ArrayList<String>() {{
            add("E66F3CFD3E4B03B8071C97C55");
            add("E66F3CFD1E4B04D6BEF721A1D");
            add("E66F3CFD2E4B04D6BEF721A1E");
            add("E66F3CFD0E4B04D6BEF721A14");
            add("E6741497FE4B03B80B8C735E0");
            add("E66C84C3FE4B0DAA11A016EE0");
            add("E65BB521CE4B09F5F2C4D76A8");
        }});
        put("91370683MA3WN3D834", new java.util.ArrayList<String>() {{
            add("E656836FDE4B09F5FE272986A");
            add("E656836AFE4B09F5FBBA8A67D");
            add("E6568364BE4B09F5FE2729869");
            add("E656832CCE4B09F5FE2729868");
            add("E65581901E4B09F5FD090112E");
            add("E6573F9F4E4B09F5FCFD00C56");
        }});
        put("913310047896612355", new java.util.ArrayList<String>() {{
            add("E6573152DE4B09F5FB205EBE8");
            add("E657121BDE4B09F5F1603998C");
            add("E663EC87AE4B01F710D2E2C4C");
            add("E65582738E4B09F5FD090112F");
            add("E66582558E4B0DD16FDBAEDCB");
            add("E6642C264E4B0F67AC09CD4EC");
        }});
        put("91340100MA2MX36M7N", new java.util.ArrayList<String>() {{
            add("E6542ECB5E4B09F5F56376120");
            add("E6542EB54E4B09F5F5637611F");
            add("E65422001E4B09F5F5637611E");
            add("E65421E71E4B09F5F5637611D");
            add("E6542132DE4B09F5F5637611C");
            add("E6542122DE4B09F5F5637611B");
            add("E65421134E4B09F5F5637611A");
            add("E65420FAAE4B09F5F56376119");
            add("E65420EA0E4B09F5F56376118");
            add("E65420D9EE4B09F5F56376117");
            add("E65420C34E4B09F5F56376116");
            add("E65420AAAE4B09F5F56376115");
            add("E654208DAE4B09F5F56376114");
            add("E654206D8E4B09F5F56376113");
            add("E6542025FE4B09F5F56376112");
        }});
    }};


}
