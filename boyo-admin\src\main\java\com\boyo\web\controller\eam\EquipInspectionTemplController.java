package com.boyo.web.controller.eam;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.eam.domain.*;
import com.boyo.eam.service.IEquipInspectionTemplItemService;
import com.boyo.eam.service.IEquipInspectionTemplService;
import com.boyo.eam.service.IEquipLedgerService;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.mapper.IotEquipmentMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 点检模板表(EquipInspectionTempl)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-29 10:21:50
 */
@Api("点检模板表")
@RestController
@RequestMapping("/equip/equipInspectionTempl")
@AllArgsConstructor
public class EquipInspectionTemplController extends BaseController{
    /**
     * 服务对象
     */
    private final IEquipInspectionTemplService equipInspectionTemplService;
    private final IEquipInspectionTemplItemService equipInspectionTemplItemService;
    private final IotEquipmentMapper equipmentMapper;

    /**
     * 查询点检模板表列表
     *
     */
    @ApiOperation("查询点检模板表列表")
    @GetMapping("/list")
    public TableDataInfo list(EquipInspectionTempl equipInspectionTempl) {
        startPage();
        List<EquipInspectionTempl> list = equipInspectionTemplService.selectEquipInspectionTemplList(equipInspectionTempl);
        return getDataTable(list);
    }
    
    /**
     * 获取点检模板表详情
     */
    @ApiOperation("获取点检模板表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        EquipInspectionTempl templ = equipInspectionTemplService.getById(id);
        String openid = templ.getOpenid();
        List<EquipInspectionTemplItem> itemList = equipInspectionTemplItemService.list(
                Wrappers.<EquipInspectionTemplItem>lambdaQuery()
                        .eq(EquipInspectionTemplItem::getEquipInspectionTemplOpenid, openid)
        );
        templ.setItemList(itemList);

        IotEquipment equipment = equipmentMapper.selectById(templ.getEquipLedgerOpenid());
        if(ObjectUtil.isNotNull(equipment)){
            templ.setEquipName(equipment.getEquipmentName());
            templ.setEquipCode(equipment.getEquipmentCode());
        }
        return AjaxResult.success(templ);
    }

    /**
     * 新增点检模板表
     */
    @ApiOperation("新增点检模板表")
    @PostMapping
    public AjaxResult add(@RequestBody EquipInspectionTempl equipInspectionTempl) {
        String openid = super.generateOpenid();
        equipInspectionTempl.setOpenid(openid);
        equipInspectionTempl.setCode(getNewCode());
        equipInspectionTempl.setState("0");//待处理
        equipInspectionTempl.setCreateTime(new Date());
        equipInspectionTempl.setCreateBy(SecurityUtils.getUsername());
        List<EquipInspectionTemplItem> itemList = equipInspectionTempl.getItemList();
        for (EquipInspectionTemplItem item: itemList){
            item.setOpenid(super.generateOpenid());
            item.setEquipInspectionTemplOpenid(openid);
            item.setCreateBy(SecurityUtils.getUsername());
            item.setCreateTime(new Date());
            if(ObjectUtil.isNull(item.getUseTime()) || StrUtil.isEmpty(item.getUseTime())){
                item.setUseTime("0");
            }
        }
        equipInspectionTemplItemService.saveBatch(itemList);
        return toBooleanAjax(equipInspectionTemplService.save(equipInspectionTempl));
    }

    /**
     * 修改点检模板表
     */
    @ApiOperation("修改点检模板表")
    @PutMapping
    public AjaxResult edit(@RequestBody EquipInspectionTempl equipInspectionTempl) {
        equipInspectionTempl.setUpdateTime(new Date());
        equipInspectionTempl.setUpdateBy(SecurityUtils.getUsername());

        String templOpenid = equipInspectionTempl.getOpenid();

        List<EquipInspectionTemplItem> itemList = equipInspectionTempl.getItemList();

        //删除旧的子数据
        equipInspectionTemplItemService.remove(
                Wrappers.<EquipInspectionTemplItem>lambdaQuery()
                        .eq(EquipInspectionTemplItem::getEquipInspectionTemplOpenid,templOpenid)
        );

        //构建新的子数据
        for (EquipInspectionTemplItem item :itemList){
            if (item.getOpenid()==null||"".equals(item.getOpenid())){
                item.setOpenid(super.generateOpenid());
            }
            item.setEquipInspectionTemplOpenid(templOpenid);
            item.setCreateBy(SecurityUtils.getUsername());
            item.setCreateTime(new Date());
            if(ObjectUtil.isNull(item.getUseTime()) || StrUtil.isEmpty(item.getUseTime())){
                item.setUseTime("0");
            }
        }

        //保存新的子数据
        equipInspectionTemplItemService.saveBatch(itemList);
        return toBooleanAjax(equipInspectionTemplService.updateById(equipInspectionTempl));
    }

    /**
     * 删除点检模板表
     */
    @ApiOperation("删除点检模板表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        List<EquipInspectionTempl> templList = equipInspectionTemplService.list(
                Wrappers.<EquipInspectionTempl>lambdaQuery()
                        .in(EquipInspectionTempl::getId, ids)
        );
        if (templList!=null&&templList.size()>0){
            List<String> openids = new ArrayList<>();
            for (EquipInspectionTempl eis:templList){
                openids.add(eis.getOpenid());
            }
            equipInspectionTemplItemService.remove(
                    Wrappers.<EquipInspectionTemplItem>lambdaQuery()
                            .in(EquipInspectionTemplItem::getEquipInspectionTemplOpenid,openids)
            );
        }
        return toBooleanAjax(equipInspectionTemplService.removeByIds(Arrays.asList(ids)));
    }


    /**
     * 获取最新点检模板编号
     */
    private String getNewCode(){

        String prefix = DateUtil.format(new Date(),"yyyyMMdd");

        List<EquipInspectionTempl> templList = equipInspectionTemplService.list(
                Wrappers.<EquipInspectionTempl>lambdaQuery()
                        .like(EquipInspectionTempl::getCode,prefix)
        );
        int max = 0;
        if (templList!=null&&templList.size()>0){
            for (EquipInspectionTempl emt:templList){
                String code = emt.getCode();
                int num = Integer.parseInt(code.replace(prefix,""));
                if (num>max){
                    max = num;
                }
            }
        }
        return prefix+String.format("%02d", max+1);
    }

}
