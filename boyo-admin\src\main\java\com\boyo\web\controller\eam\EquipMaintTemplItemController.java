package com.boyo.web.controller.eam;

import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.eam.domain.EquipMaintTemplItem;
import com.boyo.eam.service.IEquipMaintTemplItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 设备-维保模板-维保项目(EquipMaintTemplItem)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-10 11:07:31
 */
@Api("设备-维保模板-维保项目")
@RestController
@RequestMapping("/equip/equipMaintTemplItem")
@AllArgsConstructor
public class EquipMaintTemplItemController extends BaseController{
    /**
     * 服务对象
     */
    private final IEquipMaintTemplItemService equipMaintTemplItemService;

    /**
     * 查询设备-维保模板-维保项目列表
     *
     */
    @ApiOperation("查询设备-维保模板-维保项目列表")
    @GetMapping("/list")
    public TableDataInfo list(EquipMaintTemplItem equipMaintTemplItem) {
        startPage();
        List<EquipMaintTemplItem> list = equipMaintTemplItemService.selectEquipMaintTemplItemList(equipMaintTemplItem);
        return getDataTable(list);
    }
    
    /**
     * 获取设备-维保模板-维保项目详情
     */
    @ApiOperation("获取设备-维保模板-维保项目详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(equipMaintTemplItemService.getById(id));
    }

    /**
     * 新增设备-维保模板-维保项目
     */
    @ApiOperation("新增设备-维保模板-维保项目")
    @PostMapping
    public AjaxResult add(@RequestBody EquipMaintTemplItem equipMaintTemplItem) {
        return toBooleanAjax(equipMaintTemplItemService.save(equipMaintTemplItem));
    }

    /**
     * 修改设备-维保模板-维保项目
     */
    @ApiOperation("修改设备-维保模板-维保项目")
    @PutMapping
    public AjaxResult edit(@RequestBody EquipMaintTemplItem equipMaintTemplItem) {
        return toBooleanAjax(equipMaintTemplItemService.updateById(equipMaintTemplItem));
    }

    /**
     * 删除设备-维保模板-维保项目
     */
    @ApiOperation("删除设备-维保模板-维保项目")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(equipMaintTemplItemService.removeByIds(Arrays.asList(ids)));
    }

}
