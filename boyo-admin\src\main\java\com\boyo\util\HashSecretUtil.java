package com.boyo.util;



import com.google.common.base.Charsets;
import com.google.common.hash.HashFunction;
import com.google.common.hash.Hashing;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

public class HashSecretUtil {

	private static HashFunction sha1 = Hashing.sha1();

	public static String encodePassword(String password, String salt) {
		return sha1.hashString(password + "#" + salt, Charsets.UTF_8).toString();
	}

	public static String createSalt(String name) {
		return sha1.hashString(name + "#" + System.currentTimeMillis() + "#" + RandomStringUtils.random(20), Charsets.UTF_8).toString();
	}



	/**
	 * 生成BCryptPasswordEncoder密码
	 *
	 * @param password 密码
	 * @return 加密字符串
	 */
	public static String encryptPassword(String password)
	{
		BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
		return passwordEncoder.encode(password);
	}

	/**
	 * 判断密码是否相同
	 *
	 * @param rawPassword 真实密码
	 * @param encodedPassword 加密后字符
	 * @return 结果
	 */
	public static boolean matchesPassword(String rawPassword, String encodedPassword)
	{
		BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
		return passwordEncoder.matches(rawPassword, encodedPassword);
	}


	public static String sha1(String string) {
		return sha1.hashString(string, Charsets.UTF_8).toString();
	}
	
	public static void main(String[] args) {
		String pwd=encodePassword("Qq123456","53008a3727dedc1864a90f44804ef9b1c6046d90");
		System.out.println(pwd);
		String result = sha1.hashString("Qq123456"+"#"+"53008a3727dedc1864a90f44804ef9b1c6046d90", Charsets.UTF_8).toString();
		System.out.println(result);
	}
}
