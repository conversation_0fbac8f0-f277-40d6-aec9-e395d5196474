package com.boyo.master.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boyo.master.domain.EnterpriseConstant;
import com.boyo.master.domain.annotations.DataAsset;
import com.boyo.master.domain.annotations.Enterprise;
import com.boyo.master.entity.AccountBalance;
import com.boyo.master.mapper.AccountBalanceMapper;
import com.boyo.master.service.AccountBalanceService;
import com.boyo.master.utils.HttpUtil;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import com.boyo.master.service.DataAssetService;
import lombok.extern.slf4j.Slf4j;

import org.apache.iotdb.rpc.StatementExecutionException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.boyo.master.register.DataAssetRegister.getDataAssetType;

import java.text.ParseException;
import java.time.LocalDate;
import java.util.*;

/**
 * <AUTHOR>
 * @description 针对表【bu_enterprise_data_asset_account_balance】的数据库操作Service实现
 * @createDate 2024-11-16 14:16:13
 */
@Slf4j
@DataAsset("科目余额表")
@Service
public class AccountBalanceServiceImpl extends ServiceImpl<AccountBalanceMapper, AccountBalance>
        implements AccountBalanceService, DataAssetService {

    private String ncUrl = EnterpriseConstant.NCURL;

    @Override
    public Object getDataAssetList(IPage page, Map<String, String> params) {
        return this.page(page, new LambdaQueryWrapper<AccountBalance>()
                .eq(AccountBalance::getEnterpriseId, params.get(EnterpriseConstant.ENTERPRISE_ID))
                .eq(StrUtil.isNotEmpty(params.get("childrenCompany")), AccountBalance::getChildrenCompany, params.get("childrenCompany"))
                .eq(StrUtil.isNotEmpty(params.get("year")), AccountBalance::getYear, params.get("year"))
                .eq(StrUtil.isNotEmpty(params.get("period")), AccountBalance::getPeriod, params.get("period"))
                .like(StrUtil.isNotEmpty(params.get("subjname")), AccountBalance::getSubjname, params.get("subjname"))
        );
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncDataAssetHaihui() throws NoSuchMethodException, ParseException, IoTDBConnectionException, StatementExecutionException {
        Enterprise annotation = getCurrentMethodAnnotation(Enterprise.class);
        Long enterpriseId = Convert.toLong(annotation.value());
        final String dataAssetType = getDataAssetType(this);
        Map<String, String> company = (Map<String, String>) getSelectorList(dataAssetType, enterpriseId)
                .getOrDefault("company", new HashMap<>());
        for (String companyItem : company.keySet()) {
            for (int i = 1; i <=12 ; i++) {
                String period = i < 10 ? "0" + i : i + "";
                // 1. 拼装查询参数
                Map<String, Object> params = new HashMap<>(4);
//                params.put("period", LocalDate.now().minusDays(1).getMonthValue());
                params.put("period", period);
                params.put("year", LocalDate.now().minusDays(1).getYear());
                params.put("url", "api/nc57/index/accountBalance");
                params.put("unitname", companyItem);
                log.info("同步数据开始，查询参数【{}】", params.toString());
                // 2. 发送请求获取数据
                final ResponseEntity<?> responseEntity = HttpUtil.postData(params);
                String post = responseEntity.getBody().toString();
                // 3. 解析响应数据
                JSONObject response = JSON.parseObject(post);
                if (response==null){
                    continue;
                }
                String data = response.getString("data");
                if (StrUtil.isEmpty(data)) {
                    continue;
                }
                List<AccountBalance> dataList = new ArrayList<>();
                Collection<Object> accountBalances = JSON.parseObject(data).getJSONObject("data").values();
                for (Object item : accountBalances) {
                    dataList.add(JSON.toJavaObject((JSONObject) item, AccountBalance.class));
                }
                // 4. 循环处理数据
                List<String> remoteIdList = new ArrayList<>();
                for (AccountBalance item : dataList) {
                    item.setChildrenCompany(companyItem);
                    item.setEnterpriseId(enterpriseId);
                    item.setYear(params.get("year").toString());
                    item.setPeriod(params.get("period").toString());
                    item.setRemoteId(StrUtil.format("{}-{}-{}", item.getYear(), item.getPeriod(), item.getChildrenCompany()));
                    remoteIdList.add(item.getRemoteId());
                }
                // 5. 更新数据
//                this.remove(new LambdaQueryWrapper<AccountBalance>()
//                        .eq(AccountBalance::getEnterpriseId, enterpriseId)
//                        .in(AccountBalance::getRemoteId, remoteIdList)
//                );
                if (!this.saveBatch(dataList)) {
                    log.error("同步数据失败，查询参数【{}】", params.toString());
                }
            }
            }
    }
}




