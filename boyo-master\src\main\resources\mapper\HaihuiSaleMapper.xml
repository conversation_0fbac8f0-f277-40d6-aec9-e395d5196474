<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boyo.master.mapper.HaihuiSaleMapper">

    <resultMap type="com.boyo.master.domain.HaihuiSale" id="HaihuiSaleResult">
        <result property="htNo"    column="ht_no"    />
        <result property="payment"    column="payment"    />
        <result property="htlxzt"    column="htlxzt"    />
        <result property="state"    column="state"    />
        <result property="signatory"    column="signatory"    />
        <result property="amount"    column="amount"    />
        <result property="payament"    column="payament"    />
        <result property="nopayment"    column="nopayment"    />
        <result property="custName"    column="cust_name"    />
        <result property="startdate"    column="startdate"    />
        <result property="enddate"    column="enddate"    />
        <result property="saveTime"    column="save_time"    />
        <result property="remark"    column="remark"    />
        <result property="ex1"    column="ex1"    />
        <result property="ex2"    column="ex2"    />
    </resultMap>

    <sql id="selectHaihuiSaleVo">
        select ht_no, payment, htlxzt, state, signatory, amount, payament, nopayment, cust_name, startdate, enddate, remark, ex1, ex2 from haihui_sale
    </sql>

    <select id="selectHaihuiSaleList" parameterType="com.boyo.master.domain.HaihuiSale" resultMap="HaihuiSaleResult">
        <include refid="selectHaihuiSaleVo"/>
        <where>
            <if test="payment != null  and payment != ''"> and payment = #{payment}</if>
            <if test="htlxzt != null  and htlxzt != ''"> and htlxzt = #{htlxzt}</if>
            <if test="state != null  and state != ''"> and state = #{state}</if>
            <if test="signatory != null  and signatory != ''"> and signatory = #{signatory}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="payament != null "> and payament = #{payament}</if>
            <if test="nopayment != null "> and nopayment = #{nopayment}</if>
            <if test="custName != null  and custName != ''"> and cust_name like concat('%', #{custName}, '%')</if>
            <if test="startdate != null " > and startdate <![CDATA[>=]]> #{startdate}</if>
            <if test="enddate != null "> and startdate <![CDATA[<=]]> #{enddate}</if>
            <if test="ex1 != null  and ex1 != ''"> and ex1 = #{ex1}</if>
            <if test="ex2 != null  and ex2 != ''"> and ex2 = #{ex2}</if>
        </where>
        order by ht_no desc
    </select>

    <select id="selectHaihuiSaleByHtNo" parameterType="String" resultMap="HaihuiSaleResult">
        <include refid="selectHaihuiSaleVo"/>
        where ht_no = #{htNo}
    </select>

    <insert id="insertHaihuiSale" parameterType="com.boyo.master.domain.HaihuiSale">
        insert into haihui_sale
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="htNo != null">ht_no,</if>
            <if test="payment != null">payment,</if>
            <if test="htlxzt != null">htlxzt,</if>
            <if test="state != null">state,</if>
            <if test="signatory != null">signatory,</if>
            <if test="amount != null">amount,</if>
            <if test="payament != null">payament,</if>
            <if test="nopayment != null">nopayment,</if>
            <if test="custName != null">cust_name,</if>
            <if test="startdate != null">startdate,</if>
            <if test="enddate != null">enddate,</if>
            <if test="saveTime != null">save_time,</if>
            <if test="remark != null">remark,</if>
            <if test="ex1 != null">ex1,</if>
            <if test="ex2 != null">ex2,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="htNo != null">#{htNo},</if>
            <if test="payment != null">#{payment},</if>
            <if test="htlxzt != null">#{htlxzt},</if>
            <if test="state != null">#{state},</if>
            <if test="signatory != null">#{signatory},</if>
            <if test="amount != null">#{amount},</if>
            <if test="payament != null">#{payament},</if>
            <if test="nopayment != null">#{nopayment},</if>
            <if test="custName != null">#{custName},</if>
            <if test="startdate != null">#{startdate},</if>
            <if test="enddate != null">#{enddate},</if>
            <if test="saveTime != null">#{saveTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="ex1 != null">#{ex1},</if>
            <if test="ex2 != null">#{ex2},</if>
        </trim>
    </insert>

    <update id="updateHaihuiSale" parameterType="com.boyo.master.domain.HaihuiSale">
        update haihui_sale
        <trim prefix="SET" suffixOverrides=",">
            <if test="payment != null">payment = #{payment},</if>
            <if test="htlxzt != null">htlxzt = #{htlxzt},</if>
            <if test="state != null">state = #{state},</if>
            <if test="signatory != null">signatory = #{signatory},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="payament != null">payament = #{payament},</if>
            <if test="nopayment != null">nopayment = #{nopayment},</if>
            <if test="custName != null">cust_name = #{custName},</if>
            <if test="startdate != null">startdate = #{startdate},</if>
            <if test="enddate != null">enddate = #{enddate},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="ex1 != null">ex1 = #{ex1},</if>
            <if test="ex2 != null">ex2 = #{ex2},</if>
        </trim>
        where ht_no = #{htNo}
    </update>

    <delete id="deleteHaihuiSaleByHtNo" parameterType="String">
        delete from haihui_sale where ht_no = #{htNo}
    </delete>

    <delete id="deleteHaihuiSaleByHtNos" parameterType="String">
        delete from haihui_sale where ht_no in
        <foreach item="htNo" collection="array" open="(" separator="," close=")">
            #{htNo}
        </foreach>
    </delete>
</mapper>