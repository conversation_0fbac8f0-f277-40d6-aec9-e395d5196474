package com.boyo.web.controller.iot;

import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.iot.domain.IotTslAttr;
import com.boyo.iot.service.IIotTslAttrService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * IoT物模型属性Controller
 *
 * <AUTHOR>
 */
@Api("IoT物模型属性")
@RestController
@RequestMapping("/iot/tslAttr")
@AllArgsConstructor
public class IotTslAttrController extends BaseController {
    private final IIotTslAttrService iotTslAttrService;

    /**
     * 查询IoT物模型属性列表
     */
    @ApiOperation("查询IoT物模型属性列表")
    @GetMapping("/list")
    public TableDataInfo list(IotTslAttr iotTslAttr) {
        startPage();
        List<IotTslAttr> list = iotTslAttrService.selectIotTslAttrList(iotTslAttr);
        return getDataTable(list);
    }

    @ApiOperation("查询IoT物模型下的属性信息")
    @GetMapping("/listAttr")
    public AjaxResult listAttr(Long tslId) {
        return AjaxResult.success(iotTslAttrService.listAttr(tslId));
    }

    /**
     * 获取IoT物模型属性详细信息
     */
    @ApiOperation("获取IoT物模型属性详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(iotTslAttrService.getById(id));
    }

    /**
     * 新增IoT物模型属性
     */
    @ApiOperation("新增IoT物模型属性")
    @PostMapping
    public AjaxResult add(@RequestBody IotTslAttr iotTslAttr) {
        return toBooleanAjax(iotTslAttrService.saveOrUpdate(iotTslAttr));
    }

    /**
     * 修改IoT物模型属性
     */
    @ApiOperation("修改IoT物模型属性")
    @PutMapping
    public AjaxResult edit(@RequestBody IotTslAttr iotTslAttr) {
        return toBooleanAjax(iotTslAttrService.updateById(iotTslAttr));
    }

    /**
     * 删除IoT物模型属性
     */
    @ApiOperation("删除IoT物模型属性")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(iotTslAttrService.removeByIds(Arrays.asList(ids)));
    }
}
