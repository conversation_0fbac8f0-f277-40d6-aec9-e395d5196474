package com.boyo.web.controller.eam;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.eam.domain.*;
import com.boyo.eam.domain.VO.EquipMaintRecordTaskVO;
import com.boyo.eam.service.IEquipMaintRecordService;
import com.boyo.eam.service.IEquipMaintTaskItemDetailService;
import com.boyo.eam.service.IEquipMaintTaskItemService;
import com.boyo.eam.service.IEquipMaintTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 维保记录表(EquipMaintRecord)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-19 14:56:16
 */
@Api("维保记录表")
@RestController
@RequestMapping("/equip/equipMaintRecord")
@AllArgsConstructor
public class EquipMaintRecordController extends BaseController{
    /**
     * 服务对象
     */
    private final IEquipMaintRecordService equipMaintRecordService;
    private final IEquipMaintTaskService equipMaintTaskService;
    private final IEquipMaintTaskItemService equipMaintTaskItemService;
    private final IEquipMaintTaskItemDetailService equipMaintTaskItemDetailService;

    /**
     * 查询维保记录表列表
     *
     */
    @ApiOperation("查询维保记录表列表")
    @GetMapping("/list")
    public TableDataInfo list(EquipMaintRecord equipMaintRecord) {
        startPage();
        List<EquipMaintRecord> list = equipMaintRecordService.selectEquipMaintRecordList(equipMaintRecord);
        return getDataTable(list);
    }
    
    /**
     * 获取维保记录表详情
     */
    @ApiOperation("获取维保记录表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(equipMaintRecordService.getById(id));
    }

    /**
     * 新增维保记录表
     */
    @ApiOperation("新增维保记录表")
    @PostMapping
    public AjaxResult add(@RequestBody EquipMaintRecord equipMaintRecord) {
        return toBooleanAjax(equipMaintRecordService.save(equipMaintRecord));
    }

    /**
     * 修改维保记录表
     */
    @ApiOperation("修改维保记录表")
    @PutMapping
    public AjaxResult edit(@RequestBody EquipMaintRecord equipMaintRecord) {
        return toBooleanAjax(equipMaintRecordService.updateById(equipMaintRecord));
    }

    /**
     * 删除维保记录表
     */
    @ApiOperation("删除维保记录表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(equipMaintRecordService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 维保记录查询
     */
    @ApiOperation("查询维保记录表列表")
    @GetMapping("/list/task")
    public AjaxResult listTask(EquipMaintRecordTaskVO equipMaintRecordTaskVO) {

        List<EquipMaintRecordTaskVO> recordTaskList = equipMaintRecordService.selectTask(equipMaintRecordTaskVO);

        return AjaxResult.success(recordTaskList);
    }


    // 移动端

    /**
     * 修改维保记录表
     */
    @ApiOperation("修改维保记录表")
    @PutMapping("/pad")
    public AjaxResult padEdit(@RequestBody EquipMaintRecord equipMaintRecord) {
        String openid = equipMaintRecord.getOpenid();
        EquipMaintRecord record = equipMaintRecordService.getOne(
                Wrappers.<EquipMaintRecord>lambdaQuery()
                        .eq(EquipMaintRecord::getOpenid, openid)
        );
        record.setPass(equipMaintRecord.getPass());
        record.setMaintRemark(equipMaintRecord.getMaintRemark());
        record.setMediaId(equipMaintRecord.getMediaId());
        record.setActualValue(equipMaintRecord.getActualValue());
        long minute = DateUtil.between(record.getBeginDate(), new Date(), DateUnit.MINUTE);
        record.setResponseMinute(new Long(minute).intValue()==0?1:new Long(minute).intValue());
        record.setUpdateTime(new Date());
        record.setUpdateBy(SecurityUtils.getUsername());
        return toBooleanAjax(equipMaintRecordService.updateById(record));
    }


}
