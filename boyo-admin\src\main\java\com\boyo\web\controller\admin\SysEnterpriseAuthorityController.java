package com.boyo.web.controller.admin;

import java.util.List;
import java.util.Arrays;

import com.boyo.common.core.page.TableDataInfo;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.boyo.system.domain.SysEnterpriseAuthority;
import com.boyo.system.service.ISysEnterpriseAuthorityService;

/**
 * 企业权限管理Controller
 *
 * <AUTHOR>
 */
@Api("企业权限管理")
@RestController
@RequestMapping("/system/authority")
@AllArgsConstructor
public class SysEnterpriseAuthorityController extends BaseController {
    private final ISysEnterpriseAuthorityService sysEnterpriseAuthorityService;

    /**
     * 查询企业权限管理列表
     */
    @ApiOperation("查询企业权限管理列表")
    @GetMapping("/list")
    public TableDataInfo list(SysEnterpriseAuthority sysEnterpriseAuthority) {
        startPage();
        List<SysEnterpriseAuthority> list = sysEnterpriseAuthorityService.selectSysEnterpriseAuthorityList(sysEnterpriseAuthority);
        return getDataTable(list);
    }

    /**
     * 新增企业权限管理
     */
    @ApiOperation("新增企业权限管理")
    @PostMapping
    public AjaxResult add(@RequestBody List<SysEnterpriseAuthority> sysEnterpriseAuthority) {

        return toBooleanAjax(sysEnterpriseAuthorityService.saveBatch(sysEnterpriseAuthority));
    }

}
