package com.boyo.web.controller.master;

import java.util.List;
import java.util.Arrays;

import com.boyo.master.vo.SupplierVO;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.boyo.master.domain.TSupplier;
import com.boyo.master.service.ITSupplierService;

/**
 * 供应商管理Controller
 *
 * <AUTHOR>
 */
@Api("供应商管理")
@RestController
@RequestMapping("/master/supplier")
@AllArgsConstructor
public class SupplierController extends BaseController {
    private final ITSupplierService tSupplierService;

    /**
     * 查询供应商管理列表
     */
    @ApiOperation("查询供应商管理列表")
    @GetMapping("/list")
    public TableDataInfo list(TSupplier tSupplier) {
        startPage();
        List<SupplierVO> list = tSupplierService.selectTSupplierList(tSupplier);
        return getDataTable(list);
    }

    /**
     * 获取供应商管理详细信息
     */
    @ApiOperation("获取供应商管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(tSupplierService.getById(id));
    }

    /**
     * 新增供应商管理
     */
    @ApiOperation("新增供应商管理")
    @PostMapping
    public AjaxResult add(@RequestBody TSupplier tSupplier) {
        tSupplier.setSupplierOpenid(super.generateOpenid());
        return toBooleanAjax(tSupplierService.save(tSupplier));
    }

    /**
     * 修改供应商管理
     */
    @ApiOperation("修改供应商管理")
    @PutMapping
    public AjaxResult edit(@RequestBody TSupplier tSupplier) {
        return toBooleanAjax(tSupplierService.updateById(tSupplier));
    }

    /**
     * 删除供应商管理
     */
    @ApiOperation("删除供应商管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(tSupplierService.removeByIds(Arrays.asList(ids)));
    }
}
