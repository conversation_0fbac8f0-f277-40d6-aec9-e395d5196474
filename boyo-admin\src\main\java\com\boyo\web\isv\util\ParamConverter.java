package com.boyo.web.isv.util;

import com.boyo.web.isv.model.ExpireLicenseReq;
import com.boyo.web.isv.model.GetLicenseReq;
import com.boyo.web.isv.model.RefreshLicenseReq;
import com.boyo.web.isv.model.ReleaseLicenseReq;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

public class ParamConverter {
    public static GetLicenseReq convertGetLicenseReq(Map<String, String[]> paramsMap) {
        GetLicenseReq getLicenseReq = new GetLicenseReq();

        getLicenseReq.setAuthToken(getPropertyFromMap("authToken", paramsMap));
        getLicenseReq.setTimeStamp(getPropertyFromMap("timeStamp", paramsMap));
        getLicenseReq.setCustomerId(getPropertyFromMap("customerId", paramsMap));
        getLicenseReq.setCustomerName(getPropertyFromMap("customerName", paramsMap));
        getLicenseReq.setBusinessId(getPropertyFromMap("businessId", paramsMap));
        getLicenseReq.setOrderId(getPropertyFromMap("orderId", paramsMap));
        getLicenseReq.setSkuCode(getPropertyFromMap("skuCode", paramsMap));
        getLicenseReq.setProductId(getPropertyFromMap("productId", paramsMap));
        getLicenseReq.setExpireTime(getPropertyFromMap("expireTime", paramsMap));
        getLicenseReq.setPeriodType(getPropertyFromMap("periodType", paramsMap));
        getLicenseReq.setSaasExtendParams(getPropertyFromMap("saasExtendParams", paramsMap));

        String amount = getPropertyFromMap("amount", paramsMap);
        getLicenseReq.setAmount(StringUtils.isNotEmpty(amount) ? Integer.parseInt(amount) : null);

        String provisionType = getPropertyFromMap("provisionType", paramsMap);
        getLicenseReq.setProvisionType(StringUtils.isNotEmpty(provisionType) ? Integer.parseInt(provisionType) : null);

        String chargingMode = getPropertyFromMap("chargingMode", paramsMap);
        getLicenseReq.setChargingMode(StringUtils.isNotEmpty(chargingMode) ? Integer.parseInt(chargingMode) : null);

        String periodNumber = getPropertyFromMap("periodNumber", paramsMap);
        getLicenseReq.setPeriodNumber(StringUtils.isNotEmpty(periodNumber) ? Integer.parseInt(periodNumber) : null);

        return getLicenseReq;
    }

    public static RefreshLicenseReq convertRefreshLicenseReq(Map<String, String[]> paramsMap) {
        RefreshLicenseReq refreshLicenseReq = new RefreshLicenseReq();

        refreshLicenseReq.setOrderId(getPropertyFromMap("orderId", paramsMap));
        refreshLicenseReq.setInstanceId(getPropertyFromMap("instanceId", paramsMap));
        refreshLicenseReq.setProductId(getPropertyFromMap("productId", paramsMap));
        refreshLicenseReq.setExpireTime(getPropertyFromMap("expireTime", paramsMap));
        refreshLicenseReq.setAuthToken(getPropertyFromMap("authToken", paramsMap));
        refreshLicenseReq.setTimeStamp(getPropertyFromMap("timeStamp", paramsMap));
        refreshLicenseReq.setPeriodType(getPropertyFromMap("periodType", paramsMap));

        String periodNumber = getPropertyFromMap("periodNumber", paramsMap);
        refreshLicenseReq.setPeriodNumber(StringUtils.isNotEmpty(periodNumber) ? Integer.parseInt(periodNumber) : null);

        return refreshLicenseReq;
    }

    public static ExpireLicenseReq convertExpireLicenseReq(Map<String, String[]> paramsMap) {
        ExpireLicenseReq expireLicenseReq = new ExpireLicenseReq();

        expireLicenseReq.setInstanceId(getPropertyFromMap("instanceId", paramsMap));
        expireLicenseReq.setOrderId(getPropertyFromMap("orderId", paramsMap));
        expireLicenseReq.setAuthToken(getPropertyFromMap("authToken", paramsMap));
        expireLicenseReq.setTimeStamp(getPropertyFromMap("timeStamp", paramsMap));

        return expireLicenseReq;
    }

    public static ReleaseLicenseReq convertReleaseLicenseReq(Map<String, String[]> paramsMap) {
        ReleaseLicenseReq releaseLicenseReq = new ReleaseLicenseReq();

        releaseLicenseReq.setInstanceId(getPropertyFromMap("instanceId", paramsMap));
        releaseLicenseReq.setOrderId(getPropertyFromMap("orderId", paramsMap));
        releaseLicenseReq.setAuthToken(getPropertyFromMap("authToken", paramsMap));
        releaseLicenseReq.setTimeStamp(getPropertyFromMap("timeStamp", paramsMap));

        return releaseLicenseReq;
    }

    public static String getPropertyFromMap(String property, Map<String, String[]> paramsMap) {
        if (null != paramsMap && null != paramsMap.get(property) && paramsMap.get(property).length > 0) {
            return paramsMap.get(property)[0];
        }

        return null;
    }
}
