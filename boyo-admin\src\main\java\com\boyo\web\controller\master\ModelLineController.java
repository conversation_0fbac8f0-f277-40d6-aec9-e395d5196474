package com.boyo.web.controller.master;

import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.master.domain.ModelLine;
import com.boyo.master.service.IModelLineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 工厂模型-产线Controller
 *
 * <AUTHOR>
 */
@Api("工厂模型-产线")
@RestController
@RequestMapping("/master/line")
@AllArgsConstructor
public class ModelLineController extends BaseController {
    private final IModelLineService modelLineService;

    /**
     * 查询工厂模型-产线列表
     */
    @ApiOperation("查询工厂模型-产线列表")
    @GetMapping("/list")
    public TableDataInfo list(ModelLine modelLine) {
        startPage();
        List<ModelLine> list = modelLineService.selectModelLineList(modelLine);
        return getDataTable(list);
    }

    /**
     * 获取工厂模型-产线详细信息
     */
    @ApiOperation("获取工厂模型-产线详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(modelLineService.getById(id));
    }

    /**
     * 新增工厂模型-产线
     */
    @ApiOperation("新增工厂模型-产线")
    @PostMapping
    public AjaxResult add(@RequestBody ModelLine modelLine) {
        modelLine.setLineOpenid(super.generateOpenid());
        return toBooleanAjax(modelLineService.save(modelLine));
    }

    /**
     * 修改工厂模型-产线
     */
    @ApiOperation("修改工厂模型-产线")
    @PutMapping
    public AjaxResult edit(@RequestBody ModelLine modelLine) {
        return toBooleanAjax(modelLineService.updateById(modelLine));
    }

    /**
     * 删除工厂模型-产线
     */
    @ApiOperation("删除工厂模型-产线")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(modelLineService.removeByIds(Arrays.asList(ids)));
    }
}
