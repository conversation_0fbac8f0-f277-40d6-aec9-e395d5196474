package com.boyo.web.controller.thirdParty.Huaxia;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

@Data
public class QueryBo {




    /*
    * startTime: 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    String startTime;

    /*
    * endTime: 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    String endTime;


    /*
    * uscc: 统一社会信用代码
     */
    String uscc;

    /*
    * companyName: 公司名称
     */
    String companyName;

}
