package com.boyo.web.controller.eam;

import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.eam.domain.Media;
import com.boyo.eam.service.IMediaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * (Media)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-05 10:30:24
 */
@Api("")
@RestController
@RequestMapping("/equip/media")
@AllArgsConstructor
public class MediaController extends BaseController{
    /**
     * 服务对象
     */
    private final IMediaService mediaService;

    /**
     * 查询列表
     *
     */
    @ApiOperation("查询列表")
    @GetMapping("/list")
    public TableDataInfo list(Media media) {
        startPage();
        List<Media> list = mediaService.selectMediaList(media);
        return getDataTable(list);
    }
    
    /**
     * 获取详情
     */
    @ApiOperation("获取详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(mediaService.getById(id));
    }

    /**
     * 新增
     */
    @ApiOperation("新增")
    @PostMapping
    public AjaxResult add(@RequestBody Media media) {
        return toBooleanAjax(mediaService.save(media));
    }

    /**
     * 修改
     */
    @ApiOperation("修改")
    @PutMapping
    public AjaxResult edit(@RequestBody Media media) {
        return toBooleanAjax(mediaService.updateById(media));
    }

    /**
     * 删除
     */
    @ApiOperation("删除")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(mediaService.removeByIds(Arrays.asList(ids)));
    }

}
