package com.boyo.web.controller.master;

import java.util.List;
import java.util.Arrays;

import com.boyo.common.utils.uuid.IdUtils;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.boyo.master.domain.BaseDict;
import com.boyo.master.service.IBaseDictService;

/**
 * 租户数据字典管理Controller
 *
 * <AUTHOR>
 */
@Api("租户数据字典管理")
@RestController
@RequestMapping("/master/dict")
@AllArgsConstructor
public class BaseDictController extends BaseController {
    private final IBaseDictService baseDictService;

    /**
     * 查询租户数据字典管理列表
     */
    @ApiOperation("查询租户数据字典管理列表")
    @GetMapping("/list")
    public TableDataInfo list(BaseDict baseDict) {
        startPage();
        List<BaseDict> list = baseDictService.selectBaseDictList(baseDict);
        return getDataTable(list);
    }

    /**
     * 获取租户数据字典管理详细信息
     */
    @ApiOperation("获取租户数据字典管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(baseDictService.getById(id));
    }

    /**
     * 新增租户数据字典管理
     */
    @ApiOperation("新增租户数据字典管理")
    @PostMapping
    public AjaxResult add(@RequestBody BaseDict baseDict) {
        baseDict.setOpenid(IdUtils.fastSimpleUUID());
        return toBooleanAjax(baseDictService.save(baseDict));
    }

    /**
     * 修改租户数据字典管理
     */
    @ApiOperation("修改租户数据字典管理")
    @PutMapping
    public AjaxResult edit(@RequestBody BaseDict baseDict) {
        return toBooleanAjax(baseDictService.updateById(baseDict));
    }

    /**
     * 删除租户数据字典管理
     */
    @ApiOperation("删除租户数据字典管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(baseDictService.removeByIds(Arrays.asList(ids)));
    }
}
