package com.boyo.web.controller.eam;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.eam.domain.EquipMaintTempl;
import com.boyo.eam.domain.EquipMaintTemplItem;
import com.boyo.eam.domain.EquipMaintTemplItemDetail;
import com.boyo.eam.service.IEquipMaintTemplItemDetailService;
import com.boyo.eam.service.IEquipMaintTemplItemService;
import com.boyo.eam.service.IEquipMaintTemplService;
import com.boyo.framework.annotation.Tenant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 设备-维保模板(EquipMaintTempl)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-10 11:07:30
 */
@Api("设备-维保模板")
@RestController
@RequestMapping("/equip/equipMaintTempl")
@AllArgsConstructor
@Tenant
public class EquipMaintTemplController extends BaseController{
    /**
     * 服务对象
     */
    private final IEquipMaintTemplService equipMaintTemplService;
    private final IEquipMaintTemplItemService equipMaintTemplItemService;
    private final IEquipMaintTemplItemDetailService equipMaintTemplItemDetailService;
    /**
     * 查询设备-维保模板列表
     *
     */
    @ApiOperation("查询设备-维保模板列表")
    @GetMapping("/list")
    public TableDataInfo list(EquipMaintTempl equipMaintTempl) {
        startPage();
        List<EquipMaintTempl> list = equipMaintTemplService.selectEquipMaintTemplList(equipMaintTempl);
        return getDataTable(list);
    }
    
    /**
     * 获取设备-维保模板详情
     */
    @ApiOperation("获取设备-维保模板详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        EquipMaintTempl equipMaintTempl = equipMaintTemplService.getById(id);
        List<EquipMaintTemplItem> itemList = equipMaintTemplItemService.list(
            Wrappers.<EquipMaintTemplItem>lambdaQuery()
                .eq(EquipMaintTemplItem::getEquipMaintTemplOpenid,equipMaintTempl.getOpenid())
        );
        for (EquipMaintTemplItem emti:itemList){
            emti.setEquipMaintTemplItemDetails(equipMaintTemplItemDetailService.list(
                Wrappers.<EquipMaintTemplItemDetail>lambdaQuery()
                    .eq(EquipMaintTemplItemDetail::getEquipMaintTemplItemOpenid,emti.getOpenid())
            ));
        }
        equipMaintTempl.setEquipMaintTemplItems(itemList);
        return AjaxResult.success(equipMaintTempl);
    }

    /**
     * 新增设备-维保模板
     */
    @ApiOperation("新增设备-维保模板")
    @Transactional
    @PostMapping
    public AjaxResult add(@RequestBody EquipMaintTempl equipMaintTempl) {
        String templOpenid = super.generateOpenid();
        equipMaintTempl.setOpenid(templOpenid);
        List<EquipMaintTemplItem> items = equipMaintTempl.getEquipMaintTemplItems();
        if (items!=null&&items.size()>0){
            for (EquipMaintTemplItem emti:items){
                String templItemOpenid = super.generateOpenid();
                emti.setOpenid(templItemOpenid);
                emti.setEquipMaintTemplOpenid(templOpenid);
                List<EquipMaintTemplItemDetail> details = emti.getEquipMaintTemplItemDetails();
                if (details!=null&&details.size()>0){
                    for (EquipMaintTemplItemDetail emtid:details){
                        String templItemDetailOpenid = super.generateOpenid();
                        emtid.setOpenid(templItemDetailOpenid);
                        emtid.setEquipMaintTemplItemOpenid(templItemOpenid);
                    }
                    equipMaintTemplItemDetailService.saveBatch(details);
                }
            }
            equipMaintTemplItemService.saveBatch(items);
        }
        return toBooleanAjax(equipMaintTemplService.save(equipMaintTempl));
    }

    /**
     * 修改设备-维保模板
     */
    @ApiOperation("修改设备-维保模板")
    @Transactional
    @PutMapping
    public AjaxResult edit(@RequestBody EquipMaintTempl equipMaintTempl) {
        String templOpenid = equipMaintTempl.getOpenid();
        List<EquipMaintTemplItem> items = equipMaintTempl.getEquipMaintTemplItems();

        //删除旧数据
        removeChild(templOpenid);

        //插入新数据
        if (items!=null&&items.size()>0){
            for (EquipMaintTemplItem emti:items){
                if (emti.getOpenid()==null||"".equals(emti.getOpenid())){
                    emti.setOpenid(super.generateOpenid());
                }
                emti.setEquipMaintTemplOpenid(templOpenid);
                List<EquipMaintTemplItemDetail> details = emti.getEquipMaintTemplItemDetails();
                if (details!=null&&details.size()>0){
                    String templItemOpenid = emti.getOpenid();
                    for (EquipMaintTemplItemDetail emtid:details){
                        if (emtid.getOpenid()==null||"".equals(emtid.getOpenid())){
                            emtid.setOpenid(super.generateOpenid());
                        }
                        emtid.setEquipMaintTemplItemOpenid(templItemOpenid);
                    }
                    equipMaintTemplItemDetailService.saveBatch(details);
                }
            }
            equipMaintTemplItemService.saveBatch(items);
        }


        return toBooleanAjax(equipMaintTemplService.updateById(equipMaintTempl));
    }

    /**
     * 删除设备-维保模板
     */
    @ApiOperation("删除设备-维保模板")
    @Transactional
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        List<EquipMaintTempl> templList = equipMaintTemplService.list(
                Wrappers.<EquipMaintTempl>lambdaQuery()
                        .in(EquipMaintTempl::getId, ids)
        );
        for (EquipMaintTempl emt:templList){
            removeChild(emt.getOpenid());
        }
        return toBooleanAjax(equipMaintTemplService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 删除模板下额子模块
     * @param templOpenid
     * @return
     */
    private boolean removeChild(String templOpenid){
        // 删除子模块
        List<EquipMaintTemplItem> delItems = equipMaintTemplItemService.list(
                Wrappers.<EquipMaintTemplItem>lambdaQuery()
                        .eq(EquipMaintTemplItem::getEquipMaintTemplOpenid, templOpenid)
        );
        if (delItems!=null&&delItems.size()>0){
            for (EquipMaintTemplItem emti:delItems){
                equipMaintTemplItemDetailService.remove(
                        Wrappers.<EquipMaintTemplItemDetail>lambdaQuery()
                                .eq(EquipMaintTemplItemDetail::getEquipMaintTemplItemOpenid,emti.getOpenid())
                );
            }
        }
        return equipMaintTemplItemService.remove(
                Wrappers.<EquipMaintTemplItem>lambdaQuery()
                        .eq(EquipMaintTemplItem::getEquipMaintTemplOpenid,templOpenid)
        );
    }
}
