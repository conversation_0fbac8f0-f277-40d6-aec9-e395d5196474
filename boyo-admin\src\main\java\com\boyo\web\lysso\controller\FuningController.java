package com.boyo.web.lysso.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.common.core.domain.model.LoginBody;
import com.boyo.common.utils.StringUtils;
import com.boyo.framework.web.service.SysLoginService;
import com.boyo.system.domain.*;
import com.boyo.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

@RestController
public class FuningController extends BaseController {
    @Autowired
    private ITSysEnterpriseService tSysEnterpriseService;
    @Autowired
    private SysLoginService sysLoginService;
    @Autowired
    private IEnterpriseUserService enterpriseUserService;
    @Autowired
    private IEnterpriseDepartmentService departmentService;
    @Autowired
    private ISysEnterpriseAuthorityService sysEnterpriseAuthorityService;
    @Autowired
    private IEnterpriseRoleService enterpriseRoleService;
    @Autowired
    private IEnterpriseRoleFunctionService roleFunctionService;
    private static final String appKey = "AAP_FNPT";
    private static final String appSecret = "WESDFGGHDF89DF";

    private static final String[] function = {"2168","2252","2258","2188","2189","2190","2191","1","100","101",
            "102","103","104","105","106","2364","2365","2337","2338","2352",
            "2339","2340","2354","2355","2356","2341","2342","2343","2344","2345",
            "2346","2347","2357","2348","2349","2350","2351","2361","2362","2363",
            "2383","2384","2385","2386","2387","2388","2391","2392","2394",
            "2366", "2367", "2368", "2369", "2370", "2371", "2372", "2373", "2374", "2375", "2389", "2390", "2393"};


    public static String generateRandomString(int length) {
        String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int index = random.nextInt(characters.length());
            sb.append(characters.charAt(index));
        }
        return sb.toString();
    }
    @PostMapping("/sso/login/funing")
    public AjaxResult ssoLogin(@RequestParam String casticket, @RequestParam String enttoken, HttpServletRequest request) throws Exception {
        String trace = generateRandomString(10);

        // 获取accessToken
        HttpRequest request1 = HttpRequest.builder()
                .setUrl("https://www.yundeeiot.com/cascenter/get/access/token")
                .setMethod(HttpRequest.Method.POST)
                .addHeader("Content-Type", "application/json; charset=UTF-8")
                .addHeader("traceId", trace)
                .addPostData("appId", appKey)
                .addPostData("appSecret", appSecret);
        HttpResponse response = request1.post();

        JSONObject responseJson = JSONObject.parseObject(response.getText());
        System.out.println(responseJson);
        String accessToken = responseJson.getJSONObject("data").getString("accessToken");

        //获取企业信息
        HttpRequest request2 = HttpRequest.builder()
                .setUrl("https://www.yundeeiot.com/cascenter/get/enterprise/info/by/token")
                .setMethod(HttpRequest.Method.POST)
                .addHeader("Content-Type", "application/json; charset=UTF-8")
                .addHeader("traceId", trace)
                .addPostData("entToken", enttoken)
                .addPostData("accessToken", accessToken);
        HttpResponse response1 = request2.post();

        JSONObject responseJson1 = JSONObject.parseObject(response1.getText());
        String company = responseJson1.getJSONObject("data").getString("enterpriseName");
        String username = responseJson1.getJSONObject("data").getString("contactName");
        String phone = responseJson1.getJSONObject("data").getString("contactPhone");
        String rid = responseJson1.getJSONObject("data").getString("vtenantId");

        TSysEnterprise tSysEnterprise = new TSysEnterprise();
        tSysEnterprise.setEnterpriseOpenid(super.generateOpenid());
        tSysEnterprise.setEnterpriseName(company);
        tSysEnterprise.setEnterpriseAbbreviation(company);
        tSysEnterprise.setEnterpriseCode(company);
        tSysEnterprise.setEnterpriseContacts(username);
        tSysEnterprise.setEnterprisePhone(phone);
        tSysEnterprise.setEnterpriseEmail("<EMAIL>");

        //检查企业是否存在，存在即可直接登陆
        if(tSysEnterpriseService.checkExist(tSysEnterprise)){
            QueryWrapper<TSysEnterprise> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("enterprise_name", company);
            TSysEnterprise one = tSysEnterpriseService.getOne(queryWrapper);
            //List<OrgMemberDTO> orgMembers = userDTO.getOrgMembers();
            QueryWrapper<EnterpriseUser> query = new QueryWrapper<>();
            query.eq("enterprise_openid",one.getEnterpriseOpenid());
            query.eq("user_name",username);
            EnterpriseUser employee = enterpriseUserService.getOne(query);
            if (ObjectUtil.isNull(employee)){
                //创建员工
                EnterpriseUser enterpriseUser = new EnterpriseUser();
                enterpriseUser.setEnterpriseOpenid(one.getEnterpriseOpenid());
                enterpriseUser.setUserPhone(phone);
                enterpriseUser.setUserStatus("1");
                enterpriseUser.setUserPassword("$2a$10$FVfE/Lv7gtWv1S9DP5qPzuPAXhD09Vr6bm6uxH2WVJAuVWBb2.rvG");
                enterpriseUser.setUserFullName(username);
                enterpriseUser.setUserOpenid(rid);
                enterpriseUser.setCreateTime(DateUtil.date());
                enterpriseUser.setUpdateTime(DateUtil.date());
                enterpriseUser.setUserName(username);
                enterpriseUserService.saveEnterpriseUser(enterpriseUser);
            }
            //初始化数据库
            if (StringUtils.isEmpty(one.getEnterpriseDatabaseUrl())){
                tSysEnterpriseService.initDatabase(one.getEnterpriseOpenid());
            }
            //分配权限
            SysEnterpriseAuthority authority = new SysEnterpriseAuthority();
            authority.setEnterpriseOpenid(one.getEnterpriseOpenid());
            List<SysEnterpriseAuthority> sysEnterpriseAuthorities = sysEnterpriseAuthorityService.selectSysEnterpriseAuthorityList(authority);
            if (sysEnterpriseAuthorities == null || sysEnterpriseAuthorities.size() <= 0){
                Calendar calendar = Calendar.getInstance();
                // 将当前时间向后偏移15天
                calendar.add(Calendar.DAY_OF_MONTH, 15);
                // 将 Calendar 类型转换为 Date 类型
                Date date = calendar.getTime();

                //权限为空，进行授权
                List<SysEnterpriseAuthority> sysEnterpriseAuthority = new ArrayList<>();
                SysEnterpriseAuthority authority1 = new SysEnterpriseAuthority();
                authority1.setEnterpriseOpenid(one.getEnterpriseOpenid());
                authority1.setSystemOpenid("DCBB04D3554B42F0A376978D725F50B6"); //企业管理
                authority1.setSystemValidity(date);
                SysEnterpriseAuthority authority2 = new SysEnterpriseAuthority();
                authority2.setEnterpriseOpenid(one.getEnterpriseOpenid());
                authority2.setSystemOpenid("05149ef65a344abd93a35fdb41d6a63c"); //生产管理
                authority2.setSystemValidity(date);
                sysEnterpriseAuthority.add(authority1);
                sysEnterpriseAuthority.add(authority2);
                sysEnterpriseAuthorityService.saveBatch(sysEnterpriseAuthority);

            }
            EnterpriseRole role = new EnterpriseRole();
            role.setEnterpriseOpenid(one.getEnterpriseOpenid());
            List<EnterpriseRole> enterpriseRoles = enterpriseRoleService.selectEnterpriseRoleList(role);
            if (enterpriseRoles == null || enterpriseRoles.size() == 0){
                //创建管理员权限
                EnterpriseRole enterpriseRole = new EnterpriseRole();
                enterpriseRole.setEnterpriseOpenid(one.getEnterpriseOpenid());
                enterpriseRole.setDataScope("1");
                enterpriseRole.setRoleName("管理员");
                enterpriseRole.setRoleDesc("总管理员，最高权限");
                enterpriseRole.setRoleOpenid(IdUtil.simpleUUID());
                enterpriseRoleService.saveRoleAdmin(enterpriseRole);
                String roleOpenid = enterpriseRoleService.selectEnterpriseRoleList(enterpriseRole).get(0).getRoleOpenid();
                List<EnterpriseRoleFunction> enterpriseRoleFunctionList = new ArrayList<>();
                for (String f : function) {
                    EnterpriseRoleFunction roleFunction = new EnterpriseRoleFunction();
                    roleFunction.setRoleOpenid(roleOpenid);
                    roleFunction.setFunctionOpenid(f);
                    roleFunction.setCreateTime(new Date());
                    enterpriseRoleFunctionList.add(roleFunction);
                }
                roleFunctionService.saveBatch(enterpriseRoleFunctionList);
            }

            //创建管理员权限
            EnterpriseRole enterpriseRole = new EnterpriseRole();
            enterpriseRole.setEnterpriseOpenid(one.getEnterpriseOpenid());
            enterpriseRole.setDataScope("1");
            enterpriseRole.setRoleName("管理员");
            enterpriseRole.setRoleDesc("总管理员，最高权限");
            String roleOpenid = enterpriseRoleService.selectEnterpriseRoleList(null).get(0).getRoleOpenid();
            //给这个员工赋予管理员权限
            QueryWrapper<EnterpriseUser> queryUser = new QueryWrapper<>();
            queryUser.eq("enterprise_openid",one.getEnterpriseOpenid());
            queryUser.eq("user_name",username);
            queryUser.eq("user_phone",phone);
            EnterpriseUser enterpriseUser = enterpriseUserService.getOne(queryUser);
            EnterpriseDepartment department = new EnterpriseDepartment();
            department.setEnterpriseOpenid(one.getEnterpriseOpenid());
            enterpriseUser.setDepartmentOpenid(departmentService.selectEnterpriseDepartmentList(department).get(0).getDepartmentOpenid());
            List<String> roleList = new ArrayList<>();
            roleList.add(roleOpenid);
            enterpriseUser.setRoleList(roleList);
            enterpriseUserService.updateEnterpriseUserById(enterpriseUser);



            LoginBody loginBody = new LoginBody();
            loginBody.setCode("");
            loginBody.setPassword("123456");
            loginBody.setEnterpriseCode(one.getEnterpriseCode());
            loginBody.setUuid(rid);
            loginBody.setUsername(username);
            String iotToken = sysLoginService.loginFromLingYang(loginBody, request);
            LoginInfoAio loginInfo = new LoginInfoAio();
            loginInfo.setToken(iotToken);
            loginInfo.setEnterpriseCode(company);
            loginInfo.setUsername(username);
            return AjaxResult.success("验证成功，已经存在企业，正在登录...",loginInfo);
        }
        //创建企业后进行登录
        if (tSysEnterpriseService.save(tSysEnterprise)){
            QueryWrapper<TSysEnterprise> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("enterprise_name", company);
            TSysEnterprise one = tSysEnterpriseService.getOne(queryWrapper);

            QueryWrapper<EnterpriseUser> query = new QueryWrapper<>();
            query.eq("enterprise_openid",one.getEnterpriseOpenid());
            query.eq("user_name",username);
            EnterpriseUser employee = enterpriseUserService.getOne(query);
            if (ObjectUtil.isNull(employee)){
                //创建员工
                EnterpriseUser enterpriseUser = new EnterpriseUser();
                enterpriseUser.setEnterpriseOpenid(one.getEnterpriseOpenid());
                enterpriseUser.setUserPhone(phone);
                enterpriseUser.setUserStatus("1");
                enterpriseUser.setUserPassword("$2a$10$FVfE/Lv7gtWv1S9DP5qPzuPAXhD09Vr6bm6uxH2WVJAuVWBb2.rvG");
                enterpriseUser.setUserFullName(username);
                enterpriseUser.setUserOpenid(rid);
                enterpriseUser.setCreateTime(DateUtil.date());
                enterpriseUser.setUpdateTime(DateUtil.date());
                enterpriseUser.setUserName(username);
                enterpriseUserService.saveEnterpriseUser(enterpriseUser);
            }

            //初始化数据库
            if (StringUtils.isEmpty(one.getEnterpriseDatabaseUrl())){
                tSysEnterpriseService.initDatabase(one.getEnterpriseOpenid());
            }
            //分配权限
            SysEnterpriseAuthority authority = new SysEnterpriseAuthority();
            authority.setEnterpriseOpenid(one.getEnterpriseOpenid());
            List<SysEnterpriseAuthority> sysEnterpriseAuthorities = sysEnterpriseAuthorityService.selectSysEnterpriseAuthorityList(authority);
            if (sysEnterpriseAuthorities == null || sysEnterpriseAuthorities.size() <= 0){
                Calendar calendar = Calendar.getInstance();
                // 将当前时间向后偏移15天
                calendar.add(Calendar.DAY_OF_MONTH, 15);
                // 将 Calendar 类型转换为 Date 类型
                Date date = calendar.getTime();

                //权限为空，进行授权
                List<SysEnterpriseAuthority> sysEnterpriseAuthority = new ArrayList<>();
                SysEnterpriseAuthority authority1 = new SysEnterpriseAuthority();
                authority1.setEnterpriseOpenid(one.getEnterpriseOpenid());
                authority1.setSystemOpenid("DCBB04D3554B42F0A376978D725F50B6"); //企业管理
                authority1.setSystemValidity(date);
                SysEnterpriseAuthority authority2 = new SysEnterpriseAuthority();
                authority2.setEnterpriseOpenid(one.getEnterpriseOpenid());
                authority2.setSystemOpenid("05149ef65a344abd93a35fdb41d6a63c"); //生产管理
                authority2.setSystemValidity(date);
                sysEnterpriseAuthority.add(authority1);
                sysEnterpriseAuthority.add(authority2);
                sysEnterpriseAuthorityService.saveBatch(sysEnterpriseAuthority);

            }
            EnterpriseRole role = new EnterpriseRole();
            role.setEnterpriseOpenid(one.getEnterpriseOpenid());
            List<EnterpriseRole> enterpriseRoles = enterpriseRoleService.selectEnterpriseRoleList(role);
            if (enterpriseRoles == null || enterpriseRoles.size() == 0){
                //创建管理员权限
                EnterpriseRole enterpriseRole = new EnterpriseRole();
                enterpriseRole.setEnterpriseOpenid(one.getEnterpriseOpenid());
                enterpriseRole.setDataScope("1");
                enterpriseRole.setRoleName("管理员");
                enterpriseRole.setRoleDesc("总管理员，最高权限");
                enterpriseRole.setRoleOpenid(IdUtil.simpleUUID());
                enterpriseRoleService.saveRoleAdmin(enterpriseRole);
                String roleOpenid = enterpriseRoleService.selectEnterpriseRoleList(enterpriseRole).get(0).getRoleOpenid();
                List<EnterpriseRoleFunction> enterpriseRoleFunctionList = new ArrayList<>();
                for (String f : function) {
                    EnterpriseRoleFunction roleFunction = new EnterpriseRoleFunction();
                    roleFunction.setRoleOpenid(roleOpenid);
                    roleFunction.setFunctionOpenid(f);
                    roleFunction.setCreateTime(new Date());
                    roleFunction.setUpdateTime(new Date());
                    enterpriseRoleFunctionList.add(roleFunction);
                }
                roleFunctionService.saveBatch(enterpriseRoleFunctionList);
            }

            EnterpriseRole enterpriseRole = new EnterpriseRole();
            enterpriseRole.setEnterpriseOpenid(one.getEnterpriseOpenid());
            enterpriseRole.setDataScope("1");
            enterpriseRole.setRoleName("管理员");
            enterpriseRole.setRoleDesc("总管理员，最高权限");
            String roleOpenid = enterpriseRoleService.selectEnterpriseRoleList(enterpriseRole).get(0).getRoleOpenid();

            //给这个员工赋予管理员权限
            QueryWrapper<EnterpriseUser> queryUser = new QueryWrapper<>();
            queryUser.eq("enterprise_openid",one.getEnterpriseOpenid());
            queryUser.eq("user_name",username);
            queryUser.eq("user_phone",phone);
            EnterpriseUser enterpriseUser = enterpriseUserService.getOne(queryUser);
            EnterpriseDepartment department = new EnterpriseDepartment();
            department.setEnterpriseOpenid(one.getEnterpriseOpenid());
            enterpriseUser.setDepartmentOpenid(departmentService.selectEnterpriseDepartmentList(department).get(0).getDepartmentOpenid());
            List<String> roleList = new ArrayList<>();
            roleList.add(roleOpenid);
            enterpriseUser.setRoleList(roleList);
            enterpriseUserService.updateEnterpriseUserById(enterpriseUser);

            LoginBody loginBody = new LoginBody();
            loginBody.setCode("");
            loginBody.setPassword("123456");
            loginBody.setEnterpriseCode(one.getEnterpriseCode());
            loginBody.setUuid(rid);
            loginBody.setUsername(company);
            String iotToken = sysLoginService.loginFromLingYang(loginBody, request);
            LoginInfoAio loginInfo = new LoginInfoAio();
            loginInfo.setToken(iotToken);
            loginInfo.setEnterpriseCode(company);
            loginInfo.setUsername(phone);
            return AjaxResult.success("验证成功，成功创建企业，正在登录...",loginInfo);
        }
        return AjaxResult.error("验证失败",rid);
    }


}