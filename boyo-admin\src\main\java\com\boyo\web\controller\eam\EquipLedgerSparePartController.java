package com.boyo.web.controller.eam;

import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.eam.domain.EquipLedgerSparePart;
import com.boyo.eam.service.IEquipLedgerSparePartService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 台账和部件关联表(EquipLedgerSparePart)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-04 16:59:34
 */
@Api("台账和部件关联表")
@RestController
@RequestMapping("/equip/equipLedgerSparePart")
@AllArgsConstructor
public class EquipLedgerSparePartController extends BaseController{
    /**
     * 服务对象
     */
    private final IEquipLedgerSparePartService equipLedgerSparePartService;

    /**
     * 查询台账和部件关联表列表
     *
     */
    @ApiOperation("查询台账和部件关联表列表")
    @GetMapping("/list")
    public TableDataInfo list(EquipLedgerSparePart equipLedgerSparePart) {
        startPage();
        List<EquipLedgerSparePart> list = equipLedgerSparePartService.selectEquipLedgerSparePartList(equipLedgerSparePart);
        return getDataTable(list);
    }
    
    /**
     * 获取台账和部件关联表详情
     */
    @ApiOperation("获取台账和部件关联表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(equipLedgerSparePartService.getById(id));
    }

    /**
     * 新增台账和部件关联表
     */
    @ApiOperation("新增台账和部件关联表")
    @PostMapping
    public AjaxResult add(@RequestBody EquipLedgerSparePart equipLedgerSparePart) {
        return toBooleanAjax(equipLedgerSparePartService.save(equipLedgerSparePart));
    }

    /**
     * 修改台账和部件关联表
     */
    @ApiOperation("修改台账和部件关联表")
    @PutMapping
    public AjaxResult edit(@RequestBody EquipLedgerSparePart equipLedgerSparePart) {
        return toBooleanAjax(equipLedgerSparePartService.updateById(equipLedgerSparePart));
    }

    /**
     * 删除台账和部件关联表
     */
    @ApiOperation("删除台账和部件关联表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(equipLedgerSparePartService.removeByIds(Arrays.asList(ids)));
    }

}
