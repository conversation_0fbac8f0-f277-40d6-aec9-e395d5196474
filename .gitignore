# Build and Release Folders
bin-debug/
bin-release/
[Oo]bj/
[Bb]in/

# Other files and folders
.settings/

# Executables
*.swf
*.air
*.ipa
*.apk

# Project files, i.e. `.project`, `.actionScriptProperties` and `.flexProperties`
# should NOT be excluded as they contain compiler settings and other important
# information for Eclipse / Flash Builder.
target/
*.class

# IntelliJ IDEA
*.iml
*.iws
*.ipr
.idea/
.idea/*
!.idea/runConfigurations/

# Gradle
.gradle/
.gradle/*
build/
gradle-app.setting
!gradle-wrapper.jar
!gradle-wrapper.properties
**/build/
buildOutputCleanup.lock
cache.properties
checksums.lock
compiler.xml
encodings.xml
executionHistory.lock
fileHashes.lock
