package com.boyo.web.lysso.controller;

import com.alibaba.fastjson.JSONObject;

import javax.net.ssl.*;
import java.io.IOException;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> 2019/11/12
 * <p>
 * # GET 请求
 * HttpResponse res = HttpRequest.builder()
 * .setUrl("http://localhost:8080")
 * .setMethod(HttpRequest.Method.GET)
 * .addQuery("username", "lpw")
 * .addHeader("User-Agent", "Chrome")
 * .addHeader("Content-Type", "text/html")
 * .get();
 * <p>
 * # POST 请求 (提交 FORM 表单)
 * HttpResponse res = HttpRequest.builder()
 * .setUrl("http://localhost:8080")
 * .setMethod(HttpRequest.Method.POST)
 * .addHeader("User-Agent", "Chrome")
 * .addHeader("Content-Type", "application/x-www-form-urlencoded")
 * .addPostData("username", "lpw")
 * .addPostData("password", "123456")
 * .post();
 * <p>
 * # POST 请求 (提交 JSON 数据)
 * HttpResponse res = HttpRequest.builder()
 * .setUrl("http://localhost:8080")
 * .setMethod(HttpRequest.Method.POST)
 * .addHeader("User-Agent", "Chrome")
 * .addHeader("Content-Type", "application/json")
 * .addPostData("username", "lpw")
 * .addPostData("password", "123456")
 * .post();
 * <p>
 * # 获取请求结果
 * if (res.getResponseCode() == HttpStatus.SC_OK) {
 * log.info(res.getText());
 * }
 */

public class HttpRequest {

    /**
     * 常见请求方法
     */
    public static final class Method {
        private Method() {
            throw new UnsupportedOperationException("HttpRequest.Method");
        }

        public static final String GET = "GET";
        public static final String POST = "POST";
        public static final String PUT = "PUT";
        public static final String DELETE = "DELETE";
    }

    /**
     * 常见编码
     */
    public static final class Encode {
        private Encode() {
            throw new UnsupportedOperationException("HttpRequest.Encode");
        }

        public static final String UTF8 = "UTF-8";
        public static final String UTF16 = "UTF-16";
        public static final String GB18030 = "GB18030";
        public static final String GBK = "GBK";
        public static final String GB2312 = "GB2312";
        public static final String ISO_8859_1 = "ISO-8859-1";
        public static final String ASCII = "ASCII";
    }

    /**
     * URL
     */
    private String url;

    /**
     * 路径参数
     */
    private Map<String, Object> query = new HashMap<>();

    /**
     * method
     */
    private String method = Method.GET;

    /**
     * post 请求的 body 参数
     */
    private Map<String, Object> postData = new HashMap<>();

    /**
     * cookies
     */
    private String cookies = "";

    /**
     * headers
     */
    private Map<String, Object> headers = new HashMap<>();

    /**
     * 编码
     */
    private String encode = Encode.UTF8;

    /**
     * 连接对象
     */
    private HttpURLConnection conn;

    /**
     * 是否允许往服务器写入数据
     */
    private boolean doInput = true;

    /**
     * 是否允许往服务器读取数据
     */
    private boolean doOutput = false;

    /**
     * 读超时时间
     */
    private int readTimeout = -1;

    /**
     * 连接超时时间
     */
    private int connectTimeout = -1;

    /**
     * 是否使用缓存
     */
    private boolean useCaches = false;


    private static final HostnameVerifier DO_NOT_VERIFY = (hostname, session) -> true;

    private static final TrustManager[] TRUST_ALL_CERTS = new TrustManager[]{new X509TrustManager() {
        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[]{};
        }

        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType) {}

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType) {}
    }};

    private static void trustAllHosts(HttpsURLConnection connection) {
        try {
            SSLContext sc = SSLContext.getInstance("TLS");
            sc.init(null, TRUST_ALL_CERTS, new java.security.SecureRandom());
            SSLSocketFactory newFactory = sc.getSocketFactory();
            connection.setSSLSocketFactory(newFactory);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public HttpRequest() {
    }


    public HttpRequest(String url) {
        this.url = url;
    }


    public static HttpRequest builder() {
        return new HttpRequest();
    }

    public HttpRequest build() {
        return this;
    }


    private HttpURLConnection getConnection() {
        this.openConnection();
        return this.conn;
    }


    private void openConnection() {
        if (url == null || "".equals(url)) {
            throw new RuntimeException("URL 不能为空");
        }

        try {
            URL url = new URL(this.url + toQueryString(query));
            this.conn = (HttpURLConnection) url.openConnection();

            // 跳过 HTTPS 验证
            boolean useHttps = this.url.startsWith("https");
            if (useHttps) {
                HttpsURLConnection https = (HttpsURLConnection) this.conn;
                trustAllHosts(https);
                https.setHostnameVerifier(DO_NOT_VERIFY);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    public HttpResponse request() {
        HttpResponse httpResponse = null;
        openConnection();
        try {
            setRequestProperties(conn);
            httpResponse = new HttpResponse(conn, encode);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }
        return httpResponse;
    }

    public HttpResponse get() {
        this.method = Method.GET;
        return request();
    }


    public HttpResponse post() {
        this.method = Method.POST;
        return request();
    }


    public HttpResponse put() {
        this.method = Method.PUT;
        return request();
    }


    public HttpResponse delete() {
        this.method = Method.DELETE;
        return request();
    }


    private void setRequestProperties(HttpURLConnection conn) throws IOException {
        conn.setDoInput(doInput);
        conn.setDoOutput(doOutput);
        conn.setUseCaches(useCaches);
        if (readTimeout > 0) {
            conn.setReadTimeout(readTimeout);
        }

        if (connectTimeout > 0) {
            conn.setConnectTimeout(connectTimeout);
        }

        conn.setRequestMethod(method);
        if (!headers.isEmpty()) {
            for (Map.Entry<String, Object> header : headers.entrySet()) {
                conn.setRequestProperty(header.getKey(), (String) header.getValue());
            }
        }

        if (cookies != null && !"".equals(cookies.trim())) {
            conn.setRequestProperty("Cookie", cookies);
        }

        if (Method.POST.equals(method.toUpperCase()) || Method.PUT.equals(method.toUpperCase())) {
            conn.setUseCaches(false);
            conn.setDoOutput(true);

            OutputStream os = conn.getOutputStream();
            os.write(toPostDataString().getBytes());
            os.flush();
        }
    }

    private String toPostDataString() {
        if (postData.size() < 1) {
            return "";
        }
        String postDataString;
        String contentType = (String) headers.get("Content-Type");
        if (contentType != null && contentType.contains("form")) {
            postDataString = toQueryString(postData);
            postDataString = postDataString.substring(1);
        } else {
            postDataString = JSONObject.toJSONString(postData);
        }
        return postDataString;
    }


    public String toQueryString(Map<String, Object> query) {
        if (query == null) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        if (!query.isEmpty()) {
            if (url.contains("?")) {
                sb.append("&");
            } else {
                sb.append("?");
            }
            for (Map.Entry entry : query.entrySet()) {
                sb.append(entry.getKey().toString()).append("=").append(entry.getValue().toString()).append("&");
            }
            sb.delete(sb.length() - 1, sb.length());
        }
        return sb.toString();
    }

    /**
     * 添加一条路径参数
     */
    public HttpRequest addPathVariable(Object var) {
        this.url += "/" + var;
        return this;
    }


    /**
     * 添加一条路径参数
     *
     * @param key   键
     * @param value 值
     */
    public HttpRequest addQuery(String key, String value) {
        if (key == null || value == null) {
            throw new IllegalArgumentException("key 或 value 不可为空");
        }
        query.put(key, value);
        return this;
    }


    public HttpRequest addPostData(String key, Object value) {
        postData.put(key, value);
        return this;

    }

    public HttpRequest addPostData(Map<String, Object> data) {
        if (data == null) {
            throw new IllegalArgumentException("data 数据不可为空");
        }
        postData.putAll(data);
        return this;
    }

    public HttpRequest addCookie(String key, String value) {
        if (key == null || value == null) {
            throw new IllegalArgumentException("key 或 value 不可为空");
        }
        cookies = cookies.trim() + ("".equals(cookies.trim()) ? "" : ";") + key + "=" + value;
        return this;
    }


    public HttpRequest addHeader(String key, String value) {
        if (key == null || "".equals(key) || value == null) {
            throw new IllegalArgumentException("key 或 value 不可为空");
        }
        headers.put(key, value);
        return this;
    }


    public String getUrl() {
        return url;
    }


    public HttpRequest setUrl(String url) {
        if (url == null || "".equals(url)) {
            throw new IllegalArgumentException("URL 不能为空或空串");
        }

        this.url = url;
        return this;
    }


    public Map<String, Object> getQuery() {
        return query;
    }

    public HttpRequest setQuery(Map<String, Object> query) {
        if (query == null) {
            throw new IllegalArgumentException("param 不能为空");
        }
        this.query = query;
        return this;
    }


    public String getMethod() {
        return method;
    }


    public HttpRequest setMethod(String method) {
        if (method == null || "".equals(method)) {
            throw new IllegalArgumentException("method 不能为空");
        }

        this.method = method;
        return this;
    }


    public Map getPostData() {
        return postData;
    }


    public HttpRequest setPostData(Map<String, Object> postData) {
        if (postData == null) {
            throw new IllegalArgumentException("postData 不能为空");
        }
        this.postData = postData;
        return this;
    }


    public String getCookies() {
        return cookies;
    }

    public HttpRequest setCookies(String cookies) {
        if (cookies == null || "".equals(cookies)) {
            throw new IllegalArgumentException("cookie 不能为空");
        }

        this.cookies = cookies;
        return this;
    }


    public Map<String, Object> getHeaders() {
        return headers;
    }


    public HttpRequest setHeaders(Map<String, Object> headers) {
        if (headers == null) {
            throw new IllegalArgumentException("headers 不能为空");
        }
        this.headers = headers;
        return this;
    }


    public String getEncode() {
        return encode;
    }


    public HttpRequest setEncode(String encode) {
        if (encode == null || "".equals(encode)) {
            throw new IllegalArgumentException("encode 不能为空");
        }
        this.encode = encode;
        return this;
    }


    public boolean isDoInput() {
        return doInput;
    }


    public HttpRequest setDoInput(boolean doInput) {
        this.doInput = doInput;
        return this;
    }

    public boolean isDoOutput() {
        return doOutput;
    }


    public HttpRequest setDoOutput(boolean doOutput) {
        this.doOutput = doOutput;
        return this;
    }


    public int getReadTimeout() {
        return readTimeout;
    }


    public HttpRequest setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
        return this;
    }


    public int getConnectTimeout() {
        return connectTimeout;
    }


    public HttpRequest setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
        return this;
    }


    public boolean isUseCaches() {
        return useCaches;
    }


    public HttpRequest setUseCaches(boolean useCaches) {
        this.useCaches = useCaches;
        return this;
    }
}