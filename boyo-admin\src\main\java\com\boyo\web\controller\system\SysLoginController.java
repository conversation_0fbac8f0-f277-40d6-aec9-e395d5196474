package com.boyo.web.controller.system;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.common.exception.CustomException;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.master.entity.EnterpriseCustom;
import com.boyo.master.service.IEnterpriseCustomService;
import com.boyo.system.domain.BoyoMenu;
import com.boyo.system.mapper.BoyoMenuMapper;
import com.boyo.system.service.IBoyoMenuService;
import lombok.AllArgsConstructor;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.boyo.common.constant.Constants;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.domain.entity.SysMenu;
import com.boyo.common.core.domain.entity.SysUser;
import com.boyo.common.core.domain.model.LoginBody;
import com.boyo.common.core.domain.model.LoginUser;
import com.boyo.common.utils.ServletUtils;
import com.boyo.framework.web.service.SysLoginService;
import com.boyo.framework.web.service.SysPermissionService;
import com.boyo.framework.web.service.TokenService;
import com.boyo.system.service.ISysMenuService;

import javax.servlet.http.HttpServletRequest;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
public class SysLoginController {
    private SysLoginService loginService;

    private ISysMenuService menuService;

    private SysPermissionService permissionService;

    private TokenService tokenService;
    private final WxMpService wxService;
    private final BoyoMenuMapper boyoMenuMapper;


    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody, HttpServletRequest request) {
        AjaxResult ajax = AjaxResult.success();
        if (StrUtil.isNotEmpty(loginBody.getWechat())) {
            try {
                WxMpUser user = wxService.switchoverTo("wx8485cacdf80ab61e").getUserService().userInfo(loginBody.getWechat());
                if (ObjectUtil.isNull(user)) {
                    throw new CustomException("微信用户信息错误");
                }
            } catch (WxErrorException e) {
                e.printStackTrace();
            }

        }
        // 生成令牌
        String token = loginService.login(loginBody, request);
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser user = loginUser.getUser();
        AjaxResult ajax = AjaxResult.success();
        Set<String> roles = new HashSet<String>();
        Set<String> permissions = new HashSet<String>();
        if (ObjectUtil.isNotNull(user)) {
            // 角色集合
            roles = permissionService.getRolePermission(user);
            // 权限集合
            permissions = permissionService.getMenuPermission(user);
        } else {
            roles.add("admin");
//            管理员有所有权限
            if(loginUser.getEnterpriseUser().getUserAdmin().equals(1L)){
                permissions.add("*:*:*");
            }else{
                List<BoyoMenu> menus = boyoMenuMapper.selectMenuByUserId(SecurityUtils.getUserOpenid());
                for (BoyoMenu menu : menus){
                    if(StrUtil.isNotEmpty(menu.getPerms())){
                        permissions.add(menu.getPerms());
                    }
                }
            }
//            获取用户的权限
        }
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        ajax.put("user", user);
        ajax.put("enterpriseUser", loginUser.getEnterpriseUser());
        return ajax;

    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        // 用户信息
        SysUser user = loginUser.getUser();
        if (ObjectUtil.isNotNull(user)) {
            List<SysMenu> menus = menuService.selectMenuTreeByUserId(user.getUserId());
            return AjaxResult.success(menuService.buildMenus(menus));
        } else {
            return null;
        }

    }
}
