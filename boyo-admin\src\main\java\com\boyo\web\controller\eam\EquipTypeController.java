package com.boyo.web.controller.eam;

import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.eam.domain.EquipType;
import com.boyo.eam.service.IEquipTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 设备类型表(EquipType)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
@Api("设备类型表")
@RestController
@RequestMapping("/equip/equipType")
@AllArgsConstructor
public class EquipTypeController extends BaseController{
    /**
     * 服务对象
     */
    private final IEquipTypeService equipTypeService;

    /**
     * 查询设备类型表列表
     *
     */
    @ApiOperation("查询设备类型表列表")
    @GetMapping("/list")
    public TableDataInfo list(EquipType equipType) {
        startPage();
        List<EquipType> list = equipTypeService.selectEquipTypeList(equipType);
        return getDataTable(list);
    }
    
    /**
     * 获取设备类型表详情
     */
    @ApiOperation("获取设备类型表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(equipTypeService.getById(id));
    }

    /**
     * 新增设备类型表
     */
    @ApiOperation("新增设备类型表")
    @PostMapping
    public AjaxResult add(@RequestBody EquipType equipType) {
        equipType.setOpenid(super.generateOpenid());
        return toBooleanAjax(equipTypeService.save(equipType));
    }

    /**
     * 修改设备类型表
     */
    @ApiOperation("修改设备类型表")
    @PutMapping
    public AjaxResult edit(@RequestBody EquipType equipType) {
        return toBooleanAjax(equipTypeService.updateById(equipType));
    }

    /**
     * 删除设备类型表
     */
    @ApiOperation("删除设备类型表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(equipTypeService.removeByIds(Arrays.asList(ids)));
    }

}
