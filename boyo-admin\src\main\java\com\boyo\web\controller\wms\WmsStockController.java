package com.boyo.web.controller.wms;

import java.util.List;

import com.boyo.wms.dto.WmsAllotDTO;
import com.boyo.wms.vo.WmsStockVO;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.boyo.wms.entity.WmsStock;
import com.boyo.wms.service.IWmsStockService;

/**
 * 库存管理Controller
 *
 * <AUTHOR>
 */
@Api("库存管理")
@RestController
@RequestMapping("/wms/stock")
@AllArgsConstructor
public class WmsStockController extends BaseController {
    private final IWmsStockService wmsStockService;

    /**
     * 查询库存管理列表
     */
    @ApiOperation("查询库存管理列表")
    @GetMapping("/list")
    public TableDataInfo list(WmsStockVO wmsStock) {
//        startPage();
        List<WmsStockVO> list = wmsStockService.selectWmsStockList(wmsStock);
        return getDataTable(list);
    }

    @ApiOperation("查询库存管理列表")
    @GetMapping("/selectWmsStockByMateriel")
    public TableDataInfo selectWmsStockByMateriel(WmsStock wmsStock) {
//        startPage();
        List<WmsStockVO> list = wmsStockService.selectWmsStockByMateriel(wmsStock);
        return getDataTable(list);
    }


    /**
     * 获取库存管理详细信息
     */
    @ApiOperation("获取库存管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(wmsStockService.getById(id));
    }


    @ApiOperation("获取库存报警信息")
    @GetMapping("selectStockWarn")
    public AjaxResult selectStockWarn(){
        return AjaxResult.success(wmsStockService.selectStockWarn());
    }


    @ApiOperation("库存调拨")
    @PostMapping("allotMaterial")
    public AjaxResult allotMaterial(@RequestBody WmsAllotDTO allotDTO) {
        return AjaxResult.success(wmsStockService.allotMaterial(allotDTO));
    }

}
