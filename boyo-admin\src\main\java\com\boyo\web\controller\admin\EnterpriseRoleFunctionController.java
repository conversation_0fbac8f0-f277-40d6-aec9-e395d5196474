package com.boyo.web.controller.admin;

import java.util.List;
import java.util.Arrays;

import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.boyo.system.domain.EnterpriseRoleFunction;
import com.boyo.system.service.IEnterpriseRoleFunctionService;

/**
 * 企业角色权限管理Controller
 *
 * <AUTHOR>
 */
@Api("企业角色权限管理")
@RestController
@RequestMapping("/system/rolefunction")
@AllArgsConstructor
public class EnterpriseRoleFunctionController extends BaseController {
    private final IEnterpriseRoleFunctionService enterpriseRoleFunctionService;

    /**
     * 查询企业角色权限管理列表
     */
    @ApiOperation("查询企业角色权限管理列表")
    @GetMapping("/list")
    public TableDataInfo list(EnterpriseRoleFunction enterpriseRoleFunction) {
        startPage();
        List<EnterpriseRoleFunction> list = enterpriseRoleFunctionService.selectEnterpriseRoleFunctionList(enterpriseRoleFunction);
        return getDataTable(list);
    }

    /**
     * 获取企业角色权限管理详细信息
     */
    @ApiOperation("获取企业角色权限管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(enterpriseRoleFunctionService.getById(id));
    }

    /**
     * 新增企业角色权限管理
     */
    @ApiOperation("新增企业角色权限管理")
    @PostMapping
    public AjaxResult add(@RequestBody EnterpriseRoleFunction enterpriseRoleFunction) {
        return toBooleanAjax(enterpriseRoleFunctionService.save(enterpriseRoleFunction));
    }

    /**
     * 修改企业角色权限管理
     */
    @ApiOperation("修改企业角色权限管理")
    @PutMapping
    public AjaxResult edit(@RequestBody EnterpriseRoleFunction enterpriseRoleFunction) {
        return toBooleanAjax(enterpriseRoleFunctionService.updateById(enterpriseRoleFunction));
    }

    /**
     * 删除企业角色权限管理
     */
    @ApiOperation("删除企业角色权限管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(enterpriseRoleFunctionService.removeByIds(Arrays.asList(ids)));
    }
}
