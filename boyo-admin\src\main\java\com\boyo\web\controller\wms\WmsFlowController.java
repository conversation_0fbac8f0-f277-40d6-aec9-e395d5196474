package com.boyo.web.controller.wms;

import java.util.Date;
import java.util.List;
import java.util.Arrays;

import com.boyo.wms.dto.WmsCheckOutDTO;
import com.boyo.wms.dto.WmsFlowDTO;
import com.boyo.wms.dto.WmsWarehousingDTO;
import com.boyo.wms.vo.WmsFlowVO;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.boyo.wms.entity.WmsFlow;
import com.boyo.wms.service.IWmsFlowService;

/**
 * 出入库流水管理Controller
 *
 * <AUTHOR>
 */
@Api("出入库流水管理")
@RestController
@RequestMapping("/wms/flow")
@AllArgsConstructor
public class WmsFlowController extends BaseController {
    private final IWmsFlowService wmsFlowService;

    /**
     * 查询出入库流水管理列表
     */
    @ApiOperation("查询出入库流水管理列表")
    @GetMapping("/list")
    public TableDataInfo list(WmsFlow wmsFlow) {
        startPage();
        List<WmsFlowVO> list = wmsFlowService.selectWmsFlowList(wmsFlow);
        return getDataTable(list);
    }

    /**
     * 新增出入库流水管理
     */
    @ApiOperation("新增出入库流水管理")
    @PostMapping
    public AjaxResult add(@RequestBody WmsFlowDTO wmsFlow) {
        wmsFlow.setCreatedAt(new Date());
        return toBooleanAjax(wmsFlowService.saveFlow(wmsFlow));
    }

    /**
     * 修改出入库流水管理
     */
    @ApiOperation("修改出入库流水管理")
    @PutMapping
    public AjaxResult edit(@RequestBody WmsFlow wmsFlow) {
        return toBooleanAjax(wmsFlowService.updateById(wmsFlow));
    }

    /**
     * 删除出入库流水管理
     */
    @ApiOperation("删除出入库流水管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(wmsFlowService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 快速入库
     * @return
     */
    @PostMapping("/quickWarehousing")
    public AjaxResult quickWarehousing(@RequestBody WmsWarehousingDTO warehousingDTO){
        return toBooleanAjax(wmsFlowService.quickWarehousing(warehousingDTO));
    }

    /**
     * 快速出库
     * @return
     */
    @PostMapping("/quickCheckOut")
    public AjaxResult quickCheckOut(@RequestBody WmsCheckOutDTO checkOutDTO){
        return toBooleanAjax(wmsFlowService.quickCheckOut(checkOutDTO));
    }

    @GetMapping("/flowDaySummary")
    public AjaxResult flowDaySummary(){
        return AjaxResult.success(wmsFlowService.listDateFlow());
    }
}
