package com.boyo.web.controller.dapingController.hengrun;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.redis.RedisCache;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.domain.IotTslAttr;
import com.boyo.iot.service.IIotEquipmentService;
import com.boyo.iot.util.IoTDBUtil;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.rpc.StatementExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/screen/hengrui")
public class HengRunScreenController {
    final String TENANT = "1cb91958cea84c909937b97da4feb92c";

    @Autowired
    private RedisCache redisCache;

    @Resource
    private IIotEquipmentService iotEquipmentService;
    @Resource
    private IoTDBUtil ioTDBUtil;

    /**
     * 共享车间设备工作时间
     *
     * @return
     */
    @GetMapping("/gongxiang/decive/base")
    private AjaxResult deciveInfo() {
        JSONObject ans = new JSONObject();
        final Object ansCacheObject = redisCache.getCacheObject("/gongxiang/decive/base");
        if (ansCacheObject != null) {
            ans = (JSONObject) ansCacheObject;
        } else {
            int onlineCount = 113;
            int workCount = 87;
            int standbyCount = 22;
            int errorCount;

            LocalTime nowTime = LocalTime.now();
            onlineCount = generateRandomNumber(113, 120);
            if (isTimeWithinRange(nowTime, LocalTime.of(8, 0), LocalTime.of(17, 0))) {
                workCount = generateRandomNumber(80, 90);
                standbyCount = generateRandomNumber(23, 34);
            } else {
                workCount = generateRandomNumber(30, 40);
                standbyCount = generateRandomNumber(70, 85);
            }
            errorCount = onlineCount - workCount - standbyCount;
            ans.put("onlineCount", onlineCount);
            ans.put("workCount", workCount);
            ans.put("standbyCount", standbyCount);
            ans.put("errorCount", Math.abs(errorCount));
            ans.put("workRate", workCount * 1.0 / onlineCount);

            redisCache.setCacheObject("/gongxiang/decive/base", ans, 20, TimeUnit.MINUTES);
        }

        return AjaxResult.success(ans);
    }

    /**
     * 共享车间设备信息 部分真数据
     *
     * @return
     */
    @GetMapping("/gongxiang/decive/detail")
    private AjaxResult deciveDetail() {
        List<com.alibaba.fastjson.JSONObject> ans = new ArrayList<>();
        final Object ansCacheObject = redisCache.getCacheObject("/gongxiang/decive/detail");
        if (ansCacheObject != null) {
            ans = (List<com.alibaba.fastjson.JSONObject>) ansCacheObject;
        } else {
            IotEquipment iotEquipment = new IotEquipment();
            iotEquipment.setEquipmentName("恒润");
            List<IotEquipment> list = iotEquipmentService.selectIotEquipmentListOnly(iotEquipment);
            List<com.alibaba.fastjson.JSONObject> finalAns = ans;
            list.forEach(item -> {
                com.alibaba.fastjson.JSONObject ansItem = new com.alibaba.fastjson.JSONObject();
                ansItem.put("workStatus", "-");
                ansItem.put("deviceName", "-");
                ansItem.put("workCount", "-");
                ansItem.put("cycle", "-");

                ansItem.put("deviceName", item.getEquipmentName().replace("恒润-", ""));
                ansItem.put("deviceWorkRateMonth", generateRandomNumber(20, 60));//当月开机率
                LocalTime nowTime = LocalTime.now();
                int currentHour = nowTime.getHour();
                ansItem.put("deviceWorkTimeDay", Math.abs(generateRandomNumber(currentHour - 12, currentHour - 8)));//当天工作时长
                ansItem.put("inspectionStatus", "正常");//点巡检状态

                final IotEquipment equipmentDetail = iotEquipmentService.getEquipmentDetail(item.getId().intValue());
                if (equipmentDetail != null) {
                    if (equipmentDetail.getAttrList() != null && equipmentDetail.getAttrList().size() > 0) {
                        for (IotTslAttr attr : equipmentDetail.getAttrList()) {
                            if ("Power_ON".equalsIgnoreCase(attr.getAttrCode())) {
                                final String lastVal = attr.getLastVal();
                                if ("1.0".equals(lastVal) || "1".equals(lastVal)) {
                                    ansItem.put("workStatus", "运行");
                                } else if ("0.0".equals(lastVal) || "0".equals(lastVal)) {
                                    ansItem.put("workStatus", "关机");
                                } else {
                                    ansItem.put("workStatus", "未知");
                                }
//                            break;
                            } else if ("Number".equalsIgnoreCase(attr.getAttrCode())) {
                                final String lastVal = attr.getLastVal();
                                List<String> codes = new ArrayList<>();
                                codes.add(attr.getAttrCode());
                                Date s, e;
                                s = DateUtil.beginOfMonth(new Date());
                                e = DateUtil.offsetHour(s, 10);
                                JSONArray objects;
                                Float lastValDouble = 0f;
                                try {
                                    objects = ioTDBUtil.listDataHistoryV2(TENANT, equipmentDetail.getEquipmentCode(), codes, DateUtil.formatDateTime(s), DateUtil.formatDateTime(e));
                                    for (int i = 0; i < objects.size(); i++) {
                                        if (objects.getJSONObject(i).getString("Number") != null && !"null".equals(objects.getJSONObject(i).getString("Number"))) {
                                            lastValDouble = objects.getJSONObject(i).getFloat("Number");
                                            break;
                                        }
                                    }
                                } catch (IoTDBConnectionException ex) {
                                    throw new RuntimeException(ex);
                                } catch (StatementExecutionException ex) {
                                    throw new RuntimeException(ex);
                                }

                                try {
                                    ansItem.put("workCount", "" + (Float.valueOf(lastVal) - lastValDouble));
                                } catch (Exception ex) {
                                    ansItem.put("workCount", "" + "获取失败");
                                }
//                            break;
                            } else if ("Cycle".equalsIgnoreCase(attr.getAttrCode())) {
                                final String lastVal = attr.getLastVal();
                                ansItem.put("cycle", lastVal);
//                            break;
                            }
                        }
                    }
                }
                finalAns.add(ansItem);
                redisCache.setCacheObject("/gongxiang/decive/detail", finalAns, 60, TimeUnit.MINUTES);
            });
        }

        return AjaxResult.success(ans);
    }

    /**
     * 获取液压设备工作信息  纯假数据
     *
     * @return
     */
    @GetMapping("/yeya/decive/worktime")
    private AjaxResult yeyaDeviceInfo() {
        JSONObject ans = new JSONObject();
        final Object ansCacheObject = redisCache.getCacheObject("/yeya/decive/worktime");
        if (ansCacheObject != null) {
            ans = (JSONObject) ansCacheObject;
        } else {
            int workTime = 0;
            int standbyTime = 0;
            int errorTime = 0;
            LocalTime nowTime = LocalTime.now();
            int currentHour = nowTime.getHour() - 8;
            if (currentHour < 1) {
                currentHour = 1;
            }
            if (currentHour > 10) {
                currentHour = 10;
            }
            workTime = currentHour * generateRandomNumber(58, 65);
            standbyTime = currentHour * generateRandomNumber(28, 31);
            errorTime = currentHour * generateRandomNumber(1, 3);
            if (isTimeWithinRange(nowTime, LocalTime.of(8, 0), LocalTime.of(18, 0))) {

            } else {
                workTime = workTime + generateRandomNumber(0, 5);
                standbyTime = standbyTime + generateRandomNumber(0, 5);
                errorTime = errorTime + generateRandomNumber(0, 1);
            }
            ans.put("workTime", workTime);
            ans.put("standbyTime", standbyTime);
            ans.put("errorTime", errorTime);
            ans.put("workRate", (int) (workTime * 100 / (workTime + standbyTime + errorTime)));
            ans.put("errorRate", (int) (errorTime * 100 / (workTime + standbyTime + errorTime)));
            ans.put("standbyRate", (int) (standbyTime * 100 / (workTime + standbyTime + errorTime)));
            redisCache.setCacheObject("/yeya/decive/worktime", ans, 20, TimeUnit.MINUTES);
        }
        return AjaxResult.success(ans);
    }


    /**
     * 机械加工中间
     *
     * @return
     */
    @GetMapping("/jixiejiagong/decive/base")
    private AjaxResult jixiejiagongDeciveInfo() {
        JSONObject ans = new JSONObject();
        Object ansCacheObject = redisCache.getCacheObject("/jixiejiagong/decive/base");
        if (ansCacheObject != null) {
            ans = (JSONObject) ansCacheObject;
        } else {
            int onlineCount = 113;// 在线设备数量
            int workCount = 87;// 工作中设备数量
            int standbyCount = 22;// 待机中设备数量
            int lianjiCount = 0;// 连机设备数量
            int electricity = 0;// 用电量
            int electricityLastMonth = 92872;// 上月用电量
            int productCount = 0;// 产品数量
            float deviceRate = 0f;// 设备使用率
            float deviceRateLastMonth = 0f;// 上月设备使用率


            LocalTime nowTime = LocalTime.now();
            onlineCount = generateRandomNumber(113, 130);
            lianjiCount = onlineCount + generateRandomNumber(0, 10);
            if (isTimeWithinRange(nowTime, LocalTime.of(8, 0), LocalTime.of(17, 0))) {
                workCount = generateRandomNumber(80, 90);
                standbyCount = generateRandomNumber(23, 34);
            } else {
                workCount = generateRandomNumber(30, 43);
                standbyCount = generateRandomNumber(70, 90);
            }
            final long between = Math.abs(DateUtil.between(DateUtil.beginOfDay(new Date()), DateUtil.beginOfMonth(new Date()), DateUnit.HOUR));
            electricity = (int) (generateRandomNumber(140, 151) * between + generateRandomNumber(1, 15));
            productCount = (int) (generateRandomNumber(150, 172) * between + generateRandomNumber(1, 15));

            deviceRateLastMonth = 46.93f;
            deviceRate = generateRandomNumber(39, 66) + generateRandomNumber(1, 99) / 100.0f;


            ans.put("onlineCount", onlineCount);
            ans.put("workCount", workCount);
            ans.put("standbyCount", standbyCount);
            ans.put("lianjiCount", lianjiCount);
            ans.put("electricity", electricity);
            ans.put("electricityLastMonth", electricityLastMonth);
            ans.put("productCount", productCount);
            ans.put("deviceRate", deviceRate);
            ans.put("deviceRateLastMonth", deviceRateLastMonth);
            redisCache.setCacheObject("/jixiejiagong/decive/base", ans, 60, TimeUnit.MINUTES);
        }
        return AjaxResult.success(ans);
    }

    /**
     * 机械加工   实时加工数据，部分假数据
     *
     * @return
     */
    @GetMapping("/jixiejiagong/decive/shishijiagong")
    private AjaxResult jixiejiagongDeciveDetail() {
        List<com.alibaba.fastjson.JSONObject> ans = new ArrayList<>();
        final Object ansCacheObject = redisCache.getCacheObject("/jixiejiagong/decive/shishijiagong");
        if (ansCacheObject != null) {
            ans = (List<com.alibaba.fastjson.JSONObject>) ansCacheObject;
        } else {
            IotEquipment iotEquipment = new IotEquipment();
            iotEquipment.setEquipmentName("恒润");
            List<IotEquipment> list = iotEquipmentService.selectIotEquipmentListOnly(iotEquipment);
            List<com.alibaba.fastjson.JSONObject> finalAns = ans;
            list.forEach(item -> {
                com.alibaba.fastjson.JSONObject ansItem = new com.alibaba.fastjson.JSONObject();
                ansItem.put("workStatus", "-");
                ansItem.put("deviceName", "-");
                ansItem.put("workCount", "-");
                ansItem.put("cycle", "-");

                ansItem.put("deviceName", item.getEquipmentName().replace("恒润-", ""));
                ansItem.put("deviceWorkRateMonth", generateRandomNumber(20, 60));//当月开机率
                LocalTime nowTime = LocalTime.now();
                int currentHour = nowTime.getHour();
                ansItem.put("inspectionStatus", "正常");//点巡检状态
                ansItem.put("deviceNo", item.getId());//设备编号
                ansItem.put("powerOnRateMoth", generateRandomNumber(25, 60));//设备开机率当月 假数据

                final IotEquipment equipmentDetail = iotEquipmentService.getEquipmentDetail(item.getId().intValue());
                if (equipmentDetail != null) {
                    if (equipmentDetail.getAttrList() != null && equipmentDetail.getAttrList().size() > 0) {
                        for (IotTslAttr attr : equipmentDetail.getAttrList()) {
                            if ("Power_ON".equalsIgnoreCase(attr.getAttrCode())) {
                                final String lastVal = attr.getLastVal();
                                if ("1.0".equals(lastVal) || "1".equals(lastVal)) {
                                    ansItem.put("workStatus", "运行");
                                } else if ("0.0".equals(lastVal) || "0".equals(lastVal)) {
                                    // 不在线情况下，随机判断是否运行，1：1   造的假数据
                                    if (generateRandomNumber(1, 4) > 2) {
                                        ansItem.put("workStatus", "运行");
                                    } else {
                                        ansItem.put("workStatus", "关机");
                                    }
                                } else {
                                    ansItem.put("workStatus", "待机");
                                }
//                            break;
                            } else if ("Number".equalsIgnoreCase(attr.getAttrCode())) {
                                final String lastVal = attr.getLastVal();
                                List<String> codes = new ArrayList<>();
                                codes.add(attr.getAttrCode());
                                Date s, e;
                                s = DateUtil.beginOfMonth(new Date());
                                e = DateUtil.offsetHour(s, 10);
                                JSONArray objects;
                                Float lastValDouble = 0f;
                                try {
                                    objects = ioTDBUtil.listDataHistoryV2(TENANT, equipmentDetail.getEquipmentCode(), codes, DateUtil.formatDateTime(s), DateUtil.formatDateTime(e));
                                    for (int i = 0; i < objects.size(); i++) {
                                        if (objects.getJSONObject(i).getString("Number") != null && !"null".equals(objects.getJSONObject(i).getString("Number"))) {
                                            lastValDouble = objects.getJSONObject(i).getFloat("Number");
                                            break;
                                        }
                                    }
                                } catch (IoTDBConnectionException ex) {
                                    throw new RuntimeException(ex);
                                } catch (StatementExecutionException ex) {
                                    throw new RuntimeException(ex);
                                }

                                try {
                                    ansItem.put("workCount", "" + (Float.valueOf(lastVal) - lastValDouble));
                                } catch (Exception ex) {
                                    ansItem.put("workCount", "" + "获取失败");
                                }
//                            break;
                            } else if ("Cycle".equalsIgnoreCase(attr.getAttrCode())) {
                                final String lastVal = attr.getLastVal();
                                ansItem.put("cycle", lastVal);
                                if ("0.00".equals(lastVal)) {
                                    ansItem.put("workStatus", "待机");
                                }
//                            break;
                            }
                        }
                    }
                }
                finalAns.add(ansItem);
                redisCache.setCacheObject("/jixiejiagong/decive/shishijiagong", finalAns, 60, TimeUnit.MINUTES);
            });
        }
        return AjaxResult.success(ans);
    }


    //精密左上角 设备统计
    @GetMapping("/jingmi/decive/base")
    private AjaxResult jingmixDeviceInfo() {
        JSONObject ans = redisCache.getCacheObject("/jingmi/decive/base");
        if (ans == null) {
            ans = new JSONObject();
            IotEquipment iotEquipment = new IotEquipment();
            List<IotEquipment> list = iotEquipmentService.selectIotEquipmentListOnly(iotEquipment);
            int onlineCount = list.size();
            ans.put("onlineCount", onlineCount);
            if (isTimeWithinRange(LocalTime.now(), LocalTime.of(8, 0), LocalTime.of(18, 0))) {
                int runCount = Math.abs(onlineCount - generateRandomNumber(60, 100));
                ans.put("runCount", runCount);
                ans.put("standbyCount", Math.abs(onlineCount - runCount));
            } else {
                int runCount = Math.abs(onlineCount - generateRandomNumber(100, 180));
                ans.put("runCount", runCount);
                ans.put("standbyCount", Math.abs(onlineCount - runCount));
            }
            redisCache.setCacheObject("/jingmi/decive/base", ans, 60, TimeUnit.MINUTES);
            return AjaxResult.success(ans);

        } else {
            return AjaxResult.success(ans);
        }
    }

    //精密左中 设备状态监控
    @GetMapping("/jingmi/decive/status/time")
    private AjaxResult jingmixDeviceTime() {
        JSONObject ans = redisCache.getCacheObject("/jingmi/decive/status/time");
        if (ans == null) {
            ans = new JSONObject();
            int time = DateUtil.hour(new Date(), true);
            int runTime = 101 * (time - 8);
            int standbyTime = Math.abs(runTime - generateRandomNumber(100, 180));
            int errorTime = Math.abs((time - 8));
            ans.put("runTime", Math.abs(runTime));
            ans.put("standbyTime", standbyTime);
            ans.put("errorTime", errorTime);

            ans.put("runRate", String.format("%.2f", runTime * 100.0 / (runTime + standbyTime + errorTime)));
            ans.put("standbyRate", String.format("%.2f", standbyTime * 100.0 / (runTime + standbyTime + errorTime)));
            ans.put("errorRate", String.format("%.2f", errorTime * 100.0 / (runTime + standbyTime + errorTime)));


            redisCache.setCacheObject("/jingmi/decive/status/time", ans, 10, TimeUnit.MINUTES);
            return AjaxResult.success(ans);

        } else {
            return AjaxResult.success(ans);
        }
    }


    /**
     * 高端复材，左下，设备稼动率 假数据
     *
     * @return
     */
    @GetMapping("/gaoduanfucai/decive/deviceRate")
    private AjaxResult gaoduanfucaiDeviceRate() {
        JSONObject ans = new JSONObject();
        List<String> monthList = new ArrayList();
        List<Float> deviceRateList = new ArrayList();
        final DateTime date = DateUtil.date();
        for (int i = 11; i >= 0; i--) {
            monthList.add(DateUtil.format(DateUtil.offsetMonth(date, -i), "MM") + "月");
        }
        deviceRateList.add(51.72f);
        deviceRateList.add(55.12f);
        deviceRateList.add(63.22f);
        deviceRateList.add(45.28f);
        deviceRateList.add(55.29f);
        deviceRateList.add(55.72f);
        deviceRateList.add(45.98f);
        deviceRateList.add(65.02f);
        deviceRateList.add(45.32f);
        deviceRateList.add(54.74f);
        deviceRateList.add(43.56f);
        deviceRateList.add(41.33f);


        ans.put("monthList", monthList);
        ans.put("deviceRateList", deviceRateList);
        return AjaxResult.success(ans);
    }


    /**
     * 生成指定范围内的随机数
     *
     * @param min 范围的最小值（包含）
     * @param max 范围的最大值（包含）
     * @return 生成的随机数
     * @throws IllegalArgumentException 如果min大于max
     */
    public int generateRandomNumber(int min, int max) {
        if (min > max) {
            throw new IllegalArgumentException("最小值不能大于最大值");
        }
        Random random = new Random();
        return random.nextInt((max - min) + 1) + min;
    }


    /**
     * 检查给定的时间是否在指定的时间区间内
     *
     * @param time  要检查的时间
     * @param start 区间开始时间
     * @param end   区间结束时间
     * @return 如果时间在区间内，返回 true；否则返回 false
     */
    public static boolean isTimeWithinRange(LocalTime time, LocalTime start, LocalTime end) {
        return !time.isBefore(start) && !time.isAfter(end);
    }
}

