package com.boyo.web.controller.wechat;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSONObject;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.common.core.domain.entity.SysUser;
import com.boyo.common.core.domain.model.LoginUser;
import com.boyo.common.core.redis.RedisCache;
import com.boyo.common.exception.CustomException;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.common.utils.StringUtils;
import com.boyo.framework.web.service.TokenService;
import com.boyo.system.service.IEnterpriseUserService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpMessageRouter;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/wx")
public class WxPortalController {
    private final WxMpService wxService;
    private final IEnterpriseUserService enterpriseUserService;
    private final RedisCache redisCache;
    public static final String appid = "wxb965ba4b2b8a7c3a";
    @GetMapping("/createBindQr")
    public AjaxResult getWechatUser() {
        String key = IdUtil.fastSimpleUUID();
        redisCache.setCacheObject(key, SecurityUtils.getLoginUser().getEnterpriseUser().getUserOpenid(),300, TimeUnit.SECONDS);
        String callback = "https://byy-client.ningmengdou.com/bind.html?key=" + key;
        String url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxb965ba4b2b8a7c3a&redirect_uri="+ URLUtil.encode(callback)+"&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect";
        return AjaxResult.success(url);
    }
    @GetMapping("/bindWechatUser")
    public AjaxResult getWechatUser(String code, String key) {
        try {
            if (ObjectUtil.isNull(redisCache.getCacheObject(key))) {
                throw new CustomException("二维码已过期，请重新生成!");
            }
            String userOpenid = redisCache.getCacheObject(key);
            redisCache.deleteObject(key);
            WxOAuth2AccessToken token = wxService.getOAuth2Service().getAccessToken(appid, "d54ea7671a27d07da7f45e1006639f43", code);
            String openid = token.getOpenId();
            EnterpriseUser user = enterpriseUserService.selectByOpenId(userOpenid);
            if (ObjectUtil.isNull(user)) {
                return AjaxResult.success("用户信息不存在");
            } else {
                if(StrUtil.isNotEmpty(user.getUserWechat())){
                    throw new CustomException("该用户已被其他微信号绑定，请勿重复绑定");
                }
                enterpriseUserService.updateWechat(userOpenid,openid);
                return AjaxResult.success();
            }
        } catch (WxErrorException e) {
            throw new CustomException("微信授权错误!");
        }
    }
    @PostMapping("/cancelWechat")
    public AjaxResult cancelWechat(){
        EnterpriseUser user = enterpriseUserService.selectByOpenId(SecurityUtils.getUserOpenid());
        if (ObjectUtil.isNull(user)) {
            return AjaxResult.success("用户信息不存在");
        } else {
            user.setUserWechat("");
            enterpriseUserService.updateById(user);
            return AjaxResult.success();
        }
    }

}
