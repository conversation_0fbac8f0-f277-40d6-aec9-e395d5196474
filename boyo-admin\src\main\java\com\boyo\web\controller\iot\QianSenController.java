package com.boyo.web.controller.iot;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.text.Convert;
import com.boyo.iot.domain.HistoryData;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.domain.IotEquipmentProp;
import com.boyo.iot.entity.Iofault;
import com.boyo.iot.entity.IorealData;
import com.boyo.iot.service.IIofaultService;
import com.boyo.iot.service.IIorealDataService;
import com.boyo.iot.service.IIotEquipmentPropService;
import com.boyo.iot.service.IIotEquipmentService;
import com.boyo.iot.util.IoTDBUtil;
import com.boyo.iot.vo.IoTAttrVO;
import com.boyo.master.service.IBaseDictService;
import com.boyo.mes.entity.ProductOrder;
import com.boyo.mes.entity.WorkReport;
import com.boyo.mes.service.IMesModulRecordService;
import com.boyo.mes.service.IProcessEquipmentService;
import com.boyo.mes.service.IProductOrderService;
import com.boyo.mes.service.IWorkReportService;
import com.github.pagehelper.PageHelper;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.rpc.StatementExecutionException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;


@RestController
@ApiOperation("千森木业大屏接口")
@RequestMapping("/qiansen")
@AllArgsConstructor
public class QianSenController extends BaseController {
    private final IIorealDataService realDataService;
    private final IoTDBUtil ioTDBUtil;
    private final IIofaultService iofaultService;
    private final IIotEquipmentService equipmentService;
    private final IProductOrderService orderService;
    private final IWorkReportService reportService;
    private final IIotEquipmentPropService iotEquipmentPropService;
    private final IIorealDataService iIorealDataService;


    @ApiOperation("查询当前最新数据")
    @GetMapping("/getCurrent")
    public AjaxResult getCurrent() {
        return AjaxResult.success(realDataService.selectIorealDataList(null));
    }

    @GetMapping("/chanliang")
    public AjaxResult getChanliang(String rq,String tag) throws IoTDBConnectionException, StatementExecutionException {
        String teanant = "9ad728691c824923a53cab8ecf1f8f71";
        String day = "08:00";
        if (StrUtil.isEmpty(rq)) {
            rq = DateUtil.formatDate(new Date());
        }
        String start = rq + " " + day + ":00";
        String end = DateUtil.formatDateTime(DateUtil.offsetDay(DateUtil.parseDateTime(start), 1));
        QueryWrapper<IotEquipment > queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tsl_id",9);
        List<IotEquipment> list1 = equipmentService.list(queryWrapper);
        double total = 0;
        for (IotEquipment iotEquipment : list1) {
            double number = 0;
            List<HistoryData> list = ioTDBUtil.listData(teanant,iotEquipment.getEquipmentCode(),tag,start,end);
            double min = 0;
            double max = 0;
            for (int i = 0; i < list.size(); i++) {
                if(list.get(i).getVal()!= null && !list.get(i).getVal().equals("null")) {
                    min = Convert.toDouble(list.get(i).getVal()) ;
                    break;
                }
            }
            for (int i = list.size()-1; i >0; i--) {
                if(list.get(i).getVal()!=null&&!list.get(i).getVal().equals("null")){
                    max = Convert.toDouble(list.get(i).getVal());
                    break;
                }
            }
            number = max - min;
            total +=number;
        }
        return AjaxResult.success(total);
    }

    @GetMapping("/lastchanliang")
    public AjaxResult getLastChanliang(String rq,String tag) throws IoTDBConnectionException, StatementExecutionException {
        String teanant = "9ad728691c824923a53cab8ecf1f8f71";
        String day = "08:00";
        if (StrUtil.isEmpty(rq)) {
            rq = DateUtil.formatDate(new Date());
        }
        String start = rq + " " + day + ":00";
        String end = DateUtil.formatDateTime(DateUtil.offsetDay(DateUtil.parseDateTime(start), -1));
        QueryWrapper<IotEquipment > queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tsl_id",9);
        List<IotEquipment> list1 = equipmentService.list(queryWrapper);
        double total = 0;
        for (IotEquipment iotEquipment : list1) {
            double number = 0;
            List<HistoryData> list = ioTDBUtil.listData(teanant,iotEquipment.getEquipmentCode(),tag,end,start);
            double min = 0;
            double max = 0;
            for (int i = 0; i < list.size(); i++) {
                if(list.get(i).getVal()!= null && !list.get(i).getVal().equals("null")) {
                    min = Convert.toDouble(list.get(i).getVal()) ;
                    break;
                }
            }
            for (int i = list.size()-1; i >0; i--) {
                if(list.get(i).getVal()!=null&&!list.get(i).getVal().equals("null")){
                    max = Convert.toDouble(list.get(i).getVal());
                    break;
                }
            }
            number = max - min;
            total +=number;
        }
        return AjaxResult.success(total);
    }

    @ApiOperation("产出量/用电量")
    @GetMapping("/getPEnum")
    public AjaxResult getPEnum(String tag){
        String tenant = "9ad728691c824923a53cab8ecf1f8f71";
        List<HistoryData> dataList = new ArrayList<>();
        // 获取今天的日期
        LocalDate today = LocalDate.now();
        // 循环输出七个时间段
        for (int i = 0; i < 7; i++) {
            // 获取当前日期往前第i天的日期
            LocalDate date1 = today.minus(i, ChronoUnit.DAYS);
            LocalDate date2 = today.minus(i+1, ChronoUnit.DAYS);
            // 获取当前日期的 8:00 时间
            LocalTime start = LocalTime.of(8, 0);
            // 创建一个时间段
            StringBuilder sb1 = new StringBuilder();
            StringBuilder sb2 = new StringBuilder();
            sb1.append(date1.toString()).append(" ").append(start.toString()).append(":00");
            sb2.append(date2.toString()).append(" ").append(start.toString()).append(":00");
            QueryWrapper<IotEquipment > queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("tsl_id",9);
            List<IotEquipment> iotEquipments = equipmentService.list(queryWrapper);
            double chanliang = 0;
            for (IotEquipment iotEquipment : iotEquipments) {
                JSONArray number = ioTDBUtil.getMaxAndMin(tenant, iotEquipment.getEquipmentCode(), tag, sb2.toString(), sb1.toString());
                if (number != null && number.size() > 0){
                    JSONObject jsonObject = number.getJSONObject(0);
                    if (jsonObject != null){
                        if (jsonObject.getDouble("max") != null && jsonObject.getDouble("min") != null){
                            chanliang = chanliang + jsonObject.getDouble("max") - jsonObject.getDouble("min");
                        }
                    }
                }

            }
            HistoryData data = new HistoryData();
            data.setTime(date2.toString());
            data.setVal(chanliang);
            dataList.add(data);
        }
        return AjaxResult.success(dataList);
    }

    @GetMapping("/getfaultList")
    public AjaxResult getFaultList(String rq){
        String day = "08:00";
        if (StrUtil.isEmpty(rq)) {
            rq = DateUtil.formatDate(new Date());
        }
        String start = rq + " " + day + ":00";
        String end = DateUtil.formatDateTime(DateUtil.offsetDay(DateUtil.parseDateTime(start), -7));
        QueryWrapper<Iofault> iofaultQueryWrapper = new QueryWrapper<>();
        iofaultQueryWrapper.between("create_time",end,start);
        List<Iofault> list = iofaultService.list(iofaultQueryWrapper);
        for (Iofault iofault : list) {
            QueryWrapper<IotEquipment> equipmentQueryWrapper = new QueryWrapper<>();
            equipmentQueryWrapper.eq("id",iofault.getEquipmentId());
            List<IotEquipment> list1 = equipmentService.list(equipmentQueryWrapper);
            if(list1.size()>0){
                iofault.setEquipmentName(list1.get(0).getEquipmentName());
            }
        }
        return AjaxResult.success(list);
    }

    @GetMapping("/orderlist")
    public AjaxResult getOrderList(String rq) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String day = "00:00";
        String night = "23:59";
        String startDay = "";
        Map<String , Integer> map = new LinkedHashMap<>();
        if (StrUtil.isEmpty(rq)) {
            rq = DateUtil.formatDate(new Date());
        }
        for (int i = 0; i < 7; i++) {
            startDay = DateUtil.formatDateTime(DateUtil.offsetDay(simpleDateFormat.parse(rq), -i));
            String[] s = startDay.split(" ");
            String startDay1 = s[0] + " " + day + ":00";
            String endNight = s[0] + " " + night + ":59";
            QueryWrapper<ProductOrder> orderQueryWrapper = new QueryWrapper<>();
            orderQueryWrapper.eq("order_status", 2);
            orderQueryWrapper.between("complete_time", startDay1, endNight);
            List<ProductOrder> list = orderService.list(orderQueryWrapper);
            map.put(s[0],list.size());
        }
        return AjaxResult.success(map);
    }

    @GetMapping("/reportlist")
    public AjaxResult getreportList(String rq) throws ParseException {
        DecimalFormat decimalFormat = new DecimalFormat("#0.00");
        String day = "08:00";
        if (StrUtil.isEmpty(rq)) {
            rq = DateUtil.formatDate(new Date());
        }
        String start = rq + " " + day + ":00";
        String end = DateUtil.formatDateTime(DateUtil.offsetDay(DateUtil.parseDateTime(start), 1));
        Map<Integer ,String> map = new LinkedHashMap<>();
        for (int i = 44;i<49;i++) {
            QueryWrapper<WorkReport> orderQueryWrapper = new QueryWrapper<>();
            orderQueryWrapper.eq("process_id", i);
            orderQueryWrapper.between("report_time", start, end);
            List<WorkReport> list = reportService.list(orderQueryWrapper);
            String passRate = "";
            double totalNumber = 0;
            double ngNumber = 0;
            for (WorkReport workReport : list) {
                totalNumber += workReport.getReportNum();
                ngNumber += workReport.getWasteNum();
            }
            if(totalNumber == 0){
                passRate = "0%";
            }else {
                passRate = decimalFormat.format(((totalNumber - ngNumber) / totalNumber)*100) + "%";
            }
            map.put(i,passRate);
        }
        return AjaxResult.success(map);
    }

    @GetMapping("/listCropRate")
    public AjaxResult listCropRate(String rq){
        String tenant="9ad728691c824923a53cab8ecf1f8f71";
        String day = "08:00";
        if (StrUtil.isEmpty(rq)) {
            rq = DateUtil.formatDate(new Date());
        }
        IotEquipment iotEquipment=new IotEquipment();
        iotEquipment.setTslId(9l);
        String start = rq + " " + day + ":00";
        List<IotEquipment> list=equipmentService.selectIotEquipmentList(iotEquipment);
        String s = DateUtil.formatDate(DateUtil.offsetDay(new Date(), 1));
        String end = s + " " + day + ":00";
        List<CropRate> cropRateList=new ArrayList<>();
        for (IotEquipment equipment : list) {
            CropRate cropRate=new CropRate();
            try {
                double workTime = 0;
                int waitTime = 0;
                JSONArray workStatusList=ioTDBUtil.listDataHistory(tenant,equipment.getEquipmentCode(),"Work_Status",start,end);
                for(int i=0;i< workStatusList.size();i++){
                    if(!"null".equals(workStatusList.getJSONObject(i).getString("Work_Status)"))&&workStatusList.getJSONObject(i).getString("Work_Status)")!=null){
                        if(Convert.toDouble(workStatusList.getJSONObject(i).getString("Work_Status)"))==1.0){
                            workTime++;
                        }else {
                            waitTime++;
                        }
                    }
                }
                cropRate.setId(equipment.getId());
                cropRate.setStart(workTime);
                cropRate.setWait(waitTime);
                cropRateList.add(cropRate);
            } catch (IoTDBConnectionException e) {
                e.printStackTrace();
            } catch (StatementExecutionException e) {
                e.printStackTrace();
            }
        }
        return AjaxResult.success(cropRateList);
    }

    @GetMapping("/getWorkRate")
    public AjaxResult getWorkRate(String tag) {
        DecimalFormat decimalFormat = new DecimalFormat("#0.00");
        List<IorealData> iorealDataList = realDataService.selectIorealDataList(null);
        double total = 0;
        double work = 0;
        for (IorealData iorealData : iorealDataList) {
            if(iorealData.getTag().equals(tag)){
                total++;
                if(iorealData.getVal().equals("1")){
                    work++;
                }
            }
        }
        String rate =  decimalFormat.format((work / total) *100) ;
        return AjaxResult.success(rate);
    }

    @GetMapping("/workshoplist")
    public AjaxResult getWorkShopList(){
        String cheJian = "一车间,二车间,三车间,四车间,五车间";
        String[] split = cheJian.split(",");
        List<IotEquipment> list = null;
        Map<String,List<QiansenEquip>> map = new LinkedHashMap<>();
        for (int i = 0; i < split.length; i++) {
            QueryWrapper<IotEquipment > equipmentQueryWrapper = new QueryWrapper<>();
            equipmentQueryWrapper.like("equipment_name",split[i]);
            list = equipmentService.list(equipmentQueryWrapper);
            List<QiansenEquip> qiansenEquipList = new ArrayList<>();
            for (IotEquipment  iotEquipment : list) {
               IorealData iorealData = new IorealData();
               iorealData.setDeviceCode(iotEquipment.getEquipmentCode());
               iorealData.setTag("Work_Status");
               List<IorealData> iorealDataList = iIorealDataService.selectIorealDataList(iorealData);
               QiansenEquip qiansenEquip = new QiansenEquip();
               qiansenEquip.setEquipmentName(iotEquipment.getEquipmentName());
               qiansenEquip.setStatus(iorealDataList.get(0).getVal());
               qiansenEquipList.add(qiansenEquip);
            }
            map.put(split[i],qiansenEquipList);
        }
        return AjaxResult.success(map);
    }

}

@Data
class QiansenEquip{

    String equipmentName;
    String Status;

}
