package com.boyo.web.controller.eam;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.eam.domain.EquipRelation;
import com.boyo.eam.domain.EquipWorkshopSection;
import com.boyo.eam.service.IEquipRelationService;
import com.boyo.eam.service.IEquipWorkshopSectionService;
import com.boyo.master.domain.ModelLine;
import com.boyo.master.domain.ModelWorkshop;
import com.boyo.master.service.IModelLineService;
import com.boyo.master.service.IModelWorkshopService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 车间、产线、工段关联表(EquipRelation)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-21 15:23:24
 */
@Api("车间、产线、工段关联表")
@RestController
@RequestMapping("/equip/equipRelation")
@AllArgsConstructor
public class EquipRelationController extends BaseController{
    /**
     * 服务对象
     */
    private final IEquipRelationService equipRelationService;
    private final IModelWorkshopService modelWorkshopService;
    private final IModelLineService modelLineService;
    private final IEquipWorkshopSectionService equipWorkshopSectionService;

    /**
     * 查询车间、产线、工段关联表列表
     *
     */
    @ApiOperation("查询车间、产线、工段关联表列表")
    @GetMapping("/list")
    public TableDataInfo list(EquipRelation equipRelation) {
        startPage();
        List<EquipRelation> list = equipRelationService.selectEquipRelationList(equipRelation);
        return getDataTable(list);
    }

    /**
     * 获取车间、产线、工段关联表详情
     */
    @ApiOperation("获取车间、产线、工段关联表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        EquipRelation er = equipRelationService.getById(id);
        if (er!=null){
            String relationType = er.getRelationType();
            String relation = er.getRelation();
            // 0生产非生产，1车间，2产线，3工段
            if ("0".equals(relationType)){
                if ("production".equals(relation)){
                    er.setName("生产");
                }else if("non-production".equals(relation)){
                    er.setName("非生产");
                }
            }else if ("1".equals(relationType)){
                ModelWorkshop modelWorkshop = modelWorkshopService.getOne(
                        Wrappers.<ModelWorkshop>lambdaQuery()
                                .eq(ModelWorkshop::getWorkshopOpenid, relation)
                );
                if (modelWorkshop!=null){
                    er.setName(modelWorkshop.getWorkshopName());
                }
            }else if ("2".equals(relationType)){
                ModelLine modelLine = modelLineService.getOne(
                        Wrappers.<ModelLine>lambdaQuery()
                                .eq(ModelLine::getLineOpenid, relation)
                );
                if (modelLine!=null){
                    er.setName(modelLine.getLineName());
                }
            }else if ("3".equals(relationType)){
                EquipWorkshopSection equipWorkshopSection = equipWorkshopSectionService.getOne(
                        Wrappers.<EquipWorkshopSection>lambdaQuery()
                                .eq(EquipWorkshopSection::getOpenid, relation)
                );
                if (equipWorkshopSection!=null){
                    er.setName(equipWorkshopSection.getName());
                }
            }else if ("4".equals(relationType)){
                if ("total".equals(relation)){
                    er.setName("总");
                }
            }
        }
        return AjaxResult.success(er);
    }

    /**
     * 新增车间、产线、工段关联表
     */
    @ApiOperation("新增车间、产线、工段关联表")
    @PostMapping
    public AjaxResult add(@RequestBody EquipRelation equipRelation) {
        equipRelation.setOpenid(generateOpenid());
        return toBooleanAjax(equipRelationService.save(equipRelation));
    }

    /**
     * 修改车间、产线、工段关联表
     */
    @ApiOperation("修改车间、产线、工段关联表")
    @PutMapping
    public AjaxResult edit(@RequestBody EquipRelation equipRelation) {
        return toBooleanAjax(equipRelationService.updateById(equipRelation));
    }

    /**
     * 删除车间、产线、工段关联表
     */
    @ApiOperation("删除车间、产线、工段关联表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(equipRelationService.removeByIds(Arrays.asList(ids)));
    }

}
