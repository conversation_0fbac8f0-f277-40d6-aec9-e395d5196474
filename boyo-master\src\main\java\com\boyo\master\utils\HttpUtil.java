package com.boyo.master.utils;

import cn.hutool.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

public class HttpUtil {
    static String NC_BASEURL = "http://192.168.0.54:8097/";
    static String NC_BASEURL_Test = "http://192.168.0.52:8097/";
    static String Tenant = "2db4bfe2cbe14269b410e4747073ee47";
    private static RestTemplate restTemplate = new RestTemplate();

    public static ResponseEntity<?> postData(Map<String, Object> requestBody) {

//        String url = "" + requestBody.get("url");
        String url = "http://221.2.63.235:1882/haihui/sale/ncData";

        // 构造请求头（如需要）
        HttpHeaders headers = new HttpHeaders();
        // headers.set(...); // 根据需要设置请求头

        // 使用HttpEntity封装请求体和请求头
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

        try {
            // 发送POST请求并获取响应
            ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
            String body = response.getBody();
            // 将字符串解析为JSON对象
            // 将JSON对象转换回字符串（没有转义字符）

            JSONObject jsonObject = new JSONObject(body);
            final String dataString = jsonObject.get("data").toString();

            jsonObject.remove("data");
            jsonObject.put("data", dataString);
            // 将内网服务的响应直接返回
            return ResponseEntity.status(response.getStatusCode()).body(jsonObject);
        } catch (Exception e) {
            // 捕获异常并处理，例如记录日志或返回错误响应
            return ResponseEntity.status(500).body("Internal Server Error: " + e.getMessage());
        }
    }
}
