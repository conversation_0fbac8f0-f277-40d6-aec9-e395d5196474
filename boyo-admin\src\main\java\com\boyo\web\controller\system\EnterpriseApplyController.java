package com.boyo.web.controller.system;

import java.util.Date;

import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.boyo.system.domain.EnterpriseApply;
import com.boyo.system.service.IEnterpriseApplyService;

/**
 * 申请试用Controller
 * <AUTHOR>
 */
@Api("申请试用")
@RestController
@RequestMapping("/system/apply")
@AllArgsConstructor
public class EnterpriseApplyController extends BaseController {
    private final IEnterpriseApplyService enterpriseApplyService;

    /**
     * 新增申请试用
     */
    @ApiOperation("新增申请试用")
    @PostMapping
    public AjaxResult add(@RequestBody EnterpriseApply enterpriseApply) {
        enterpriseApply.setCreateTime(new Date());
        return toBooleanAjax(enterpriseApplyService.save(enterpriseApply));
    }

}
