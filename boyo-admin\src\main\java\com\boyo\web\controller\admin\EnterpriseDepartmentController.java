package com.boyo.web.controller.admin;

import java.util.Date;
import java.util.List;
import java.util.Arrays;

import com.boyo.common.utils.SecurityUtils;
import com.boyo.common.utils.TreeTableUtils;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.boyo.system.domain.EnterpriseDepartment;
import com.boyo.system.service.IEnterpriseDepartmentService;

/**
 * 企业部门管理Controller
 *
 * <AUTHOR>
 */
@Api("企业部门管理")
@RestController
@RequestMapping("/system/department")
@AllArgsConstructor
public class EnterpriseDepartmentController extends BaseController {
    private final IEnterpriseDepartmentService enterpriseDepartmentService;

    /**
     * 查询企业部门管理列表
     */
    @ApiOperation("查询企业部门管理列表")
    @GetMapping("/list")
    public TableDataInfo list(EnterpriseDepartment enterpriseDepartment) {
        enterpriseDepartment.setEnterpriseOpenid(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid());
        List<EnterpriseDepartment> list = enterpriseDepartmentService.selectEnterpriseDepartmentList(enterpriseDepartment);
        list = TreeTableUtils.list2TreeList(list,"departmentOpenid","parentOpenid","children");
        return getDataTable(list);
    }

    /**
     * 获取企业部门管理详细信息
     */
    @ApiOperation("获取企业部门管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(enterpriseDepartmentService.getById(id));
    }

    /**
     * 新增企业部门管理
     */
    @ApiOperation("新增企业部门管理")
    @PostMapping
    public AjaxResult add(@RequestBody EnterpriseDepartment enterpriseDepartment) {
       return toBooleanAjax(enterpriseDepartmentService.save(enterpriseDepartment));
    }

    /**
     * 修改企业部门管理
     */
    @ApiOperation("修改企业部门管理")
    @PutMapping
    public AjaxResult edit(@RequestBody EnterpriseDepartment enterpriseDepartment) {
        return toBooleanAjax(enterpriseDepartmentService.updateById(enterpriseDepartment));
    }

    /**
     * 删除企业部门管理
     */
    @ApiOperation("删除企业部门管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(enterpriseDepartmentService.removeByIds(Arrays.asList(ids)));
    }
}
