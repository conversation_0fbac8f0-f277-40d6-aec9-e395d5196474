package com.boyo.mes.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boyo.mes.entity.EquipmentApply;
import java.util.List;

/**
 * 上下班记录(EquipmentApply)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-03 16:47:21
 */
public interface EquipmentApplyMapper extends BaseMapper<EquipmentApply>{

    /**
     * 通过实体作为筛选条件查询
     *
     * @param equipmentApply 实例对象
     * @return 对象列表
     */
    List<EquipmentApply> selectEquipmentApplyList(EquipmentApply equipmentApply);


}

