package com.boyo.web.controller.master;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;
import java.util.Map;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSONObject;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.core.text.Convert;
import com.boyo.master.domain.TMaterial;
import com.boyo.master.service.ITMaterialService;
import com.boyo.master.vo.MaterialVO;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;


/**
 * 主数据-物料Controller
 *
 * <AUTHOR>
 */
@Api("主数据-物料")
@RestController
@RequestMapping("/master/material")
@AllArgsConstructor
public class MaterialController extends BaseController {
    private final ITMaterialService tMaterialService;

    /**
     * 查询主数据-物料列表
     */
    @ApiOperation("查询主数据-物料列表")
    @GetMapping("/list")
    public TableDataInfo list(TMaterial tMaterial) {
        startPage();
        List<MaterialVO> list = tMaterialService.selectTMaterialList(tMaterial);
        return getDataTable(list);
    }

    /**
     * 获取主数据-物料详细信息
     */
    @ApiOperation("获取主数据-物料详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(tMaterialService.getById(id));
    }

    /**
     * 新增主数据-物料
     */
    @ApiOperation("新增主数据-物料")
    @PostMapping
    public AjaxResult add(@RequestBody TMaterial tMaterial) {
        if(tMaterialService.checkExist(tMaterial)){
            return AjaxResult.error("物料编码已存在！");
        }
        tMaterial.setMaterielOpenid(super.generateOpenid());
        return toBooleanAjax(tMaterialService.save(tMaterial));
    }

    /**
     * 修改主数据-物料
     */
    @ApiOperation("修改主数据-物料")
    @PutMapping
    public AjaxResult edit(@RequestBody TMaterial tMaterial) {
        if(tMaterialService.checkExist(tMaterial)){
            return AjaxResult.error("物料编码已存在！");
        }
        return toBooleanAjax(tMaterialService.updateById(tMaterial));
    }

    /**
     * 删除主数据-物料
     */
    @ApiOperation("删除主数据-物料")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(tMaterialService.removeByIds(Arrays.asList(ids)));
    }

    @PostMapping("/upload")
    public AjaxResult uploadFile(@RequestParam("file") MultipartFile file) throws Exception {
        try {
            ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
            List<Map<String,Object>> readAll = reader.readAll();
            List<TMaterial> materialList = new ArrayList<>();
            for (int i = 0; i < readAll.size(); i++) {
                TMaterial material = new TMaterial();
                material.setMaterielName(Convert.toStr(readAll.get(i).get("产品名称")));
                material.setMaterielCode(Convert.toStr(readAll.get(i).get("产品编码")));
                material.setMaterielType("type_product");
                material.setMaterielOpenid(super.generateOpenid());
                materialList.add(material);
            }
            if(materialList.size() > 0){
                tMaterialService.saveBatch(materialList);
            }
            return AjaxResult.success();
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        }
    }
}
