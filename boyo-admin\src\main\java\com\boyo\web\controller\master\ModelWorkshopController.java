package com.boyo.web.controller.master;

import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.master.domain.ModelWorkshop;
import com.boyo.master.service.IModelWorkshopService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 工厂模型-车间Controller
 *
 * <AUTHOR>
 */
@Api("工厂模型-车间")
@RestController
@RequestMapping("/master/workshop")
@AllArgsConstructor
public class ModelWorkshopController extends BaseController {
    private final IModelWorkshopService modelWorkshopService;

    /**
     * 查询工厂模型-车间列表
     */
    @ApiOperation("查询工厂模型-车间列表")
    @GetMapping("/list")
    public TableDataInfo list(ModelWorkshop modelWorkshop) {
        startPage();
        List<ModelWorkshop> list = modelWorkshopService.selectModelWorkshopList(modelWorkshop);
        return getDataTable(list);
    }

    /**
     * 获取工厂模型-车间详细信息
     */
    @ApiOperation("获取工厂模型-车间详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(modelWorkshopService.getById(id));
    }

    /**
     * 新增工厂模型-车间
     */
    @ApiOperation("新增工厂模型-车间")
    @PostMapping
    public AjaxResult add(@RequestBody ModelWorkshop modelWorkshop) {
        modelWorkshop.setWorkshopOpenid(super.generateOpenid());
        return toBooleanAjax(modelWorkshopService.save(modelWorkshop));
    }

    /**
     * 修改工厂模型-车间
     */
    @ApiOperation("修改工厂模型-车间")
    @PutMapping
    public AjaxResult edit(@RequestBody ModelWorkshop modelWorkshop) {
        return toBooleanAjax(modelWorkshopService.updateById(modelWorkshop));
    }

    /**
     * 删除工厂模型-车间
     */
    @ApiOperation("删除工厂模型-车间")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(modelWorkshopService.removeByIds(Arrays.asList(ids)));
    }
}
