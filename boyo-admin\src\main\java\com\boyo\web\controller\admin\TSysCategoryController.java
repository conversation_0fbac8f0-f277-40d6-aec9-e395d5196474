package com.boyo.web.controller.admin;

import java.util.List;
import java.util.Arrays;

import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.boyo.system.domain.TSysCategory;
import com.boyo.system.service.ITSysCategoryService;

/**
 * 系统类别管理Controller
 *
 * <AUTHOR>
 */
@Api("系统类别管理")
@RestController
@RequestMapping("/system/category")
@AllArgsConstructor
public class TSysCategoryController extends BaseController {

    private final ITSysCategoryService tSysCategoryService;

    /**
     * 查询系统类别管理列表
     */
    @ApiOperation("查询系统类别管理列表")
    @GetMapping("/list")
    public TableDataInfo list(TSysCategory tSysCategory) {
        startPage();
        List<TSysCategory> list = tSysCategoryService.selectTSysCategoryList(tSysCategory);
        return getDataTable(list);
    }

    /**
     * 获取系统类别管理详细信息
     */
    @ApiOperation("获取系统类别管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(tSysCategoryService.getById(id));
    }

    /**
     * 新增系统类别管理
     */
    @ApiOperation("新增系统类别管理")
    @PostMapping
    public AjaxResult add(@RequestBody TSysCategory tSysCategory) {
        return toBooleanAjax(tSysCategoryService.save(tSysCategory));
    }

    /**
     * 修改系统类别管理
     */
    @ApiOperation("修改系统类别管理")
    @PutMapping
    public AjaxResult edit(@RequestBody TSysCategory tSysCategory) {
        return toBooleanAjax(tSysCategoryService.updateById(tSysCategory));
    }

    /**
     * 删除系统类别管理
     */
    @ApiOperation("删除系统类别管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(tSysCategoryService.removeByIds(Arrays.asList(ids)));
    }
}
