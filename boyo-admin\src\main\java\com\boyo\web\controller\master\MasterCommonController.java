package com.boyo.web.controller.master;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.master.domain.ModelAllocation;
import com.boyo.master.domain.ModelArea;
import com.boyo.master.domain.ModelWarehouse;
import com.boyo.master.domain.TModelFactory;
import com.boyo.master.service.IModelAllocationService;
import com.boyo.master.service.IModelAreaService;
import com.boyo.master.service.IModelWarehouseService;
import com.boyo.master.service.ITModelFactoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@Api("主数据-公用接口")
@RestController
@RequestMapping("/master/common")
@AllArgsConstructor
public class MasterCommonController extends BaseController {

    private final ITModelFactoryService modelFactoryService;
    private final IModelWarehouseService modelWarehouseService;
    private final IModelAreaService modelAreaService;
    private final IModelAllocationService allocationService;

    /**
     * 获取工厂仓库模型-到库区
     *
     * @return
     */
    @ApiOperation("获取工厂仓库模型-到库区")
    @GetMapping("/getWmsModelToArea")
    public AjaxResult getWmsModelToArea() {
        JSONArray result = new JSONArray();
        List<TModelFactory> factoryList = modelFactoryService.list();
        List<ModelWarehouse> warehouseList = modelWarehouseService.list();
        List<ModelArea> areaList = modelAreaService.list();

        JSONArray warehouses = new JSONArray();
        if (warehouseList != null && warehouseList.size() > 0) {
            for (int i = 0; i < warehouseList.size(); i++) {
                ModelWarehouse warehouse = warehouseList.get(i);
                JSONObject temp = new JSONObject();
                temp.put("value", warehouse.getWarehouseOpenid());
                temp.put("label", warehouse.getWarehouseName());
                temp.put("parent", warehouse.getWarehouseFactory());
                temp.put("children", new ArrayList<>());
                if (areaList != null && areaList.size() > 0) {
                    for (int j = 0; j < areaList.size(); j++) {
                        ModelArea area = areaList.get(j);
                        if (area.getAreaWarehouseId().equals(warehouse.getWarehouseOpenid())) {
                            JSONObject child = new JSONObject();
                            child.put("value", area.getAreaOpenid());
                            child.put("label", area.getAreaName());
                            temp.getJSONArray("children").add(child);
                        }
                    }
                }
                warehouses.add(temp);
            }
        }
        if (factoryList != null && factoryList.size() > 0) {
            for (int i = 0; i < factoryList.size(); i++) {
                TModelFactory factory = factoryList.get(i);
                JSONObject temp = new JSONObject();
                temp.put("value", factory.getFactoryOpenid());
                temp.put("label", factory.getFactoryName());
                temp.put("children", new ArrayList<>());
                if (warehouses != null && warehouses.size() > 0) {
                    for (int j = 0; j < warehouses.size(); j++) {
                        JSONObject warehouse = warehouses.getJSONObject(j);
                        if (factory.getFactoryOpenid().equals(warehouse.getString("parent"))) {
                            temp.getJSONArray("children").add(warehouse);
                        }
                    }
                }
                result.add(temp);
            }
        }
        return AjaxResult.success(result);
    }

    @ApiOperation("获取工厂仓库模型-到货位")
    @GetMapping("/getWmsModelToAllocation")
    public AjaxResult getWmsModelToAllocation() {
        JSONArray result = new JSONArray();
        List<TModelFactory> factoryList = modelFactoryService.list();
        List<ModelWarehouse> warehouseList = modelWarehouseService.list();
        List<ModelArea> areaList = modelAreaService.list();
        List<ModelAllocation> allocationList = allocationService.list();
        JSONArray areas = new JSONArray();
        if (areaList != null && areaList.size() > 0) {
            for (int i = 0; i < areaList.size(); i++) {
                ModelArea area = areaList.get(i);
                JSONObject temp = new JSONObject();
                temp.put("value", area.getAreaOpenid());
                temp.put("label", area.getAreaName());
                temp.put("parent", area.getAreaWarehouseId());
                temp.put("children", new ArrayList<>());
                if (allocationList != null && allocationList.size() > 0) {
                    for (int j = 0; j < allocationList.size(); j++) {
                        ModelAllocation allocation = allocationList.get(j);
                        if (allocation.getAllocationArea().equals(area.getAreaOpenid())) {
                            JSONObject child = new JSONObject();
                            child.put("value", allocation.getAllocationOpenid());
                            child.put("label", allocation.getAllocationName());
                            temp.getJSONArray("children").add(child);
                        }
                    }
                }
                areas.add(temp);
            }
        }
        JSONArray warehouses = new JSONArray();
        if (warehouseList != null && warehouseList.size() > 0) {
            for (int i = 0; i < warehouseList.size(); i++) {
                ModelWarehouse warehouse = warehouseList.get(i);
                JSONObject temp = new JSONObject();
                temp.put("value", warehouse.getWarehouseOpenid());
                temp.put("label", warehouse.getWarehouseName());
                temp.put("parent", warehouse.getWarehouseFactory());
                temp.put("children", new ArrayList<>());
                if (areas != null && areas.size() > 0) {
                    for (int j = 0; j < areas.size(); j++) {
                        JSONObject area = areas.getJSONObject(j);
                        if (warehouse.getWarehouseOpenid().equals(area.getString("parent"))) {
                            temp.getJSONArray("children").add(area);
                        }
                    }
                }
                warehouses.add(temp);
            }
        }
        if (factoryList != null && factoryList.size() > 0) {
            for (int i = 0; i < factoryList.size(); i++) {
                TModelFactory factory = factoryList.get(i);
                JSONObject temp = new JSONObject();
                temp.put("value", factory.getFactoryOpenid());
                temp.put("label", factory.getFactoryName());
                temp.put("children", new ArrayList<>());
                if (warehouses != null && warehouses.size() > 0) {
                    for (int j = 0; j < warehouses.size(); j++) {
                        JSONObject warehouse = warehouses.getJSONObject(j);
                        if (factory.getFactoryOpenid().equals(warehouse.getString("parent"))) {
                            temp.getJSONArray("children").add(warehouse);
                        }
                    }
                }
                result.add(temp);
            }
        }
        return AjaxResult.success(result);
    }
}
