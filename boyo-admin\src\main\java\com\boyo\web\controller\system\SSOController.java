package com.boyo.web.controller.system;

import cn.hutool.core.util.PhoneUtil;
import com.boyo.common.annotation.Log;
import com.boyo.common.constant.UserConstants;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.domain.entity.SysRole;
import com.boyo.common.core.domain.entity.SysUser;
import com.boyo.common.core.domain.model.LoginUser;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.enums.BusinessType;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.common.utils.ServletUtils;
import com.boyo.common.utils.StringUtils;
import com.boyo.common.utils.poi.ExcelUtil;
import com.boyo.framework.web.service.TokenService;
import com.boyo.system.service.ISysPostService;
import com.boyo.system.service.ISysRoleService;
import com.boyo.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/openapi/")
public class SSOController extends BaseController {
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysPostService postService;

    @Autowired
    private TokenService tokenService;


    /**
     * 新增用户
     *
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/addSSO")
    public AjaxResult add(@RequestParam("telphone") String telphone,
                          @RequestParam("username") String username,
                          @RequestParam("nickname") String nickname,
                          @RequestParam(value = "password", required = false) String password,
                          @RequestParam(value = "salt", required = false) String salt,
                          HttpServletRequest request) {
        if (!PhoneUtil.isMobile(telphone)) {
            return AjaxResult.error("无效的手机号");
        }
        SysUser user = userService.selectUserByPhone(telphone);
        if (user == null) {
            if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(username))) {
                return AjaxResult.error("新增用户'" + username + "'失败，登录账号已存在");
            }
            SysUser sysUser = new SysUser();
            sysUser.setUserName(username);
            sysUser.setPhonenumber(telphone);
            sysUser.setNickName(nickname);
            sysUser.setCreateBy(username);
            sysUser.setPassword(password);
            return toAjax(userService.insertUser(sysUser));
        }
        return AjaxResult.error();
    }

}
