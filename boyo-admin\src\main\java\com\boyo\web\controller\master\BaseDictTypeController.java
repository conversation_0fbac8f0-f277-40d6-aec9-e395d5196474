package com.boyo.web.controller.master;

import java.util.List;
import java.util.Arrays;

import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.boyo.master.domain.BaseDictType;
import com.boyo.master.service.IBaseDictTypeService;

/**
 * 数据字典类型管理Controller
 *
 * <AUTHOR>
 */
@Api("数据字典类型管理")
@RestController
@RequestMapping("/master/dictType")
@AllArgsConstructor
public class BaseDictTypeController extends BaseController {
    private final IBaseDictTypeService baseDictTypeService;

    /**
     * 查询数据字典类型管理列表
     */
    @ApiOperation("查询数据字典类型管理列表")
    @GetMapping("/list")
    public TableDataInfo list(BaseDictType baseDictType) {
        startPage();
        List<BaseDictType> list = baseDictTypeService.selectBaseDictTypeList(baseDictType);
        return getDataTable(list);
    }

    /**
     * 获取数据字典类型管理详细信息
     */
    @ApiOperation("获取数据字典类型管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(baseDictTypeService.getById(id));
    }

    /**
     * 新增数据字典类型管理
     */
    @ApiOperation("新增数据字典类型管理")
    @PostMapping
    public AjaxResult add(@RequestBody BaseDictType baseDictType) {
        return toBooleanAjax(baseDictTypeService.save(baseDictType));
    }

    /**
     * 修改数据字典类型管理
     */
    @ApiOperation("修改数据字典类型管理")
    @PutMapping
    public AjaxResult edit(@RequestBody BaseDictType baseDictType) {
        return toBooleanAjax(baseDictTypeService.updateById(baseDictType));
    }

    /**
     * 删除数据字典类型管理
     */
    @ApiOperation("删除数据字典类型管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(baseDictTypeService.removeByIds(Arrays.asList(ids)));
    }
}
