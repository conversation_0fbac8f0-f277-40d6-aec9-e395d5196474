package com.boyo.web.controller.admin;

import java.util.List;
import java.util.Arrays;

import com.boyo.common.utils.SecurityUtils;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.boyo.system.domain.EnterpriseRole;
import com.boyo.system.service.IEnterpriseRoleService;

/**
 * 企业角色管理Controller
 *
 * <AUTHOR>
 */
@Api("企业角色管理")
@RestController
@RequestMapping("/system/enterpriserole")
@AllArgsConstructor
public class EnterpriseRoleController extends BaseController {
    private final IEnterpriseRoleService enterpriseRoleService;

    /**
     * 查询企业角色管理列表
     */
    @ApiOperation("查询企业角色管理列表")
    @GetMapping("/list")
    public TableDataInfo list(EnterpriseRole enterpriseRole) {
        startPage();
        enterpriseRole.setEnterpriseOpenid(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid());
        List<EnterpriseRole> list = enterpriseRoleService.selectEnterpriseRoleList(enterpriseRole);
        return getDataTable(list);
    }

    /**
     * 获取企业角色管理详细信息
     */
    @ApiOperation("获取企业角色管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(enterpriseRoleService.getById(id));
    }

    /**
     * 新增企业角色管理
     */
    @ApiOperation("新增企业角色管理")
    @PostMapping
    public AjaxResult add(@RequestBody EnterpriseRole enterpriseRole) {
        return toBooleanAjax(enterpriseRoleService.save(enterpriseRole));
    }

    /**
     * 修改企业角色管理
     */
    @ApiOperation("修改企业角色管理")
    @PutMapping
    public AjaxResult edit(@RequestBody EnterpriseRole enterpriseRole) {
        return toBooleanAjax(enterpriseRoleService.updateById(enterpriseRole));
    }

    /**
     * 删除企业角色管理
     */
    @ApiOperation("删除企业角色管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(enterpriseRoleService.removeByIds(Arrays.asList(ids)));
    }
}
