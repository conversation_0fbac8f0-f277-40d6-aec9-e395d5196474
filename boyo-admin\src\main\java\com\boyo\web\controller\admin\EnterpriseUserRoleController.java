package com.boyo.web.controller.admin;

import java.util.List;
import java.util.Arrays;

import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.boyo.system.domain.EnterpriseUserRole;
import com.boyo.system.service.IEnterpriseUserRoleService;

/**
 * 企业用户角色管理Controller
 *
 * <AUTHOR>
 */
@Api("企业用户角色管理")
@RestController
@RequestMapping("/system/enterpriseuserrole")
@AllArgsConstructor
public class EnterpriseUserRoleController extends BaseController {
    private final IEnterpriseUserRoleService enterpriseUserRoleService;

    /**
     * 查询企业用户角色管理列表
     */
    @ApiOperation("查询企业用户角色管理列表")
    @GetMapping("/list")
    public TableDataInfo list(EnterpriseUserRole enterpriseUserRole) {
        startPage();
        List<EnterpriseUserRole> list = enterpriseUserRoleService.selectEnterpriseUserRoleList(enterpriseUserRole);
        return getDataTable(list);
    }

    /**
     * 获取企业用户角色管理详细信息
     */
    @ApiOperation("获取企业用户角色管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(enterpriseUserRoleService.getById(id));
    }

    /**
     * 新增企业用户角色管理
     */
    @ApiOperation("新增企业用户角色管理")
    @PostMapping
    public AjaxResult add(@RequestBody EnterpriseUserRole enterpriseUserRole) {
        return toBooleanAjax(enterpriseUserRoleService.save(enterpriseUserRole));
    }

    /**
     * 修改企业用户角色管理
     */
    @ApiOperation("修改企业用户角色管理")
    @PutMapping
    public AjaxResult edit(@RequestBody EnterpriseUserRole enterpriseUserRole) {
        return toBooleanAjax(enterpriseUserRoleService.updateById(enterpriseUserRole));
    }

    /**
     * 删除企业用户角色管理
     */
    @ApiOperation("删除企业用户角色管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(enterpriseUserRoleService.removeByIds(Arrays.asList(ids)));
    }
}
