package com.boyo.web.isv.controller;

import com.boyo.web.isv.model.*;
import com.boyo.web.isv.service.IsvProduceService;
import com.boyo.web.isv.util.IsvProduceAPI;
import com.boyo.web.isv.util.ParamConverter;
import com.boyo.web.isv.util.ResultCodeEnum;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

import static org.springframework.http.MediaType.APPLICATION_JSON_UTF8_VALUE;

@RestController
@RequestMapping(value = "/isv/produce")
public class IsvProduceController {


    ObjectMapper mapper = new ObjectMapper();

    /**
     * 云市场分配的秘钥，在服务商入驻云市场成功后，isvConsole可以查看,因为安全性，并不建议写死
     */
    private static final String ACCESS_KEY = "2ff48783-7ba4-4159-9086-0c7c70e3b4eb";

    /**
     * 返回体header中的签名key
     */
    private static final String BODY_SIGN_KEY = "Body-Sign";

    private static final String CONTENT_TYPE = "Content-Type";

    /**
     * 返回体header中签名key的value
     */
    private static final String BODY_SIGN_VALUE = "sign_type=\"HMAC-SHA256\", signature= \"{signature}\"";

    @Resource
    private IsvProduceService isvProduceService;


    /**
     * 新购生产接口请求处理
     * 对接指导：https://support.huaweicloud.com/accessg-marketplace/zh-cn_topic_0000001353181669.html
     *
     * @param request  请求体
     * @param response 返回
     * @throws IOException 异常
     */
    @RequestMapping(value = "", params = "activity=getLicense")
    public void getLicense(HttpServletRequest request, HttpServletResponse response) throws IOException {

        Map<String, String[]> paramsMap = request.getParameterMap();
        if (verifyParams(IsvProduceAPI.verificationRequestQueryParams(paramsMap, ACCESS_KEY), response)) {
            return;
        }

        // 将请求参数封装到对象中
        GetLicenseReq getLicenseReq = ParamConverter.convertGetLicenseReq(paramsMap);

        // activity=getLicense时，扩展参数必填，需要进行解密
        getLicenseReq.setSaasExtendParams(IsvProduceAPI.decryptSaasExtendParams(getLicenseReq.getSaasExtendParams()));

        // 服务商实现具体的新购资源生成逻辑
        GetLicenseInfo getLicenseInfo = isvProduceService.getLicense(getLicenseReq);

        if (null == getLicenseInfo) {
            writeErrorResp(ResultCodeEnum.OTHER_INNER_ERROR, response);
            return;
        }

        // 生成资源成功后，替换以下变量值为实际业务参数 构造返回
        String instanceId = getLicenseInfo.getInstanceId(); // 资源实例id，最好使用uuid，确保instanceId的唯一性
        String license = getLicenseInfo.getLicense(); // license识别码

        String responseBody = buildGetLicenseSuccessResponse(instanceId, license);
        writeSuccessResp(response, responseBody);
    }

    /**
     * 续费生产接口请求处理
     * 对接指导：https://support.huaweicloud.com/accessg-marketplace/zh-cn_topic_0000001353381237.html
     *
     * @param request  请求体
     * @param response 返回
     * @throws IOException 异常
     */
    @RequestMapping(value = "", params = "activity=refreshLicense")
    public void refreshLicense(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Map<String, String[]> paramsMap = request.getParameterMap();

        // 校验参数是否合法，只做最基础的合法性校验
        if (verifyParams(IsvProduceAPI.verificationRequestQueryParams(paramsMap, ACCESS_KEY), response)) {
            return;
        }

        // 将请求参数封装到对象中，后续业务可以直接使用
        RefreshLicenseReq refreshLicenseReq = ParamConverter.convertRefreshLicenseReq(paramsMap);

        // 服务商实现具体的资源续费逻辑
        boolean isSuccess = isvProduceService.refreshLicense(refreshLicenseReq);

        if (isSuccess) {
            writeSuccessResp(response, null);
            return;
        }
        writeErrorResp(ResultCodeEnum.OTHER_INNER_ERROR, response);
    }

    /**
     * 过期生产接口请求处理
     * 对接指导：https://support.huaweicloud.com/accessg-marketplace/zh-cn_topic_0000001300621648.html
     *
     * @param request  请求体
     * @param response 返回
     * @throws IOException 异常
     */
    @RequestMapping(value = "", params = "activity=expireLicense")
    public void expireLicense(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Map<String, String[]> paramsMap = request.getParameterMap();

        // 校验参数是否合法，只做最基础的合法性校验
        if (verifyParams(IsvProduceAPI.verificationRequestQueryParams(paramsMap, ACCESS_KEY), response)) {
            return;
        }

        // 将请求参数封装到对象中，后续业务可以直接使用
        ExpireLicenseReq expireLicenseReq = ParamConverter.convertExpireLicenseReq(paramsMap);

        // 服务商实现具体的资源过期逻辑
        boolean isSuccess = isvProduceService.expireLicense(expireLicenseReq);
        if (isSuccess) {
            // 过期处理不需要返回其他数据，只要返回成功结果码
            writeSuccessResp(response, null);
            return;
        }
        writeErrorResp(ResultCodeEnum.OTHER_INNER_ERROR, response);
    }

    /**
     * 资源释放生产接口请求处理
     * 对接指导：https://support.huaweicloud.com/accessg-marketplace/zh-cn_topic_0000001353261785.html
     *
     * @param request  请求体
     * @param response 返回
     * @throws IOException 异常
     */
    @RequestMapping(value = "", params = "activity=releaseLicense")
    public void releaseLicense(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Map<String, String[]> paramsMap = request.getParameterMap();

        // 校验参数是否合法，只做最基础的合法性校验
        if (verifyParams(IsvProduceAPI.verificationRequestQueryParams(paramsMap, ACCESS_KEY), response)) {
            return;
        }

        // 将请求参数封装到对象中，后续业务可以直接使用
        ReleaseLicenseReq releaseLicenseReq = ParamConverter.convertReleaseLicenseReq(paramsMap);

        // 服务商实现具体的资源释放逻辑
        boolean isSuccess = isvProduceService.releaseLicense(releaseLicenseReq);
        if (isSuccess) {
            // 资源释放不需要返回其他数据，只要返回成功结果码
            writeSuccessResp(response, null);
            return;
        }
        writeErrorResp(ResultCodeEnum.OTHER_INNER_ERROR, response);
    }

    /**
     * 输出错误响应
     *
     * @param resultCodeEnum 错误响应内容
     * @param response HttpServletResponse
     * @throws IOException 异常
     */
    private void writeErrorResp(ResultCodeEnum resultCodeEnum, HttpServletResponse response) throws IOException {
        String responseBody = buildFailedResponse(resultCodeEnum.getResultCode(), resultCodeEnum.getResultMsg());

        // 对待返回的请求体计算签名
        String signature = IsvProduceAPI.generateResponseBodySignature(ACCESS_KEY, responseBody);

        // 将签名放到header中
        response.setHeader(BODY_SIGN_KEY, BODY_SIGN_VALUE.replace("{signature}", signature));

        // 资源生成失败
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        response.getWriter().print(responseBody);
    }

    /**
     * 根据参数验证结果做不同的响应
     *
     * @param verificationResult 参数校验结果
     * @param response HttpServletResponse
     * @return true:参数验证不通过,false:参数验证通过
     * @throws IOException 异常
     */
    private boolean verifyParams(boolean verificationResult, HttpServletResponse response) throws IOException {
        // 校验参数是否合法，只做最基础的合法性校验
        if (!verificationResult) {
            writeErrorResp(ResultCodeEnum.INVALID_PARAM, response);
            return true;
        }
        return false;
    }

    /**
     * 构建失败响应体
     * 具体可用错误码参考：https://support.huaweicloud.com/accessg-marketplace/zh-cn_topic_0070649057.html
     *
     * @param code    返回码
     * @param message 错误信息
     * @return 构造好的失败返回体
     */
    private String buildFailedResponse(String code, String message) throws JsonProcessingException {
        BaseResp result = new BaseResp();
        result.setResultMsg(message);
        result.setResultCode(code);
        return mapper.writeValueAsString(result);
    }

    private String buildGetLicenseSuccessResponse(String instanceId, String license) throws JsonProcessingException {
        mapper.getFactory().configure(JsonGenerator.Feature.ESCAPE_NON_ASCII, true);

        GetLicenseResp getLicenseResp = new GetLicenseResp();

        getLicenseResp.setResultMsg(ResultCodeEnum.SUCCESS.getResultMsg());
        getLicenseResp.setResultCode(ResultCodeEnum.SUCCESS.getResultCode());
        getLicenseResp.setInstanceId(instanceId);
        getLicenseResp.setLicense(license);

        return mapper.writeValueAsString(getLicenseResp);
    }

    /**
     * 输出正确响应结果
     *
     * @param response HttpServletResponse
     * @param successResp 成功响应内容
     * @throws IOException 异常
     */
    private void writeSuccessResp(HttpServletResponse response, String successResp) throws IOException {
        String responseBody;
        if (StringUtils.isBlank(successResp)) {
            // 返回成功结果码
            responseBody = buildSuccessResponse();
        } else {
            responseBody = successResp;
        }

        // 对待返回的请求体计算签名
        String signature = IsvProduceAPI.generateResponseBodySignature(ACCESS_KEY, responseBody);

        // 将签名放到header中
        response.setHeader(BODY_SIGN_KEY, BODY_SIGN_VALUE.replace("{signature}", signature));
        response.setHeader(CONTENT_TYPE, APPLICATION_JSON_UTF8_VALUE);
        response.getWriter().print(responseBody);
    }

    /**
     * 构建续费,过期,资源释放成功响应体
     *
     * @return 构建好的成功返回体
     */
    private String buildSuccessResponse() throws JsonProcessingException {
        BaseResp result = new BaseResp();
        result.setResultMsg(ResultCodeEnum.SUCCESS.getResultMsg());
        result.setResultCode(ResultCodeEnum.SUCCESS.getResultCode());
        return mapper.writeValueAsString(result);
    }
}
