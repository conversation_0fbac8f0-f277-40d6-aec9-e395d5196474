package com.boyo.web.controller.eam;

import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.eam.domain.EquipState;
import com.boyo.eam.service.IEquipStateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 设备状态表(EquipState)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
@Api("设备状态表")
@RestController
@RequestMapping("/equip/equipState")
@AllArgsConstructor
public class EquipStateController extends BaseController{
    /**
     * 服务对象
     */
    private final IEquipStateService equipStateService;

    /**
     * 查询设备状态表列表
     *
     */
    @ApiOperation("查询设备状态表列表")
    @GetMapping("/list")
    public TableDataInfo list(EquipState equipState) {
        startPage();
        List<EquipState> list = equipStateService.selectEquipStateList(equipState);
        return getDataTable(list);
    }
    
    /**
     * 获取设备状态表详情
     */
    @ApiOperation("获取设备状态表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(equipStateService.getById(id));
    }

    /**
     * 新增设备状态表
     */
    @ApiOperation("新增设备状态表")
    @PostMapping
    public AjaxResult add(@RequestBody EquipState equipState) {
        equipState.setOpenid(super.generateOpenid());
        return toBooleanAjax(equipStateService.save(equipState));
    }

    /**
     * 修改设备状态表
     */
    @ApiOperation("修改设备状态表")
    @PutMapping
    public AjaxResult edit(@RequestBody EquipState equipState) {
        return toBooleanAjax(equipStateService.updateById(equipState));
    }

    /**
     * 删除设备状态表
     */
    @ApiOperation("删除设备状态表")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(equipStateService.removeByIds(Arrays.asList(ids)));
    }

}
