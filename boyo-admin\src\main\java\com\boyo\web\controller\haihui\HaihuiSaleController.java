//package com.boyo.web.controller.haihui;
//
//import java.io.OutputStream;
//import java.net.HttpURLConnection;
//import java.net.URL;
//import java.nio.charset.StandardCharsets;
//import java.time.LocalDate;
//import java.time.format.DateTimeFormatter;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//import javax.servlet.http.HttpServletResponse;
//
//import cn.hutool.core.date.DateTime;
//import cn.hutool.core.date.DateUtil;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.boyo.common.core.controller.BaseController;
//import com.boyo.common.core.domain.AjaxResult;
//import com.boyo.common.core.page.TableDataInfo;
//import com.boyo.common.enums.BusinessType;
//import com.boyo.common.utils.poi.ExcelUtil;
//import com.boyo.master.domain.HaihuiSale;
//import com.boyo.master.service.IHaihuiSaleService;
//import com.boyo.web.lysso.controller.HttpRequest;
//import com.boyo.web.lysso.controller.HttpResponse;
//import com.google.gson.JsonObject;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.security.access.method.P;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.PutMapping;
//import org.springframework.web.bind.annotation.DeleteMapping;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
///**
// * 【请填写功能名称】Controller
// *
// * <AUTHOR>
// * @date 2024-10-24
// */
//@RestController
//@RequestMapping("/haihui/sale")
//public class HaihuiSaleController extends BaseController {
//    final String HAIHUI_URL = "http://pt.haihui.cn:8888/api/nmdxsservices/nmdxsservicesdate";
//    final String Authorization = "Basic bm1kdXNlcjpubWRwYXNzd29yZA==";
//
//
//    @Autowired
//    private IHaihuiSaleService haihuiSaleService;
//
//    @PostMapping("/test")
//    public AjaxResult getInfo(@RequestBody HaihuiSale haihuiSale) {
//        if (haihuiSale == null || haihuiSale.getStartdate() == null || haihuiSale.getEnddate() == null) {
//            return AjaxResult.error("参数错误");
//        }
//
//        JSONObject jsonObject = new JSONObject();
//
//        try {
//            // 从海汇获取数据
//            HttpRequest request1 = HttpRequest.builder()
//                    .setUrl(HAIHUI_URL)
//                    .setMethod(HttpRequest.Method.POST)
//                    .addHeader("Authorization", Authorization)
//                    .addHeader("Content-Type", "application/json; charset=UTF-8")
//                    .addHeader("Accept", "*/*")
//                    .addHeader("Connection", "keep-alive")
//                    .addPostData("StartDate", haihuiSale.getStartdate())
//                    .addPostData("EndDate", haihuiSale.getEnddate());
//            HttpResponse response = request1.post();
//
//            jsonObject = JSONObject.parseObject(response.getText());
//            saveSaleData(jsonObject);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return AjaxResult.success(jsonObject);
//
//    }
//
//    @Scheduled(cron = "30 30 * * * ?")
//    public void getSaleData() {
//
//        final String start = DateUtil.format(new Date(), "yyyy-MM-dd");
//        JSONObject jsonObject = new JSONObject();
//        try {
//            // 从海汇获取数据
//            HttpRequest request1 = HttpRequest.builder()
//                    .setUrl(HAIHUI_URL)
//                    .setMethod(HttpRequest.Method.POST)
//                    .addHeader("Authorization", Authorization)
//                    .addHeader("Content-Type", "application/json; charset=UTF-8")
//                    .addHeader("Accept", "*/*")
//                    .addHeader("Connection", "keep-alive")
//                    .addPostData("StartDate", start)
//                    .addPostData("EndDate", start);
//            HttpResponse response = request1.post();
//
//            jsonObject = JSONObject.parseObject(response.getText());
//            saveSaleData(jsonObject);
//            System.out.println(DateUtil.format(new Date(),  "yyyy-MM-dd HH:mm:ss")+jsonObject.toString());
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//    }
//
//
//    /**
//     * 保存销售数据
//     * 该方法解析JSON对象，提取销售相关信息，并将这些信息保存或更新在数据库中
//     * 如果服务器响应的结果为"ok"，则进一步处理数据，否则不进行任何操作
//     *
//     * @param jsonObject 包含销售数据的JSON对象
//     */
//    private void saveSaleData(JSONObject jsonObject) {
//        if (jsonObject.get("result").equals("ok")) {
//            JSONArray jsonArray = jsonObject.getJSONArray("data");
//            List<HaihuiSale> datasList = new ArrayList<>();
//
//            for (int i = 0; i < jsonArray.size(); i++) {
//                JSONObject jsonObject1 = jsonArray.getJSONObject(i);
//                HaihuiSale data = new HaihuiSale();
//                // 设置销售数据对象的各个字段
//                data.setHtNo(jsonObject1.getString("HTNO"));
//                data.setHtlxzt(jsonObject1.getString("HTLXZT"));
//                data.setCustName(jsonObject1.getString("CUSTNAME"));
//                data.setStartdate(jsonObject1.getString("STARTDATE"));
//                data.setEnddate(jsonObject1.getString("ENDDATE"));
//                data.setPayment(jsonObject1.getString("PAYMENT"));
//                data.setAmount(jsonObject1.getDouble("AMOUNT"));
//                data.setPayament(jsonObject1.getDouble("PAYAMOUNT"));
//                data.setNopayment(jsonObject1.getDouble("NOPAYAMOUNT"));
//                data.setSignatory(jsonObject1.getString("SIGNATORY"));
//                data.setState(jsonObject1.getString("STATE"));
//
//                datasList.add(data);
//            }
//
//            // 遍历销售数据列表，进行数据库的插入或更新操作
//            for (HaihuiSale data : datasList) {
//                String htNo = data.getHtNo();
//                HaihuiSale sale = haihuiSaleService.selectHaihuiSaleByHtNo(htNo);
//                // 判断数据是否已存在于数据库中
//                if (sale != null) {
//                    // 如果存在，则更新数据
//                    haihuiSaleService.updateHaihuiSale(data);
//                } else {
//                    data.setSaveTime(new Date());
//                    // 如果不存在，则插入数据，并检查插入是否成功
//                    final int i = haihuiSaleService.insertHaihuiSale(data);
//                    // 如果插入失败，记录错误日志
//                    if (i == 0) {
//                        logger.error("数据插入失败" + data);
//                    }
//                }
//            }
//        }
//    }
//
//
//    /**
//     * 查询【请填写功能名称】列表
//     */
//    @GetMapping("/list")
//    public TableDataInfo list(HaihuiSale haihuiSale) {
//        if(haihuiSale!=null){
//            if(haihuiSale.getStartdate()!=null){
//                final String startdate = haihuiSale.getStartdate();
//                final DateTime dateTime =DateUtil.parseDate(startdate);
//                final DateTime dateTime1 = DateUtil.beginOfDay(dateTime);
//                final String s = dateTime1.toString();
//                haihuiSale.setStartdate(s);
//            }
//            if(haihuiSale.getEnddate()!=null){
//                final String startdate = haihuiSale.getEnddate();
//                final DateTime dateTime =DateUtil.parseDate(startdate);
//                final DateTime dateTime1 = DateUtil.endOfDay(dateTime);
//                final String s = dateTime1.toString();
//                haihuiSale.setEnddate(s);
//            }
//        }
//        startPage();
//        List<HaihuiSale> list = haihuiSaleService.selectHaihuiSaleList(haihuiSale);
//        return getDataTable(list);
//    }
//
//    /**
//     * 获取【请填写功能名称】详细信息
//     */
//    @GetMapping(value = "/{htNo}")
//    public AjaxResult getInfo(@PathVariable("htNo") String htNo) {
//        return AjaxResult.success(haihuiSaleService.selectHaihuiSaleByHtNo(htNo));
//    }
//
//    /**
//     * 新增【请填写功能名称】
//     */
////    @PostMapping
////    public AjaxResult add(@RequestBody HaihuiSale haihuiSale) {
////        return toAjax(haihuiSaleService.insertHaihuiSale(haihuiSale));
////    }
//
//    /**
//     * 修改【请填写功能名称】
//     */
////    @PutMapping
////    public AjaxResult edit(@RequestBody HaihuiSale haihuiSale) {
////        return toAjax(haihuiSaleService.updateHaihuiSale(haihuiSale));
////    }
//
//    /**
//     * 删除【请填写功能名称】
//     */
////    @DeleteMapping("/{htNos}")
////    public AjaxResult remove(@PathVariable String[] htNos) {
////        return toAjax(haihuiSaleService.deleteHaihuiSaleByHtNos(htNos));
////    }
//}