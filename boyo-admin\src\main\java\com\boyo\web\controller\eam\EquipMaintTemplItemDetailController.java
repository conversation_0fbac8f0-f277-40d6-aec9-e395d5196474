package com.boyo.web.controller.eam;

import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.eam.domain.EquipMaintTemplItemDetail;
import com.boyo.eam.service.IEquipMaintTemplItemDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 设备-维保模板-维保明细(EquipMaintTemplItemDetail)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-10 11:07:32
 */
@Api("设备-维保模板-维保明细")
@RestController
@RequestMapping("/equip/equipMaintTemplItemDetail")
@AllArgsConstructor
public class EquipMaintTemplItemDetailController extends BaseController{
    /**
     * 服务对象
     */
    private final IEquipMaintTemplItemDetailService equipMaintTemplItemDetailService;

    /**
     * 查询设备-维保模板-维保明细列表
     *
     */
    @ApiOperation("查询设备-维保模板-维保明细列表")
    @GetMapping("/list")
    public TableDataInfo list(EquipMaintTemplItemDetail equipMaintTemplItemDetail) {
        startPage();
        List<EquipMaintTemplItemDetail> list = equipMaintTemplItemDetailService.selectEquipMaintTemplItemDetailList(equipMaintTemplItemDetail);
        return getDataTable(list);
    }
    
    /**
     * 获取设备-维保模板-维保明细详情
     */
    @ApiOperation("获取设备-维保模板-维保明细详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(equipMaintTemplItemDetailService.getById(id));
    }

    /**
     * 新增设备-维保模板-维保明细
     */
    @ApiOperation("新增设备-维保模板-维保明细")
    @PostMapping
    public AjaxResult add(@RequestBody EquipMaintTemplItemDetail equipMaintTemplItemDetail) {
        return toBooleanAjax(equipMaintTemplItemDetailService.save(equipMaintTemplItemDetail));
    }

    /**
     * 修改设备-维保模板-维保明细
     */
    @ApiOperation("修改设备-维保模板-维保明细")
    @PutMapping
    public AjaxResult edit(@RequestBody EquipMaintTemplItemDetail equipMaintTemplItemDetail) {
        return toBooleanAjax(equipMaintTemplItemDetailService.updateById(equipMaintTemplItemDetail));
    }

    /**
     * 删除设备-维保模板-维保明细
     */
    @ApiOperation("删除设备-维保模板-维保明细")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(equipMaintTemplItemDetailService.removeByIds(Arrays.asList(ids)));
    }

}
