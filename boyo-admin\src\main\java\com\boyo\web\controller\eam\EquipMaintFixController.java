package com.boyo.web.controller.eam;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.eam.domain.EquipLedger;
import com.boyo.eam.domain.EquipMaintFix;
import com.boyo.eam.service.IEquipLedgerService;
import com.boyo.eam.service.IEquipMaintFixService;
import com.boyo.eam.service.IEquipMaintRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 维修任务管理(EquipMaintFix)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-17 16:13:53
 */
@Api("维修任务管理")
@RestController
@RequestMapping("/equip/equipMaintFix")
@AllArgsConstructor
public class EquipMaintFixController extends BaseController{
    /**
     * 服务对象
     */
    private final IEquipMaintFixService equipMaintFixService;
    private final IEquipLedgerService equipLedgerService;
    private final IEquipMaintRecordService equipMaintRecordService;
    /**
     * 查询维修任务管理列表
     *
     */
    @ApiOperation("查询维修任务管理列表")
    @GetMapping("/list")
    public TableDataInfo list(EquipMaintFix equipMaintFix) {
        startPage();
        List<EquipMaintFix> list = equipMaintFixService.selectEquipMaintFixList(equipMaintFix);
        return getDataTable(list);
    }
    
    /**
     * 获取维修任务管理详情
     */
    @ApiOperation("获取维修任务管理详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        EquipMaintFix byId = equipMaintFixService.getById(id);
        String openid = byId.getEquipLedgerOpenid();
        if (openid!=null&&!"".equals(openid)){
            EquipLedger equipLedgers = equipLedgerService.getOne(
                    Wrappers.<EquipLedger>lambdaQuery()
                            .eq(EquipLedger::getOpenid, openid)
            );
            byId.setEquipName(equipLedgers.getName());
            byId.setEquipCode(equipLedgers.getCode());
        }
        return AjaxResult.success(byId);
    }

    /**
     * 新增维修任务管理
     */
    @ApiOperation("新增维修任务管理")
    @PostMapping
    public AjaxResult add(@RequestBody EquipMaintFix equipMaintFix) {
        equipMaintFix.setOpenid(super.generateOpenid());
        equipMaintFix.setCreateBy(SecurityUtils.getUsername());
        equipMaintFix.setCreateTime(new Date());
        equipMaintFix.setState("0");
        equipMaintFix.setFixCode(getNewPlanCode());
        equipMaintFix.setReportTime(new Date());
        return toBooleanAjax(equipMaintFixService.save(equipMaintFix));
    }

    /**
     * 修改维修任务管理
     */
    @ApiOperation("修改维修任务管理")
    @PutMapping
    public AjaxResult edit(@RequestBody EquipMaintFix equipMaintFix) {
        return toBooleanAjax(equipMaintFixService.updateById(equipMaintFix));
    }

    /**
     * 删除维修任务管理
     */
    @ApiOperation("删除维修任务管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(equipMaintFixService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 获取最新计划编号
     */
    private String getNewPlanCode(){

        String prefix = "EMRP" + DateUtil.format(new Date(),"yyyyMMdd");

        List<EquipMaintFix> equipMaintFixList = equipMaintFixService.list(
                Wrappers.<EquipMaintFix>lambdaQuery()
                        .like(EquipMaintFix::getFixCode,prefix)
        );
        int max = 0;
        if (equipMaintFixList!=null&&equipMaintFixList.size()>0){
            for (EquipMaintFix emf:equipMaintFixList){
                String fixCode = emf.getFixCode();
                int num = Integer.parseInt(fixCode.replace(prefix,""));
                if (num>max){
                    max = num;
                }
            }
        }
        return prefix+String.format("%02d", max+1);
    }


    // 移动端

    /**
     * 查询维修任务管理列表
     *
     */
    @ApiOperation("查询维修任务管理列表")
    @GetMapping("/pad/list")
    public TableDataInfo padList(EquipMaintFix equipMaintFix) {
        equipMaintFix.setSysUserId(Integer.parseInt(SecurityUtils.getLoginUser().getUser().getUserId().toString()));
//        equipMaintFix.setSysUserId(130);//测试
        startPage();
        List<EquipMaintFix> list = equipMaintFixService.selectEquipMaintFixList(equipMaintFix);
        return getDataTable(list);
    }

    @ApiOperation("点击开始或者结束")
    @Transactional
    @PutMapping("/pad/beginEnd")
    public AjaxResult beginEnd(String openid,String state) {
        EquipMaintFix equipMaintFix = equipMaintFixService.getOne(
                Wrappers.<EquipMaintFix>lambdaQuery()
                        .eq(EquipMaintFix::getOpenid, openid)
        );
        if ("0".equals(state)){
            equipMaintFix.setState("1");
            equipMaintRecordService.insertByTaskOpenid(openid);
        }else if ("1".equals(state)){
            equipMaintFix.setState("2");
            equipMaintRecordService.updateByTaskOpenid(openid);
        }
        return AjaxResult.success(equipMaintFixService.updateById(equipMaintFix));
    }
}
