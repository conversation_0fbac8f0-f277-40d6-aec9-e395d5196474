package com.boyo.web.controller.master;

import java.util.List;
import java.util.Arrays;

import com.boyo.master.service.IModelAllocationService;
import com.boyo.master.domain.ModelAllocation;
import com.boyo.master.vo.ModelAllocatonVO;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.enums.BusinessType;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
 * 主数据-货位管理Controller
 *
 * <AUTHOR>
 */
@Api("主数据-货位管理")
@RestController
@RequestMapping("/master/allocation")
@AllArgsConstructor
public class ModelAllocationController extends BaseController {
    private final IModelAllocationService modelAllocationService;

    /**
     * 查询主数据-货位管理列表
     */
    @ApiOperation("查询主数据-货位管理列表")
    @GetMapping("/list")
    public TableDataInfo list(ModelAllocation modelAllocation) {
        startPage();
        List<ModelAllocatonVO> list = modelAllocationService.selectModelAllocationList(modelAllocation);
        return getDataTable(list);
    }

    /**
     * 获取主数据-货位管理详细信息
     */
    @ApiOperation("获取主数据-货位管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(modelAllocationService.getById(id));
    }

    /**
     * 新增主数据-货位管理
     */
    @ApiOperation("新增主数据-货位管理")
    @PostMapping
    public AjaxResult add(@RequestBody ModelAllocation modelAllocation) {
        modelAllocation.setAllocationOpenid(super.generateOpenid());
        return toBooleanAjax(modelAllocationService.save(modelAllocation));
    }

    /**
     * 修改主数据-货位管理
     */
    @ApiOperation("修改主数据-货位管理")
    @PutMapping
    public AjaxResult edit(@RequestBody ModelAllocation modelAllocation) {
        return toBooleanAjax(modelAllocationService.updateById(modelAllocation));
    }

    /**
     * 删除主数据-货位管理
     */
    @ApiOperation("删除主数据-货位管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(modelAllocationService.removeByIds(Arrays.asList(ids)));
    }

}
