package com.boyo.master.utils;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class DateUtil2 {

    public static DateTimeFormatter YYYY_FORMATTER = DateTimeFormatter.ofPattern("yyyy");
    public static DateTimeFormatter YYYY_MM_DD_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static DateTimeFormatter YYYY_MM_DD_HH_MM_SS_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public static String getDateLate(Integer days) {
        return LocalDateTime.now().minusDays(days).format(YYYY_MM_DD_FORMATTER);
    }

    public static String getCurrentYear() {
        return LocalDateTime.now().format(YYYY_FORMATTER);
    }
}
