package com.boyo.web.controller.admin;


import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Arrays;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.core.domain.model.LoginUser;
import com.boyo.common.exception.CustomException;
import com.boyo.common.utils.ServletUtils;
import com.boyo.common.utils.TreeTableUtils;
import com.boyo.framework.web.service.TokenService;
import com.boyo.system.domain.SysEnterpriseAuthority;
import com.boyo.system.domain.TSysSystem;
import com.boyo.system.domain.vo.RouterVo;
import com.boyo.system.mapper.SysEnterpriseAuthorityMapper;
import com.boyo.system.mapper.TSysSystemMapper;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.boyo.system.domain.BoyoMenu;
import com.boyo.system.service.IBoyoMenuService;

/**
 * 菜单管理Controller
 *
 * <AUTHOR>
 */
@Api("菜单管理")
@RestController
@RequestMapping("/system/boyomenu")
@AllArgsConstructor
public class BoyoMenuController extends BaseController {
    private final IBoyoMenuService boyoMenuService;

    private final SysEnterpriseAuthorityMapper sysEnterpriseAuthorityMapper;

    private final TSysSystemMapper tSysSystemMapper;

    private TokenService tokenService;


    /**
     * 查询菜单管理列表
     */
    @ApiOperation("查询菜单管理列表")
    @GetMapping("/list")
    public TableDataInfo list(BoyoMenu boyoMenu) {
//        startPage();
        List<BoyoMenu> list = boyoMenuService.selectBoyoMenuList(boyoMenu);
        return getDataTable(list);
    }

    /**
     * 获取菜单管理详细信息
     */
    @ApiOperation("获取菜单管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(boyoMenuService.getById(id));
    }

    /**
     * 新增菜单管理
     */
    @ApiOperation("新增菜单管理")
    @PostMapping
    public AjaxResult add(@RequestBody BoyoMenu boyoMenu) {
        return toBooleanAjax(boyoMenuService.save(boyoMenu));
    }

    /**
     * 修改菜单管理
     */
    @ApiOperation("修改菜单管理")
    @PutMapping
    public AjaxResult edit(@RequestBody BoyoMenu boyoMenu) {
        return toBooleanAjax(boyoMenuService.updateById(boyoMenu));
    }

    /**
     * 删除菜单管理
     */
    @ApiOperation("删除菜单管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(boyoMenuService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 获取当前系统的可用菜单
     *
     * @param systemOpenid 系统openid
     * @return
     */
    @GetMapping("/listSystemMenu")
    public AjaxResult listSystemMenu(String systemOpenid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (ObjectUtil.isNotNull(loginUser.getEnterpriseUser())) {
            try {
                List<BoyoMenu> list = boyoMenuService.selectMenuTreeByUserId(systemOpenid, loginUser.getEnterpriseUser().getUserOpenid());
                List<RouterVo> result = boyoMenuService.buildMenus(list);
                result = boyoMenuService.addSystem(result);
                return AjaxResult.success(result);
            } catch (Exception e) {
                e.printStackTrace();
                throw new CustomException("当前用户暂无可用系统");
            }
        }
        return null;
    }

    @GetMapping("/listAllMenu")
    public AjaxResult listAllMenu() {
        List<BoyoMenu> list = null;
        JSONArray top = new JSONArray();

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (ObjectUtil.isNotNull(loginUser.getEnterpriseUser())) {
            SysEnterpriseAuthority sysEnterpriseAuthority = new SysEnterpriseAuthority();
            sysEnterpriseAuthority.setEnterpriseOpenid(loginUser.getEnterpriseUser().getEnterpriseOpenid());
            List<SysEnterpriseAuthority> enterpriseAuthorityList = sysEnterpriseAuthorityMapper.selectSysEnterpriseAuthorityList(sysEnterpriseAuthority);
            if (enterpriseAuthorityList != null && enterpriseAuthorityList.size() > 0) {
                List<String> systemIds = new ArrayList<>();
                for (SysEnterpriseAuthority obj : enterpriseAuthorityList) {
                    systemIds.add(obj.getSystemOpenid());
                }
                QueryWrapper<BoyoMenu> menuQueryWrapper = new QueryWrapper<>();
                menuQueryWrapper.in("system_openid",systemIds);
                list = boyoMenuService.list(menuQueryWrapper);
                list = TreeTableUtils.list2TreeList(list, "menuId", "parentId", "children");
                list.sort(Comparator.comparing(BoyoMenu::getOrderNum));
                List<TSysSystem> sysSystemList = tSysSystemMapper.findEnterpriseSystem(loginUser.getEnterpriseUser().getEnterpriseOpenid());
                if(sysSystemList != null && sysSystemList.size() > 0){
                    for (TSysSystem obj:sysSystemList) {
                        JSONObject object = new JSONObject();
                        object.put("menuId",obj.getSystemOpenid());
                        object.put("menuName",obj.getSystemName());
                        object.put("disableCheckbox",true);
                        for (BoyoMenu menu:list) {
                            if(obj.getSystemOpenid().equals(menu.getSystemOpenid())){
                                JSONArray temp = new JSONArray();
                                if(object.containsKey("children")){
                                    temp = object.getJSONArray("children");
                                }
                                temp.add(JSONObject.parseObject(JSONObject.toJSONString(menu)));
                                object.put("children",temp);
                            }
                        }
                        top.add(object);
                    }
                }
            }

        }

        return AjaxResult.success(top);
    }

}
