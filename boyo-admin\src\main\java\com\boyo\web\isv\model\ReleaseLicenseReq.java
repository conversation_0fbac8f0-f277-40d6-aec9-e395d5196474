package com.boyo.web.isv.model;

public class ReleaseLicenseReq {

    /**
     * 实例ID
     * 必返回
     */
    private String instanceId;

    /**
     * 新购商品时对应的订单ID。
     * 必返回
     */
    private String orderId;

    /**
     * 安全校验令牌。
     * 必返回
     */
    private String authToken;

    /**
     * 请求发起时的时间戳，取UTC时间。
     * <p>
     * 格式：yyyyMMddHHmmssSSS
     * 必返回
     */
    private String timeStamp;

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getAuthToken() {
        return authToken;
    }

    public void setAuthToken(String authToken) {
        this.authToken = authToken;
    }

    public String getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(String timeStamp) {
        this.timeStamp = timeStamp;
    }
}
