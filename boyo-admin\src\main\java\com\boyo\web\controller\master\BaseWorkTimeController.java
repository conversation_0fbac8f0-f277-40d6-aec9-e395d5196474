package com.boyo.web.controller.master;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.crm.entity.CrmContractProduct;
import com.boyo.master.domain.BaseDict;
import com.boyo.master.domain.BaseDictType;
import com.boyo.master.domain.WorkTime;
import com.boyo.master.service.IBaseDictService;
import com.boyo.master.service.IBaseWorkTimeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.sql.Wrapper;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Api("动态上班时间管理")
@RestController
@RequestMapping("/master/work/time")
@AllArgsConstructor
public class BaseWorkTimeController extends BaseController {

    private IBaseWorkTimeService workTimeService;
    private final IBaseDictService baseDictService;



    @ApiOperation("查询动态上班时间管理列表")
    @GetMapping("/list")
    public TableDataInfo list(WorkTime workTime) {
        startPage();
        List<WorkTime> list = workTimeService.list();
        return getDataTable(list);
    }


    @ApiOperation("修改")
    @PutMapping
    public AjaxResult edit (@RequestBody WorkTime workTime) {
        boolean ans = workTimeService.updateById(workTime);

//        WorkTime workTime2 = workTimeService.getById(workTime.getId());
//        QueryWrapper<BaseDict> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("base_code",workTime2.getBaseCode());
//        List<BaseDict> list = baseDictService.list(queryWrapper);
//        BaseDict baseDict = list.get(0);
//        baseDict.setBaseDesc(workTime2.getStartTime().substring(0,5));
//        baseDictService.updateById(baseDict);
        return AjaxResult.success(ans);
    }

}
