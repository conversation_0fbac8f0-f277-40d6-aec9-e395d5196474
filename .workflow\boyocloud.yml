# ========================================================
# Maven 构建参考流水线样例
# 功能：实现一个 Maven 命令行工程初始化并构建
# ========================================================
name: gitee-go-maven-example               # 定义一个唯一 ID 标识为 gitee-go-maven-example，名称为 “Maven-流水线示例” 的流水线
displayName: 'Maven-流水线示例'
triggers:                                  # 流水线触发器配置
  push:                                    # 设置 master 分支 在产生代码 push 时精确触发（PRECISE）构建
    - matchType: PRECISE
      branch: master
commitMessage: ''                          # 通过匹配当前提交的 CommitMessage 决定是否执行流水线
stages:                                    # 构建阶段配置
  - stage:                                 # 定义一个 ID 标识为 maven-build-stage ,名为 “Maven Stage” 的阶段
      name: maven-build-stage
      displayName: 'Maven Stage'
      failFast: false                      # 允许快速失败，即当 Stage 中有任务失败时，直接结束整个 Stage
      steps:                               # 构建步骤配置
        - step: mavenbuild@1               # 采用 Maven 编译环境
          name: maven-build                # 定义一个 ID 标识为 maven-build ,名为 “Maven Step” 的阶段
          displayName: 'Maven Step'
          inputs:                          # 构建输入参数设定
            jdkVersion: 8                  # 指定 JDK 环境版本为 1.8
            mavenVersion: 3.6              # 指定 Maven 环境版本为 3.6
            goals: |                       # 使用 Maven 命令初始化、构建一个 Maven 工程并执行输出内容
              mvn archetype:generate -DarchetypeGroupId=org.apache.maven.archetypes -DinteractiveMode=false -DarchetypeCatalog=internal -DgroupId=com.gitee.go.maven -DartifactId=helloworld -T20
              cd helloworld 
              mvn clean
              mvn compile
              mvn test-compile
              mvn package
              java -cp boyo-admin/target/boyo-admin.jar com.gitee.go.maven.App