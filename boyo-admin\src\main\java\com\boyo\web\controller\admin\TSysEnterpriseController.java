package com.boyo.web.controller.admin;

import java.util.List;
import java.util.Arrays;

import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.boyo.system.domain.TSysEnterprise;
import com.boyo.system.service.ITSysEnterpriseService;

/**
 * 企业管理Controller
 *
 * <AUTHOR>
 */
@Api("企业管理")
@RestController
@RequestMapping("/boyo/enterprise")
public class TSysEnterpriseController extends BaseController {
    @Autowired
    private ITSysEnterpriseService tSysEnterpriseService;

    /**
     * 查询企业管理列表
     */
    @ApiOperation("查询企业管理列表")
    @GetMapping("/list")
    public TableDataInfo list(TSysEnterprise tSysEnterprise) {
        startPage();
        List<TSysEnterprise> list = tSysEnterpriseService.selectTSysEnterpriseList(tSysEnterprise);
        return getDataTable(list);
    }

    /**
     * 获取企业管理详细信息
     */
    @ApiOperation("获取企业管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(tSysEnterpriseService.getById(id));
    }

    /**
     * 新增企业管理
     */
    @ApiOperation("新增企业管理")
    @PostMapping
    public AjaxResult add(@RequestBody TSysEnterprise tSysEnterprise) {
        if(tSysEnterpriseService.checkExist(tSysEnterprise)){
            return AjaxResult.error("企业名称或企业简称已存在");
        }
        tSysEnterprise.setEnterpriseOpenid(super.generateOpenid());
        return toBooleanAjax(tSysEnterpriseService.save(tSysEnterprise));
    }

    /**
     * 修改企业管理
     */
    @ApiOperation("修改企业管理")
    @PutMapping
    public AjaxResult edit(@RequestBody TSysEnterprise tSysEnterprise) {
        if(tSysEnterpriseService.checkExist(tSysEnterprise)){
            return AjaxResult.error("企业名称或企业简称已存在");
        }
        return toBooleanAjax(tSysEnterpriseService.updateById(tSysEnterprise));
    }

    /**
     * 删除企业管理
     */
    @ApiOperation("删除企业管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(tSysEnterpriseService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 初始化租户数据库
     * @param openid 企业openid
     * @return
     */
    @ApiOperation("初始化租户数据库")
    @PostMapping(path = "/initDataBase")
    public AjaxResult initDataBase(String openid){
        tSysEnterpriseService.initDatabase(openid);
        return AjaxResult.success();
    }

    @ApiOperation("获取企业可视化看板地址")
    @GetMapping("/getEnterpriseScreen")
    public AjaxResult getEnterpriseScreen(String openid){
        return AjaxResult.success(tSysEnterpriseService.getEnterpriseScreen(openid));
    }
}
