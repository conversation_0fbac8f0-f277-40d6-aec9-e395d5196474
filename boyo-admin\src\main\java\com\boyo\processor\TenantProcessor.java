package com.boyo.processor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.creator.DataSourceCreator;
import com.baomidou.dynamic.datasource.processor.DsProcessor;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DataSourceProperty;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boyo.system.domain.TSysEnterprise;
import com.boyo.system.service.ITSysEnterpriseService;
import lombok.RequiredArgsConstructor;
import org.aopalliance.intercept.MethodInvocation;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.sql.DataSource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class TenantProcessor extends DsProcessor {

    /**
     * 租户-数据源 MAP
     */
    public static Map<String, DataSource> TENANT_DATASOURCE = new ConcurrentHashMap<>();

    private static final String HEADER_PREFIX = "#header";

    private final DataSource dataSource;

    private final DataSourceCreator dataSourceCreator;

    private final ITSysEnterpriseService sysEnterpriseService;

    @Override
    public boolean matches(String key) {
        return key.startsWith(HEADER_PREFIX);
    }

    public boolean checkDataSource(String tenantOpenid){
        if (!TENANT_DATASOURCE.containsKey(tenantOpenid)) {
            QueryWrapper<TSysEnterprise> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("enterprise_openid", tenantOpenid);
            TSysEnterprise tenant = sysEnterpriseService.getOne(queryWrapper);
            if (tenant == null) {
                return false;
            }
            if (StrUtil.hasBlank(
                    tenant.getEnterpriseDatabaseDriver(),
                    tenant.getEnterpriseDatabaseUrl(),
                    tenant.getEnterpriseDatabaseUsername(),
                    tenant.getEnterpriseDatabasePassword()
            )) {
                return false;
//                throw new ServiceException("企业租户数据库未初始化完成");
            }
            DynamicRoutingDataSource ds = (DynamicRoutingDataSource) dataSource;
            DataSourceProperty property = new DataSourceProperty();
            property.setUrl(tenant.getEnterpriseDatabaseUrl());
            property.setUsername(tenant.getEnterpriseDatabaseUsername());
            property.setPassword(tenant.getEnterpriseDatabasePassword());
            property.setDriverClassName(tenant.getEnterpriseDatabaseDriver());
            DataSource dataSource = dataSourceCreator.createDataSource(property);
            ds.addDataSource(tenant.getEnterpriseOpenid(), dataSource);
            TENANT_DATASOURCE.put(tenantOpenid, dataSource);
        }
        return true;
    }

    @Override
    public String doDetermineDatasource(MethodInvocation invocation, String key) {
        if(ObjectUtil.isNull(RequestContextHolder.getRequestAttributes())){
            return DynamicDataSourceContextHolder.peek();
        }
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String tenantOpenid = request.getHeader(key.substring(8));
        if (!TENANT_DATASOURCE.containsKey(tenantOpenid)) {
            QueryWrapper<TSysEnterprise> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("enterprise_openid", tenantOpenid);
            TSysEnterprise tenant = sysEnterpriseService.getOne(queryWrapper);
            if (tenant == null) {
                return null;
            }
            if (StrUtil.hasBlank(
                    tenant.getEnterpriseDatabaseDriver(),
                    tenant.getEnterpriseDatabaseUrl(),
                    tenant.getEnterpriseDatabaseUsername(),
                    tenant.getEnterpriseDatabasePassword()
            )) {
                return null;
//                throw new ServiceException("企业租户数据库未初始化完成");
            }
            DynamicRoutingDataSource ds = (DynamicRoutingDataSource) dataSource;
            DataSourceProperty property = new DataSourceProperty();
            property.setUrl(tenant.getEnterpriseDatabaseUrl());
            property.setUsername(tenant.getEnterpriseDatabaseUsername());
            property.setPassword(tenant.getEnterpriseDatabasePassword());
            property.setDriverClassName(tenant.getEnterpriseDatabaseDriver());
            DataSource dataSource = dataSourceCreator.createDataSource(property);
            ds.addDataSource(tenant.getEnterpriseOpenid(), dataSource);
            TENANT_DATASOURCE.put(tenantOpenid, dataSource);
        }
        return tenantOpenid;
    }
}


