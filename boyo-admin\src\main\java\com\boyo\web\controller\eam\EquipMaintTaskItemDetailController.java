package com.boyo.web.controller.eam;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.eam.domain.EquipMaintTaskItemDetail;
import com.boyo.eam.domain.Media;
import com.boyo.eam.service.IEquipMaintTaskItemDetailService;
import com.boyo.eam.service.IMediaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 维保任务管理-维保项目-明细(EquipMaintTaskItemDetail)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-15 09:18:33
 */
@Api("维保任务管理-维保项目-明细")
@RestController
@RequestMapping("/equip/equipMaintTaskItemDetail")
@AllArgsConstructor
public class EquipMaintTaskItemDetailController extends BaseController{
    /**
     * 服务对象
     */
    private final IEquipMaintTaskItemDetailService equipMaintTaskItemDetailService;
    private final IMediaService mediaService;

    /**
     * 查询维保任务管理-维保项目-明细列表
     *
     */
    @ApiOperation("查询维保任务管理-维保项目-明细列表")
    @GetMapping("/list")
    public TableDataInfo list(EquipMaintTaskItemDetail equipMaintTaskItemDetail) {
        startPage();
        List<EquipMaintTaskItemDetail> list = equipMaintTaskItemDetailService.selectEquipMaintTaskItemDetailList(equipMaintTaskItemDetail);
        return getDataTable(list);
    }
    
    /**
     * 获取维保任务管理-维保项目-明细详情
     */
    @ApiOperation("获取维保任务管理-维保项目-明细详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(equipMaintTaskItemDetailService.getById(id));
    }

    /**
     * 新增维保任务管理-维保项目-明细
     */
    @ApiOperation("新增维保任务管理-维保项目-明细")
    @PostMapping
    public AjaxResult add(@RequestBody EquipMaintTaskItemDetail equipMaintTaskItemDetail) {
        return toBooleanAjax(equipMaintTaskItemDetailService.save(equipMaintTaskItemDetail));
    }

    /**
     * 修改维保任务管理-维保项目-明细
     */
    @ApiOperation("修改维保任务管理-维保项目-明细")
    @PutMapping
    public AjaxResult edit(@RequestBody EquipMaintTaskItemDetail equipMaintTaskItemDetail) {
        return toBooleanAjax(equipMaintTaskItemDetailService.updateById(equipMaintTaskItemDetail));
    }

    /**
     * 删除维保任务管理-维保项目-明细
     */
    @ApiOperation("删除维保任务管理-维保项目-明细")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(equipMaintTaskItemDetailService.removeByIds(Arrays.asList(ids)));
    }


    // 移动端接口

    /**
     * 查询维保任务管理-维保项目-明细列表
     *
     */
    @ApiOperation("查询维保任务管理-维保项目-明细列表")
    @GetMapping("/pad/list")
    public TableDataInfo padList(EquipMaintTaskItemDetail equipMaintTaskItemDetail) {
        startPage();
        List<EquipMaintTaskItemDetail> list = equipMaintTaskItemDetailService.selectEquipMaintTaskItemDetailList(equipMaintTaskItemDetail);
        return getDataTable(list);
    }

    /**
     * 获取维保任务管理-维保项目-明细详情
     */
    @ApiOperation("获取维保任务管理-维保项目-明细详情")
    @GetMapping(value = "/pad/{id}")
    public AjaxResult padGetInfo(@PathVariable("id") Integer id) {
        EquipMaintTaskItemDetail detail = equipMaintTaskItemDetailService.getDetailAndRecord(id);
        if (detail!=null){
            // 通过记录中的图片id查询图片地址
            String mediaIds = detail.getMediaIds();
            if (mediaIds!=null && !"".equals(mediaIds)){
                List<String> mediaUrls = new ArrayList<>();
                String[] mediaIdArray = mediaIds.split(",");
                List<Media> mediaList = mediaService.list(
                    Wrappers.<Media>lambdaQuery()
                        .in(Media::getId, mediaIdArray)
                );
                for (Media media:mediaList){
                    mediaUrls.add(media.getUrl());
                }
                detail.setMediaUrls(mediaUrls);
            }
            return AjaxResult.success(detail);
        }
        return AjaxResult.error("未找到id");
    }
}
