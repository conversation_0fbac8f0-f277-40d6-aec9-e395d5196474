package com.boyo.eam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boyo.eam.domain.EquipLedger;

import java.util.List;

/**
 * 设备台账(EquipLedger)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:02
 */
public interface IEquipLedgerService extends IService<EquipLedger> {

    /**
     * 查询多条数据
     *
     * @param equipLedger 对象信息
     * @return 对象列表
     */
    List<EquipLedger> selectEquipLedgerList(EquipLedger equipLedger);

    /**
     * 获取详情
     * @param id
     * @return
     */
    EquipLedger getInfo(Integer id);

    int insertEquipLedger(EquipLedger equipLedger);
}
