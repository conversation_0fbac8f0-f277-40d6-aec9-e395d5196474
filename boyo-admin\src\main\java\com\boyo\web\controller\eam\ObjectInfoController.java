package com.boyo.web.controller.eam;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.eam.domain.ObjectInfo;
import com.boyo.eam.domain.ObjectInfoProperty;
import com.boyo.eam.service.IObjectInfoPropertyService;
import com.boyo.eam.service.IObjectInfoService;
import com.boyo.framework.annotation.Tenant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 物模型表(ObjectInfo)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-02 18:08:12
 */
@Api("物模型表")
@RestController
@RequestMapping("/equip/objectInfo")
@AllArgsConstructor
@Tenant
public class ObjectInfoController extends BaseController{
    /**
     * 服务对象
     */
    private final IObjectInfoService objectInfoService;
    private final IObjectInfoPropertyService objectInfoPropertyService;

    /**
     * 查询物模型表列表
     *
     */
    @ApiOperation("查询物模型表列表")
    @GetMapping("/list")
    public TableDataInfo list(ObjectInfo objectInfo) {
        startPage();
        List<ObjectInfo> list = objectInfoService.selectObjectInfoList(objectInfo);
        return getDataTable(list);
    }
    
    /**
     * 获取物模型表详情
     */
    @ApiOperation("获取物模型表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        ObjectInfo byId = objectInfoService.getById(id);
        List<ObjectInfoProperty> list = objectInfoPropertyService.list(
                Wrappers.<ObjectInfoProperty>lambdaQuery()
                        .eq(ObjectInfoProperty::getModelOpenid, byId.getOpenid())
        );
        byId.setProperties(list);
        return AjaxResult.success(byId);
    }

    /**
     * 新增物模型表
     */
    @ApiOperation("新增物模型表")
    @Transactional
    @PostMapping
    public AjaxResult add(@RequestBody ObjectInfo objectInfo) {
        String objectInfoOpenid = super.generateOpenid();
        objectInfo.setOpenid(objectInfoOpenid);
        List<ObjectInfoProperty> properties = objectInfo.getProperties();
        for (ObjectInfoProperty o:properties){
            o.setModelOpenid(objectInfoOpenid);
            if (o.getOpenid()==null||"".equals(o.getOpenid())){
                o.setOpenid(super.generateOpenid());
            }
        }
        objectInfoPropertyService.saveOrUpdateBatch(properties);
        return toBooleanAjax(objectInfoService.save(objectInfo));
    }

    /**
     * 修改物模型表
     */
    @ApiOperation("修改物模型表")
    @Transactional
    @PutMapping
    public AjaxResult edit(@RequestBody ObjectInfo objectInfo) {
        List<ObjectInfoProperty> properties = objectInfo.getProperties();
        for (ObjectInfoProperty o:properties){
            if (o.getModelOpenid()==null||"".equals(o.getModelOpenid())){
                o.setModelOpenid(objectInfo.getOpenid());
            }
            if (o.getOpenid()==null||"".equals(o.getOpenid())){
                o.setOpenid(super.generateOpenid());
            }
        }
        objectInfoPropertyService.remove(
            Wrappers.<ObjectInfoProperty>lambdaQuery()
                .eq(ObjectInfoProperty::getModelOpenid,objectInfo.getOpenid())
        );
        objectInfoPropertyService.saveBatch(properties);
        return toBooleanAjax(objectInfoService.updateById(objectInfo));
    }

    /**
     * 删除物模型表
     */
    @ApiOperation("删除物模型表")
    @Transactional
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        List<ObjectInfo> objectInfoList = objectInfoService.list(
                Wrappers.<ObjectInfo>lambdaQuery()
                        .in(ObjectInfo::getId, ids)
        );
        ArrayList<String> openids = new ArrayList<>();
        for (ObjectInfo o:objectInfoList){
            openids.add(o.getOpenid());
        }
        objectInfoPropertyService.remove(
                Wrappers.<ObjectInfoProperty>lambdaQuery()
                        .in(ObjectInfoProperty::getModelOpenid, openids)
        );
        return toBooleanAjax(objectInfoService.removeByIds(Arrays.asList(ids)));
    }

}
