package com.boyo.web.controller.master;

import java.util.List;
import java.util.Arrays;

import com.boyo.master.vo.ModelWarehouseVO;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.boyo.master.domain.ModelWarehouse;
import com.boyo.master.service.IModelWarehouseService;

/**
 * 主数据-仓库管理Controller
 *
 * <AUTHOR>
 */
@Api("主数据-仓库管理")
@RestController
@RequestMapping("/master/warehouse")
@AllArgsConstructor
public class ModelWarehouseController extends BaseController {

    private final IModelWarehouseService modelWarehouseService;

    /**
     * 查询主数据-仓库管理列表
     */
    @ApiOperation("查询主数据-仓库管理列表")
    @GetMapping("/list")
    public TableDataInfo list(ModelWarehouse modelWarehouse) {
        startPage();
        List<ModelWarehouseVO> list = modelWarehouseService.selectModelWarehouseList(modelWarehouse);
        return getDataTable(list);
    }

    /**
     * 获取主数据-仓库管理详细信息
     */
    @ApiOperation("获取主数据-仓库管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(modelWarehouseService.getById(id));
    }

    /**
     * 新增主数据-仓库管理
     */
    @ApiOperation("新增主数据-仓库管理")
    @PostMapping
    public AjaxResult add(@RequestBody ModelWarehouse modelWarehouse) {
        return toBooleanAjax(modelWarehouseService.save(modelWarehouse));
    }

    /**
     * 修改主数据-仓库管理
     */
    @ApiOperation("修改主数据-仓库管理")
    @PutMapping
    public AjaxResult edit(@RequestBody ModelWarehouse modelWarehouse) {
        return toBooleanAjax(modelWarehouseService.updateById(modelWarehouse));
    }

    /**
     * 删除主数据-仓库管理
     */
    @ApiOperation("删除主数据-仓库管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(modelWarehouseService.removeByIds(Arrays.asList(ids)));
    }
}
