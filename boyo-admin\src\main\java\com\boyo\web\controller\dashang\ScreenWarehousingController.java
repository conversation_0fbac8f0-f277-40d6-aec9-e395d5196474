package com.boyo.web.controller.dashang;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.enums.BusinessType;
import com.boyo.common.utils.poi.ExcelUtil;
import com.boyo.master.domain.ScreenWarehousing;
import com.boyo.master.domain.ScreenWorkshop;
import com.boyo.master.service.IScreenWarehousingService;
import com.boyo.master.service.IScreenWorkshopService;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 大屏入库信息Controller
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
@RestController
@RequestMapping("/dashang/warehousing")
public class ScreenWarehousingController extends BaseController {
    @Autowired
    private IScreenWarehousingService screenWarehousingService;

    @Autowired
    IScreenWorkshopService screenWorkshopService;

    /**
     * 查询大屏入库信息列表
     * 此方法用于根据时间范围查询大屏入库信息的列表
     * 它首先确定查询的起始和结束时间，然后根据这些时间获取相关的入库信息
     */
    @GetMapping("/list")
    public TableDataInfo list(ScreenWarehousing screenWarehousing) {

        // 定义时间范围的起始和结束字符串
        String start;
        String end;
        // 获取从请求参数中传入的入库时间，如果未指定，则默认为当前日期
        Date storageTime = screenWarehousing.getStorageTime();
        if (storageTime == null) {
            screenWarehousing.setStorageTime(new Date());
            storageTime = new Date();
        }

        // 根据入库时间确定查询的起始和结束时间，默认为当月的开始和结束时间
        start = DateUtil.format(DateUtil.beginOfMonth(storageTime), "yyyy-MM-dd");
        end = DateUtil.format(DateUtil.endOfMonth(storageTime), "yyyy-MM-dd");
        // 如果起始时间与本月的起始时间相同，则将结束时间设为当前日期，以确保查询从月初至今的数据
        if (start.equals(DateUtil.format(DateUtil.beginOfMonth(new Date()), "yyyy-MM-dd"))) {
            end = DateUtil.format(new Date(), "yyyy-MM-dd");
        }

        // 计算起始和结束时间之间的天数差
        final long between = DateUtil.between(DateUtil.parseDate(start), DateUtil.parseDate(end), DateUnit.DAY);
        // 初始化一个列表，用于存储每一天的默认入库信息（初始化为0）
        List<ScreenWarehousing> listAns = new ArrayList<>();
        for (int i = 0; i <= between; i++) {
            ScreenWarehousing item = new ScreenWarehousing();
            item.setStorageTime(DateUtil.offsetDay(DateUtil.parseDate(start), i));
            item.setWarehousing(0L);
            listAns.add(item);
        }

        // 从数据库中查询符合条件的入库信息列表
        List<ScreenWarehousing> list = screenWarehousingService.selectScreenWarehousingBetween(screenWarehousing, start, end);
        // 将查询到的入库信息替换到之前初始化的列表中，确保时间顺序正确
        list.forEach(item -> {
            long between1 = DateUtil.between(DateUtil.parseDate(start), item.getStorageTime(), DateUnit.DAY);
            listAns.set((int) between1, item);
        });

        // 返回处理后的数据表信息
        return getDataTable(listAns);
    }


    /**
     * 导出大屏入库信息列表
     */
//    @PreAuthorize("@ss.hasPermi('system:warehousing:export')")
//    @Log(title = "大屏入库信息", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, ScreenWarehousing screenWarehousing)
//    {
//        List<ScreenWarehousing> list = screenWarehousingService.selectScreenWarehousingList(screenWarehousing);
//        ExcelUtil<ScreenWarehousing> util = new ExcelUtil<ScreenWarehousing>(ScreenWarehousing.class);
//        util.exportExcel(response, list, "大屏入库信息数据");
//    }

    /**
     * 获取大屏入库信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:warehousing:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(screenWarehousingService.selectScreenWarehousingById(id));
    }

    /**
     * 新增大屏入库信息
     */

//    @PostMapping
//    public AjaxResult add(@RequestBody ScreenWarehousing screenWarehousing) {
//        return toAjax(screenWarehousingService.insertScreenWarehousing(screenWarehousing));
//    }

    /**
     * 修改大屏入库信息
     */

    @PutMapping
    public AjaxResult edit(@RequestBody ScreenWarehousing screenWarehousing) {
        if (screenWarehousing.getStorageTime() == null) {
            return AjaxResult.error("入库时间不能为空");
        }
        ScreenWarehousing temp = new ScreenWarehousing();
        temp.setStorageTime(screenWarehousing.getStorageTime());
        List<ScreenWarehousing> list =
                screenWarehousingService.selectScreenWarehousingList(screenWarehousing);

        if (list.size() == 0) {
            return toAjax(screenWarehousingService.insertScreenWarehousing(screenWarehousing));
        } else {
            return toAjax(screenWarehousingService.updateScreenWarehousing(screenWarehousing));
        }
    }

    /**
     * 删除大屏入库信息
     */

    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(screenWarehousingService.deleteScreenWarehousingByIds(ids));
    }
}