package com.boyo.web.controller.master;

import java.util.List;
import java.util.Arrays;

import cn.hutool.core.util.IdUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.boyo.master.domain.TModelFactory;
import com.boyo.master.service.ITModelFactoryService;

/**
 * 主数据-工厂管理Controller
 *
 * <AUTHOR>
 */
@Api("主数据-工厂管理")
@RestController
@RequestMapping("/master/factory")
@AllArgsConstructor
public class ModelFactoryController extends BaseController {
    private final ITModelFactoryService tModelFactoryService;

    /**
     * 查询主数据-工厂管理列表
     */
    @ApiOperation("查询主数据-工厂管理列表")
    @GetMapping("/list")
    public TableDataInfo list(TModelFactory tModelFactory) {
        startPage();
        List<TModelFactory> list = tModelFactoryService.selectTModelFactoryList(tModelFactory);
        return getDataTable(list);
    }

    /**
     * 获取主数据-工厂管理详细信息
     */
    @ApiOperation("获取主数据-工厂管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(tModelFactoryService.getById(id));
    }

    /**
     * 新增主数据-工厂管理
     */
    @ApiOperation("新增主数据-工厂管理")
    @PostMapping
    public AjaxResult add(@RequestBody TModelFactory tModelFactory) {
        if(tModelFactoryService.checkExist(tModelFactory)){
            return AjaxResult.error("工厂编码已存在！");
        }
        tModelFactory.setFactoryOpenid(IdUtil.fastSimpleUUID());
        return toBooleanAjax(tModelFactoryService.save(tModelFactory));
    }

    /**
     * 修改主数据-工厂管理
     */
    @ApiOperation("修改主数据-工厂管理")
    @PutMapping
    public AjaxResult edit(@RequestBody TModelFactory tModelFactory) {
        if(tModelFactoryService.checkExist(tModelFactory)){
            return AjaxResult.error("工厂编码已存在！");
        }
        return toBooleanAjax(tModelFactoryService.updateById(tModelFactory));
    }

    /**
     * 删除主数据-工厂管理
     */
    @ApiOperation("删除主数据-工厂管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(tModelFactoryService.removeByIds(Arrays.asList(ids)));
    }
}
