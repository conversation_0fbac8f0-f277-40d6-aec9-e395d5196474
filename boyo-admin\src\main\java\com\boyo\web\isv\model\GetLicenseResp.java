package com.boyo.web.isv.model;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 获取License场景生产接口相应体
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetLicenseResp extends BaseResp {

    /**
     * 实例ID
     * <p>
     * 服务商提供的唯一标识，后续云市场与ISV相互交互的唯一标识
     */
    private String instanceId;

    /**
     * license
     * <p>
     * 服务商根据saasExtendParams的license识别码(identificationCode)返回给买家的license
     */
    private String license;

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getLicense() {
        return license;
    }

    public void setLicense(String license) {
        this.license = license;
    }
}