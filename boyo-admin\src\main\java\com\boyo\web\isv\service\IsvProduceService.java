package com.boyo.web.isv.service;

import com.boyo.web.isv.model.*;

public interface IsvProduceService {
    /**
     * 获取License场景服务商需要实现的业务逻辑，包含下面步骤：
     * 1；基于getLicenseReq请求参数对象做业务校验
     * 2：处理具体业务逻辑，生成资源
     *
     * @param getLicenseReq  云商店请求的数据对象
     * @return 资源生成结果
     */
    GetLicenseInfo getLicense(GetLicenseReq getLicenseReq);

    /**
     * 续费场景服务商需要实现的业务逻辑，包含下面步骤：
     * 基于refreshInstanceReq请求参数做业务校验
     * 处理过期具体业务逻辑
     *
     * @param refreshLicenseReq 云商店请求的数据对象
     * @return 业务处理结果，true:成功;false:失败
     */
    boolean refreshLicense(RefreshLicenseReq refreshLicenseReq);

    /**
     * 资源过期场景服务商需要实现的业务逻辑，包含下面步骤：
     * 基于expireLicenseReq请求参数做业务校验
     * 处理过期具体业务逻辑
     *
     * @param expireLicenseReq 云市场请求的数据对象
     * @return 业务处理结果，true:成功;false:失败
     */
    boolean expireLicense(ExpireLicenseReq expireLicenseReq);

    /**
     * 资源释放场景服务商需要实现的业务逻辑，包含下面步骤：
     * 基于releaseLicenseReq请求参数做业务校验
     * 处理过期具体业务逻辑
     *
     * @param releaseLicenseReq 云市场请求的数据对象
     * @return 业务处理结果，true:成功;false:失败
     */
    boolean releaseLicense(ReleaseLicenseReq releaseLicenseReq);
}
