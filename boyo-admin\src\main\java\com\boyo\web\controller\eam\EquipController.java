package com.boyo.web.controller.eam;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.eam.domain.EquipInspectionSpot;
import com.boyo.eam.domain.EquipMaintTask;
import com.boyo.eam.service.IEquipInspectionSpotService;
import com.boyo.eam.service.IEquipMaintTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 设备管理通用接口
 *
 * <AUTHOR>
 * @since 2021-11-25 15:09:32
 */
@Api("设备管理通用接口")
@RestController
@RequestMapping("/equip")
@AllArgsConstructor
public class EquipController extends BaseController {

    private final IEquipMaintTaskService equipMaintTaskService;
    private final IEquipInspectionSpotService equipInspectionSpotService;

    @ApiOperation("查询故障处理、维修、保养、点巡检、运行监控的任务数量")
    @GetMapping("/pad/total")
    public AjaxResult getCount(){
        Map<String, Integer> result = new HashMap<>();
        result.put("fault",null);//故障处理
        result.put("fix",null);//维修
        result.put("maintain",equipMaintTaskService.list(
                Wrappers.<EquipMaintTask>lambdaQuery()
                .ne(EquipMaintTask::getState,2)
                .eq(EquipMaintTask::getSysUserId, SecurityUtils.getLoginUser().getUser().getUserId())
        ).size());//保养
        result.put("spot",equipInspectionSpotService.list(
                Wrappers.<EquipInspectionSpot>lambdaQuery()
                .ne(EquipInspectionSpot::getState,2)
                .eq(EquipInspectionSpot::getSysUserId, SecurityUtils.getLoginUser().getUser().getUserId())
        ).size());//点巡检
        result.put("monitor",null);//运行监控
        return AjaxResult.success(result);
    }


}
