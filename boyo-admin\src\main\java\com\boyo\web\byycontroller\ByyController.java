package com.boyo.web.byycontroller;

import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.SM2;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.*;

@RestController
@RequestMapping("/iot/byy")
public class ByyController extends BaseController {

    @GetMapping("/list")
    public AjaxResult getCurrent(String userCode,String deviceCodes) throws Exception {
        RestTemplate restTemplate = new RestTemplate();
        String time =  String.valueOf(System.currentTimeMillis());
        if(deviceCodes==null){
            throw new Exception("用户名为空");
        }
        //String userCode = "S653F73BDE4B09F5F56376109";
        //String deviceCodes = "E653C7C72E4B09F5F862326D4,E653C86E4E4B09F5F7784FE1A,E653CAA08E4B09F5F7784FE1F,E653CAD9AE4B09F5F7784FE23,E653CB486E4B09F5F7784FE2A,E653CC8D0E4B09F5F7784FE2E,E653CCEA1E4B09F5FE0EC7EA7,E653CD4C5E4B09F5FE0EC7EA8";
        String message = "deviceCodes="+deviceCodes+"&time="+time+"&userCode="+userCode;
        Map<String, String> map = new HashMap<>();
        map.put("time", time);
        map.put("userCode", userCode);
        map.put("deviceCodes", deviceCodes);
        map.put("sign", sign(message));

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type","application/json;charset=UTF-8");
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String,String>> r = new HttpEntity<>(map, headers);
        String url = "https://middleground-api.ningmengdou.com/iot/openapi/getCurrent";
        ResponseEntity<JSONObject> response = restTemplate.postForEntity(url,r,JSONObject.class);
        JSONArray dataList = null;
        JSONObject jsonObject = response.getBody();
        Date now = new Date();
        Date now_10 = new Date(now.getTime() - 600000); //10分钟前的时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//可以方便地修改日期格式
        String nowTime_10 = dateFormat.format(now_10);
        if ((jsonObject.getInt("code") == 200)) {
            dataList = jsonObject.getJSONArray("data");
//            for(int i =0 ; i<dataList.size();i++) {
//                if (dataList.getJSONObject(i).get("tag").equals("Work_Status")) {
//                    if (dataList.getJSONObject(i).get("updateTime").toString().compareTo(nowTime_10) < 0) {
//                        dataList.getJSONObject(i).set("val",0);
//                    }
//                }
//            }
        }
        return AjaxResult.success(dataList);
    }

    @GetMapping("/getInfo")
    public AjaxResult getCurrent1(String userCode,String deviceCodes) throws Exception {
        RestTemplate restTemplate = new RestTemplate();
        String time =  String.valueOf(System.currentTimeMillis());
        String message = "deviceCodes="+deviceCodes+"&time="+time+"&userCode="+userCode;
        Map<String, String> map = new HashMap<>();
        map.put("time", time);
        map.put("userCode", userCode);
        map.put("deviceCodes", deviceCodes);
        map.put("sign", sign(message));
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type","application/json;charset=UTF-8");
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String,String>> r = new HttpEntity<>(map, headers);
        String url = "https://middleground-api.ningmengdou.com/iot/openapi/getCurrent";
        ResponseEntity<JSONObject> response = restTemplate.postForEntity(url,r,JSONObject.class);
        JSONArray dataList = null;
        JSONObject jsonObject = response.getBody();
        Date now = new Date();
        Date now_10 = new Date(now.getTime() - 600000); //10分钟前的时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//可以方便地修改日期格式
        String nowTime_10 = dateFormat.format(now_10);
        if ((jsonObject.getInt("code") == 200)) {
            dataList = jsonObject.getJSONArray("data");
            Iterator<Object> o = dataList.iterator();
            while (o.hasNext()) {
                JSONObject jo = (JSONObject) o.next();
                if(!jo.get("tag").equals("Work_Status")) {
                    o.remove(); //这种方式OK的
                }
            }
            for(int i =0 ; i<dataList.size();i++) {
                if (dataList.getJSONObject(i).get("tag").equals("Work_Status")) {
                    if (dataList.getJSONObject(i).get("updateTime").toString().compareTo(nowTime_10) < 0) {
                        dataList.getJSONObject(i).set("val",0);
                    }
                }
            }
        }
        return AjaxResult.success(dataList);
    }

    private static String sign(String str){
    final SM2 sm2 = new SM2("fb7222ed1602a42bd66424a3ea00cede2a8c0200e9f36193279adabc4ae692a",null,null);
        if(StrUtil.isNotEmpty(str)) {
            sm2.usePlainEncoding();
            byte[] sign = sm2.sign(str.getBytes(), null);
            return HexUtil.encodeHexStr(sign);
        }
        return "";
    }


    @GetMapping("/CurrentInfo")
    public AjaxResult getCurrentInfo(String userCode,String deviceCodes,String privateKey) throws Exception {
        RestTemplate restTemplate = new RestTemplate();
        String time =  String.valueOf(System.currentTimeMillis());
        //String userCode = "S653F73BDE4B09F5F56376109";
        //String deviceCodes = "E653C7C72E4B09F5F862326D4,E653C86E4E4B09F5F7784FE1A,E653CAA08E4B09F5F7784FE1F,E653CAD9AE4B09F5F7784FE23,E653CB486E4B09F5F7784FE2A,E653CC8D0E4B09F5F7784FE2E,E653CCEA1E4B09F5FE0EC7EA7,E653CD4C5E4B09F5FE0EC7EA8";
        String message = "deviceCodes="+deviceCodes+"&time="+time+"&userCode="+userCode;
        Map<String, String> map = new HashMap<>();
        map.put("time", time);
        map.put("userCode", userCode);
        map.put("deviceCodes", deviceCodes);
        map.put("sign", signClone(message,privateKey));

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type","application/json;charset=UTF-8");
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String,String>> r = new HttpEntity<>(map, headers);
        String url = "https://middleground-api.ningmengdou.com/iot/openapi/getCurrent";
        ResponseEntity<JSONObject> response = restTemplate.postForEntity(url,r,JSONObject.class);
        JSONArray dataList = null;
        JSONObject jsonObject = response.getBody();
        Date now = new Date();
        Date now_10 = new Date(now.getTime() - 600000); //10分钟前的时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//可以方便地修改日期格式
        String nowTime_10 = dateFormat.format(now_10);
        if ((jsonObject.getInt("code") == 200)) {
            dataList = jsonObject.getJSONArray("data");
        }
        return AjaxResult.success(dataList);
    }

        @GetMapping("/historyDataList")
    public AjaxResult getHistoryBaseData(String deviceCodes, String userCode ,String tag, String startTime, String endTime , String privateKey) {

        RestTemplate restTemplate = new RestTemplate();
        // 请求信息
        String time = String.valueOf(System.currentTimeMillis());
        String message = "deviceCodes=" + deviceCodes + "&endTime=" + endTime + "&startTime=" + startTime + "&tag=" + tag + "&time=" + time + "&userCode=" + userCode;
        Map<String, String> map = new HashMap<>();
        map.put("time", time);
        map.put("userCode", userCode);
        map.put("deviceCodes", deviceCodes);
        map.put("sign", signClone(message,privateKey));

        map.put("tag", tag);
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type","application/json;charset=UTF-8");
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String,String>> r = new HttpEntity<>(map, headers);
        String url = "https://middleground-api.ningmengdou.com/iot/openapi/getHistory";
        ResponseEntity<JSONObject> response = restTemplate.postForEntity(url,r,JSONObject.class);
        JSONArray dataList = null;
        JSONObject jsonObject = response.getBody();
        if ((jsonObject.getInt("code") == 200)) {
            dataList = jsonObject.getJSONArray("data");
        }
        return AjaxResult.success(dataList);
    }

    private static String signClone(String str,String privateKeyHex){
        final SM2 sm2 = new SM2(privateKeyHex,null,null);
        if(StrUtil.isNotEmpty(str)) {
            sm2.usePlainEncoding();
            byte[] sign = sm2.sign(str.getBytes(), null);
            return HexUtil.encodeHexStr(sign);
        }
        return "";
    }

}
