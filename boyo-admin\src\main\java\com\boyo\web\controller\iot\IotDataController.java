package com.boyo.web.controller.iot;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.text.Convert;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.framework.annotation.Tenant;
import com.boyo.iot.domain.HistoryData;
import com.boyo.iot.domain.IotEquipment;
import com.boyo.iot.domain.IotTslAttr;
import com.boyo.iot.service.IIotEquipmentService;
import com.boyo.iot.service.IIotTslAttrService;
import com.boyo.iot.util.IoTDBUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.iotdb.rpc.IoTDBConnectionException;
import org.apache.iotdb.rpc.StatementExecutionException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Api("物联网数据查询")
@RestController
@RequestMapping("/iot/data")
@AllArgsConstructor
public class IotDataController {
    private final IoTDBUtil ioTDBUtil;
    private final IIotEquipmentService equipmentService;
    private final IIotTslAttrService tslAttrService;

    @ApiOperation("获取物联网设备管理详细信息")
    @GetMapping(value = "/getDataList")
    public AjaxResult getDataList(String device, String tag, String start, String end) {
        Date s, e;
        if (StrUtil.isEmpty(start)) {
            s = DateUtil.offset(new Date(), DateField.HOUR_OF_DAY, -1);
        } else {
            s = DateUtil.parse(start);
        }
        if (StrUtil.isEmpty(end)) {
            e = new Date();
        } else {
            e = DateUtil.parse(end);
        }
        try {
            List<HistoryData> list = ioTDBUtil.listData(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), device, tag, DateUtil.formatDateTime(s), DateUtil.formatDateTime(e));
            if (list != null && list.size() > 0) {
                IotTslAttr attr = new IotTslAttr();
                IotEquipment equipment = equipmentService.getEquipmentByCode(device);
                if (equipment != null) {
                    List<IotTslAttr> attrs = equipmentService.getEquipmentDetail(Convert.toInt(equipment.getId())).getAttrList();
                    if (attrs != null && attrs.size() > 0) {
                        for (IotTslAttr temp : attrs) {
                            if (temp.getAttrCode().equalsIgnoreCase(tag)) {
                                attr = temp;
                                break;
                            }
                        }
                    }
                }
                if (attr != null && (attr.getAttrType().equalsIgnoreCase("double") || attr.getAttrType().equalsIgnoreCase("integer"))) {
                    for (HistoryData data : list) {
                        if (ObjectUtil.isNotNull(data.getVal()) && NumberUtil.isNumber(Convert.toStr(data.getVal()))) {
                            data.setVal(NumberUtil.round(Convert.toDouble(data.getVal()) * attr.getAttrMultiple(), 2));
                        }
                    }
                }
            }
            return AjaxResult.success(list);
        } catch (IoTDBConnectionException ex) {
            ex.printStackTrace();
        } catch (StatementExecutionException ex) {
            ex.printStackTrace();
        }
        return AjaxResult.error();
    }

    @GetMapping(value = "/getDataList1")
    public AjaxResult getDataList1(String device, String tag, String start, String end,String tenant) {
        Date s, e;
        if (StrUtil.isEmpty(start)) {
            s = DateUtil.offset(new Date(), DateField.HOUR_OF_DAY, -1);
        } else {
            s = DateUtil.parse(start);
        }
        if (StrUtil.isEmpty(end)) {
            e = new Date();
        } else {
            e = DateUtil.parse(end);
        }
        try {
            List<HistoryData> list = ioTDBUtil.listData(tenant, device, tag, DateUtil.formatDateTime(s), DateUtil.formatDateTime(e));
            if (list != null && list.size() > 0) {
                IotTslAttr attr = new IotTslAttr();
                IotEquipment equipment = equipmentService.getEquipmentByCode(device);
                if (equipment != null) {
                    List<IotTslAttr> attrs = equipmentService.getEquipmentDetail(Convert.toInt(equipment.getId())).getAttrList();
                    if (attrs != null && attrs.size() > 0) {
                        for (IotTslAttr temp : attrs) {
                            if (temp.getAttrCode().equalsIgnoreCase(tag)) {
                                attr = temp;
                                break;
                            }
                        }
                    }
                }
                if (attr != null && (attr.getAttrType().equalsIgnoreCase("double") || attr.getAttrType().equalsIgnoreCase("integer"))) {
                    for (HistoryData data : list) {
                        if (ObjectUtil.isNotNull(data.getVal()) && NumberUtil.isNumber(Convert.toStr(data.getVal()))) {
                            data.setVal(NumberUtil.round(Convert.toDouble(data.getVal()) * attr.getAttrMultiple(), 2));
                        }
                    }
                }
            }
            return AjaxResult.success(list);
        } catch (IoTDBConnectionException ex) {
            ex.printStackTrace();
        } catch (StatementExecutionException ex) {
            ex.printStackTrace();
        }
        return AjaxResult.error();
    }

    @ApiOperation("获取物联网设备管理详细信息")
    @GetMapping(value = "/getDataHistory")
    public AjaxResult getDataHistory(String device, String start, String end) {
        Date s, e;
        if (StrUtil.isEmpty(start)) {
            s = DateUtil.offset(new Date(), DateField.HOUR_OF_DAY, -1);
        } else {
            s = DateUtil.parse(start);
        }
        if (StrUtil.isEmpty(end)) {
            e = new Date();
        } else {
            e = DateUtil.parse(end);
        }
        try {
            JSONObject result = new JSONObject();
            JSONArray list = ioTDBUtil.listDataHistory(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), device, "*", DateUtil.formatDateTime(s), DateUtil.formatDateTime(e));
//            if(list != null && list.size() > 0){
//                IotTslAttr attr = new IotTslAttr();
            IotEquipment equipment = equipmentService.getEquipmentByCode(device);
            if (equipment != null) {
                List<IotTslAttr> attrs = equipmentService.getEquipmentDetail(Convert.toInt(equipment.getId())).getAttrList();
                result.put("attrs", attrs);
                //                    if(attrs != null && attrs.size() > 0){
//                        for (IotTslAttr temp:attrs) {
//                            if(temp.getAttrCode().equalsIgnoreCase(tag)){
//                                attr = temp;
//                                break;
//                            }
//                        }
//                    }
//                }
                for (IotTslAttr attr : attrs) {
                    if (attr != null && (attr.getAttrType().equalsIgnoreCase("double") || attr.getAttrType().equalsIgnoreCase("integer"))) {
                        for (int i = 0; i < list.size(); i++) {
                            JSONObject temp = list.getJSONObject(i);
                            for (String tag : temp.keySet()) {
                                if (attr.getAttrCode().equalsIgnoreCase(tag)) {
                                    try {
                                        temp.put(tag, NumberUtil.round(Convert.toDouble(temp.getString(tag)) * attr.getAttrMultiple(), 2));
                                        list.set(i, temp);
                                        break;
                                    }catch (Exception e1){

                                    }

                                }
                            }
                        }
//                            for (JSONObject data : list) {
//                                if (ObjectUtil.isNotNull(data.getVal()) && NumberUtil.isNumber(Convert.toStr(data.getVal()))) {
//                                    data.setVal(NumberUtil.round(Convert.toDouble(data.getVal()) * attr.getAttrMultiple(), 2));
//                                }
//                            }
                    }
                }
            }
            result.put("list", list);
            return AjaxResult.success(result);
        } catch (IoTDBConnectionException ex) {
            ex.printStackTrace();
        } catch (StatementExecutionException ex) {
            ex.printStackTrace();
        }
        return AjaxResult.error();
    }

    @ApiOperation("获取物联网设备管理详细信息")
    @GetMapping(value = "/getDataHistoryV2")
    public AjaxResult getDataHistoryV2(String device, String start, String end) {
        Date s, e;
        if (StrUtil.isEmpty(start)) {
            s = DateUtil.offset(new Date(), DateField.HOUR_OF_DAY, -1);
        } else {
            s = DateUtil.parse(start);
        }
        if (StrUtil.isEmpty(end)) {
            e = new Date();
        } else {
            e = DateUtil.parse(end);
        }
        try {
            JSONObject result = new JSONObject();
//            if(list != null && list.size() > 0){
//                IotTslAttr attr = new IotTslAttr();
            IotEquipment equipment = equipmentService.getEquipmentByCode(device);
            if (equipment != null) {
                List<IotTslAttr> attrs = equipmentService.getEquipmentDetail(Convert.toInt(equipment.getId())).getAttrList();
                result.put("attrs", attrs);
                List<String> codes = new ArrayList<>();
                for (IotTslAttr attr : attrs) {
                    codes.add(attr.getAttrCode());
                }
                JSONArray list = ioTDBUtil.listDataHistoryV2(SecurityUtils.getLoginUser().getEnterpriseUser().getEnterpriseOpenid(), device, codes, DateUtil.formatDateTime(s), DateUtil.formatDateTime(e));

                for (IotTslAttr attr : attrs) {
                    if (attr != null && (attr.getAttrType().equalsIgnoreCase("double") || attr.getAttrType().equalsIgnoreCase("integer"))) {
                        for (int i = 0; i < list.size(); i++) {
                            JSONObject temp = list.getJSONObject(i);
                            for (String tag : temp.keySet()) {
                                if (attr.getAttrCode().equalsIgnoreCase(tag)) {
                                    try {
                                        temp.put(tag, NumberUtil.round(Convert.toDouble(temp.getString(tag)) * attr.getAttrMultiple(), 2));
                                        list.set(i, temp);
                                        break;
                                    }catch (Exception e1){

                                    }

                                }
                            }
                        }
//                            for (JSONObject data : list) {
//                                if (ObjectUtil.isNotNull(data.getVal()) && NumberUtil.isNumber(Convert.toStr(data.getVal()))) {
//                                    data.setVal(NumberUtil.round(Convert.toDouble(data.getVal()) * attr.getAttrMultiple(), 2));
//                                }
//                            }
                    }
                }
                result.put("list", list);
            }
            return AjaxResult.success(result);
        } catch (IoTDBConnectionException ex) {
            ex.printStackTrace();
        } catch (StatementExecutionException ex) {
            ex.printStackTrace();
        }
        return AjaxResult.error();
    }
}
