package com.boyo.web.controller.wms;

import java.util.List;
import java.util.Arrays;

import com.boyo.wms.vo.WmsPlanVO;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.boyo.wms.entity.WmsPlan;
import com.boyo.wms.service.IWmsPlanService;

/**
 * 出入库计划管理Controller
 *
 * <AUTHOR>
 */
@Api("出入库计划管理")
@RestController
@RequestMapping("/wms/wmsplan")
@AllArgsConstructor
public class WmsPlanController extends BaseController {

    private final IWmsPlanService wmsPlanService;

    /**
     * 查询出入库计划管理列表
     */
    @ApiOperation("查询出入库计划管理列表")
    @GetMapping("/list")
    public TableDataInfo list(WmsPlan wmsPlan) {
        startPage();
        List<WmsPlanVO> list = wmsPlanService.selectWmsPlanList(wmsPlan);
        return getDataTable(list);
    }

    /**
     * 获取出入库计划管理详细信息
     */
    @ApiOperation("获取出入库计划管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(wmsPlanService.getById(id));
    }

    /**
     * 新增出入库计划管理
     */
    @ApiOperation("新增出入库计划管理")
    @PostMapping
    public AjaxResult add(@RequestBody WmsPlan wmsPlan) {
        return toBooleanAjax(wmsPlanService.save(wmsPlan));
    }

    /**
     * 修改出入库计划管理
     */
    @ApiOperation("修改出入库计划管理")
    @PutMapping
    public AjaxResult edit(@RequestBody WmsPlan wmsPlan) {
        return toBooleanAjax(wmsPlanService.updateById(wmsPlan));
    }

    /**
     * 删除出入库计划管理
     */
    @ApiOperation("删除出入库计划管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(wmsPlanService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 下发出入库计划
     *
     * @param id
     * @return
     */

    @ApiOperation("下发出入库计划")
    @PostMapping("/issuePlan")
    public AjaxResult issuePlan(String id) {
        return toBooleanAjax(wmsPlanService.issuePlan(id));
    }

    @ApiOperation("撤回出入库计划")
    @PostMapping("/withdrawPlan")
    public AjaxResult withdrawPlan(String id) {
        return toBooleanAjax(wmsPlanService.withdrawPlan(id));
    }

    @ApiOperation("结束出入库计划")
    @PostMapping("/endPlan")
    public AjaxResult endPlan(String id) {
        return toBooleanAjax(wmsPlanService.endPlan(id));
    }
}
