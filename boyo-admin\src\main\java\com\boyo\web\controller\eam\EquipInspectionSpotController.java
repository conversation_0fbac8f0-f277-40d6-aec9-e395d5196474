package com.boyo.web.controller.eam;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.eam.domain.EquipInspectionSpot;
import com.boyo.eam.domain.EquipInspectionSpotItem;
import com.boyo.eam.domain.EquipLedger;
import com.boyo.eam.service.IEquipInspectionRecordService;
import com.boyo.eam.service.IEquipInspectionSpotItemService;
import com.boyo.eam.service.IEquipInspectionSpotService;
import com.boyo.eam.service.IEquipLedgerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 点检表(EquipInspectionSpot)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-15 16:16:30
 */
@Api("点检表")
@RestController
@RequestMapping("/equip/equipInspectionSpot")
@AllArgsConstructor
public class EquipInspectionSpotController extends BaseController{
    /**
     * 服务对象
     */
    private final IEquipInspectionSpotService equipInspectionSpotService;
    private final IEquipInspectionSpotItemService equipInspectionSpotItemService;
    private final IEquipLedgerService equipLedgerService;
    private final IEquipInspectionRecordService equipInspectionRecordService;
    /**
     * 查询点检表列表
     *
     */
    @ApiOperation("查询点检表列表")
    @GetMapping("/list")
    public TableDataInfo list(EquipInspectionSpot equipInspectionSpot) {
        startPage();
        List<EquipInspectionSpot> list = equipInspectionSpotService.selectEquipInspectionSpotList(equipInspectionSpot);
        return getDataTable(list);
    }
    
    /**
     * 获取点检表详情
     */
    @ApiOperation("获取点检表详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        EquipInspectionSpot spot = equipInspectionSpotService.getById(id);
        String openid = spot.getOpenid();
        List<EquipInspectionSpotItem> itemList = equipInspectionSpotItemService.list(
                Wrappers.<EquipInspectionSpotItem>lambdaQuery()
                        .eq(EquipInspectionSpotItem::getEquipInspectionSpotOpenid, openid)
        );
        spot.setItemList(itemList);

        EquipLedger equipLedger = equipLedgerService.getOne(
                Wrappers.<EquipLedger>lambdaQuery()
                        .eq(EquipLedger::getOpenid, spot.getEquipLedgerOpenid())
        );

        spot.setEquipName(equipLedger.getName());
        spot.setEquipCode(equipLedger.getCode());
        return AjaxResult.success(spot);
    }

    /**
     * 新增点检表
     */
    @ApiOperation("新增点检表")
    @Transactional
    @PostMapping
    public AjaxResult add(@RequestBody EquipInspectionSpot equipInspectionSpot) {
        String openid = super.generateOpenid();
        equipInspectionSpot.setOpenid(openid);
        equipInspectionSpot.setCode(getNewCode());
        equipInspectionSpot.setState("0");//待处理
        List<EquipInspectionSpotItem> itemList = equipInspectionSpot.getItemList();
        for (EquipInspectionSpotItem item: itemList){
            item.setOpenid(super.generateOpenid());
            item.setEquipInspectionSpotOpenid(openid);
            item.setCreateBy(SecurityUtils.getUsername());
            item.setCreateTime(new Date());
        }
        equipInspectionSpotItemService.saveBatch(itemList);
        return toBooleanAjax(equipInspectionSpotService.save(equipInspectionSpot));
    }

    /**
     * 修改点检表
     */
    @ApiOperation("修改点检表")
    @Transactional
    @PutMapping
    public AjaxResult edit(@RequestBody EquipInspectionSpot equipInspectionSpot) {
        String spotOpenid = equipInspectionSpot.getOpenid();

        List<EquipInspectionSpotItem> itemList = equipInspectionSpot.getItemList();

        //删除旧的子数据
        equipInspectionSpotItemService.remove(
            Wrappers.<EquipInspectionSpotItem>lambdaQuery()
                .eq(EquipInspectionSpotItem::getEquipInspectionSpotOpenid,spotOpenid)
        );

        //构建新的子数据
        for (EquipInspectionSpotItem item :itemList){
            if (item.getOpenid()==null||"".equals(item.getOpenid())){
                item.setOpenid(super.generateOpenid());
            }
            item.setEquipInspectionSpotOpenid(spotOpenid);
            item.setCreateBy(SecurityUtils.getUsername());
            item.setCreateTime(new Date());
        }

        //保存新的子数据
        equipInspectionSpotItemService.saveBatch(itemList);

        return toBooleanAjax(equipInspectionSpotService.updateById(equipInspectionSpot));
    }

    /**
     * 删除点检表
     */
    @ApiOperation("删除点检表")
    @Transactional
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        List<EquipInspectionSpot> spotList = equipInspectionSpotService.list(
                Wrappers.<EquipInspectionSpot>lambdaQuery()
                        .in(EquipInspectionSpot::getId, ids)
        );
        if (spotList!=null&&spotList.size()>0){
            List<String> openids = new ArrayList<>();
            for (EquipInspectionSpot eis:spotList){
                openids.add(eis.getOpenid());
            }
            equipInspectionSpotItemService.remove(
                    Wrappers.<EquipInspectionSpotItem>lambdaQuery()
                            .in(EquipInspectionSpotItem::getEquipInspectionSpotOpenid,openids)
            );
        }
        return toBooleanAjax(equipInspectionSpotService.removeByIds(Arrays.asList(ids)));
    }



    /**
     * 获取最新点检编号
     */
    private String getNewCode(){
        String prefix = DateUtil.format(new Date(),"yyyyMMdd");
        List<EquipInspectionSpot> spotList = equipInspectionSpotService.list(
                Wrappers.<EquipInspectionSpot>lambdaQuery()
                        .like(EquipInspectionSpot::getCode,prefix)
        );
        int max = 0;
        if (spotList!=null&&spotList.size()>0){
            for (EquipInspectionSpot emt:spotList){
                String code = emt.getCode();
                int num = Integer.parseInt(code.replace(prefix,""));
                if (num>max){
                    max = num;
                }
            }
        }
        return prefix+String.format("%02d", max+1);
    }

    // 移动端接口
    @ApiOperation("查询维保任务管理列表")
    @GetMapping("/pad/list")
    public TableDataInfo padList(EquipInspectionSpot equipInspectionSpot) {
        startPage();
        List<EquipInspectionSpot> list = equipInspectionSpotService.selectEquipInspectionSpotList(equipInspectionSpot);
        return getDataTable(list);
    }


    @ApiOperation("点击开始或者结束")
    @Transactional
    @PutMapping("/pad/beginEnd")
    public AjaxResult beginEnd(@RequestBody Map<String,String> values) {
        String openid = values.get("openid");
        String state = values.get("state");

        EquipInspectionSpot equipInspectionSpot = equipInspectionSpotService.getOne(
                Wrappers.<EquipInspectionSpot>lambdaQuery()
                        .eq(EquipInspectionSpot::getOpenid, openid)
        );
        if ("0".equals(state)){
            equipInspectionSpot.setState("1");//进行中
            equipInspectionSpot.setSysUserId(Integer.parseInt(String.valueOf(SecurityUtils.getLoginUser().getUser().getUserId())));
            equipInspectionRecordService.insertBySpotOpenid(openid);
        }else if ("1".equals(state)){
            equipInspectionSpot.setState("2");//已完成
        }
        equipInspectionSpot.setSysUserId(Integer.parseInt(String.valueOf(SecurityUtils.getLoginUser().getUser().getUserId())));
        return AjaxResult.success(equipInspectionSpotService.updateById(equipInspectionSpot));
    }
}
