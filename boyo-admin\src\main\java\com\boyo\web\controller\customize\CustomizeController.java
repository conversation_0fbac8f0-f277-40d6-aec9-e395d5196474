package com.boyo.web.controller.customize;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.utils.http.HttpUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 获取定制信息
 */
@RestController
@RequestMapping("/customize")
public class CustomizeController extends BaseController {

    /**
     * 获取定制系统token信息
     * @return
     */
    public String getCostomizeToken() {

        String cToken = "";

        // token的URL地址
//        String tokenUrlTest = "http://tsingtao.vkits.cn/token/oauth/token?client_id=web&client_secret=app&grant_type=password&password=123&username=kongzzx";
        String tokenUrl = "https://apistore.ciiplat.com/token/oauth/token?client_id=web&client_secret=app&grant_type=password&password=tsingtao@1903&username=kongzzs";

        cToken = HttpUtils.sendPost(tokenUrl, "");

        return cToken;
    }

    /**
     * 带着token信息获取定制数据，针对特定演示场景，现在是全量接收
     * @return
     */
    @GetMapping("/getCostomizeData")
    public JSONArray getCostomizeData() {

        JSONArray cData = new JSONArray();

        String cToken = JSONObject.parseObject(getCostomizeToken()).getJSONObject("data").getString("token");

//        String dataUrlTest = "http://tsingtao.vkits.cn/order/order/sceneOrders";
        String dataUrl = "https://apistore.ciiplat.com/order/order/sceneOrders";

        String cParam = "scene=10001";

        JSONObject dataJson = JSONObject.parseObject(HttpUtils.sendGetWithToken(dataUrl, cParam, cToken, "utf-8"));

        cData = dataJson.getJSONArray("data");

        return cData;
    }
}
