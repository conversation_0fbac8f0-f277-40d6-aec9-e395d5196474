package com.boyo.web.controller.eam;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.utils.SecurityUtils;
import com.boyo.eam.domain.EquipInspectionRecord;
import com.boyo.eam.service.IEquipInspectionRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * (EquipInspectionRecord)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-28 19:30:53
 */
@Api("")
@RestController
@RequestMapping("/equip/equipInspectionRecord")
@AllArgsConstructor
public class EquipInspectionRecordController extends BaseController{
    /**
     * 服务对象
     */
    private final IEquipInspectionRecordService equipInspectionRecordService;

    /**
     * 查询列表
     *
     */
    @ApiOperation("查询列表")
    @GetMapping("/list")
    public TableDataInfo list(EquipInspectionRecord equipInspectionRecord) {
        startPage();
        List<EquipInspectionRecord> list = equipInspectionRecordService.selectEquipInspectionRecordList(equipInspectionRecord);
        return getDataTable(list);
    }
    
    /**
     * 获取详情
     */
    @ApiOperation("获取详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(equipInspectionRecordService.getById(id));
    }

    /**
     * 新增
     */
    @ApiOperation("新增")
    @PostMapping
    public AjaxResult add(@RequestBody EquipInspectionRecord equipInspectionRecord) {
        return toBooleanAjax(equipInspectionRecordService.save(equipInspectionRecord));
    }

    /**
     * 修改
     */
    @ApiOperation("修改")
    @PutMapping
    public AjaxResult edit(@RequestBody EquipInspectionRecord equipInspectionRecord) {
        return toBooleanAjax(equipInspectionRecordService.updateById(equipInspectionRecord));
    }

    /**
     * 删除
     */
    @ApiOperation("删除")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(equipInspectionRecordService.removeByIds(Arrays.asList(ids)));
    }


    // 移动端

    /**
     * 修改点巡检记录表
     */
    @ApiOperation("修改点巡检记录表")
    @PutMapping("/pad")
    public AjaxResult padEdit(@RequestBody EquipInspectionRecord equipInspectionRecord) {
        String openid = equipInspectionRecord.getOpenid();
        EquipInspectionRecord record = equipInspectionRecordService.getOne(
                Wrappers.<EquipInspectionRecord>lambdaQuery()
                        .eq(EquipInspectionRecord::getOpenid, openid)
        );
        record.setInspectionDate(new Date());
        record.setPass(equipInspectionRecord.getPass());
        record.setRemark(equipInspectionRecord.getRemark());
        record.setMediaId(equipInspectionRecord.getMediaId());
        record.setUpdateTime(new Date());
        record.setUpdateBy(SecurityUtils.getUsername());
        return toBooleanAjax(equipInspectionRecordService.updateById(record));
    }
}
