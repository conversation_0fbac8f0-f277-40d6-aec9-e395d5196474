package com.boyo.web.isv.util;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

public class IsvProduceAPI {
    /**
     * 默认编码：UTF-8
     */
    private static final String CHARSET = "UTF-8";

    /**
     * 校验通知消息的合法性
     * 将query参数根据字典顺序排序后进行签名，然后校验获取的签名与云市场传递的签名是否一致
     *
     * @param accessKey 接入码
     * @return 验证结果
     */
    public static boolean verificationRequestQueryParams(Map<String, String[]> paramsMap, String accessKey) {

        String timeStamp = null;
        String authToken = null;
        String[] timeStampArray = paramsMap.get("timeStamp");

        if (null != timeStampArray && timeStampArray.length > 0) {
            timeStamp = timeStampArray[0];
        }

        String[] authTokenArray = paramsMap.get("authToken");

        if (null != authTokenArray && authTokenArray.length > 0) {
            authToken = authTokenArray[0];
            //System.out.println("authToken:-----"+authToken);
        }

        // 对剩下的参数进行排序，拼接成加密内容
        Map<String, String[]> sortedMap = new TreeMap<>(paramsMap);

        // 剔除authToken，不参与签名计算
        sortedMap.remove("authToken");
        StringBuilder strBuffer = new StringBuilder();
        Set<String> keySet = sortedMap.keySet();

        for (String key : keySet) {
            String value = sortedMap.get(key)[0];
            strBuffer.append("&").append(key).append("=").append(value);
        }

        // 修正消息体,去除第一个参数前面的&
        String reqParams = strBuffer.toString().substring(1);
        String key = accessKey + timeStamp;
        String signature = null;

        try {
            // 将重新排序后的请求参数计算签名
            signature = generateResponseBodySignature(key, reqParams);
            //System.out.println("signature:-----"+signature);
        } catch (IllegalStateException e) {
            // error message log
            return false;
        }

        // 判断计算后的签名与云市场请求中传递的token是否一致
        return StringUtils.isNotEmpty(authToken) && authToken.equals(signature);
    }

    /**
     * 获取请求体签名
     *
     * @param key  accessKey
     * @param body 待签名的body体字符串
     * @return 生成的签名
     */
    public static String generateResponseBodySignature(String key, String body) {
        return base_64(hmacSHA256(key, body));
    }

    public static byte[] hmacSHA256(String macKey, String macData) {
        try {
            SecretKeySpec secret = new SecretKeySpec(macKey.getBytes(CHARSET), "HmacSHA256");
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(secret);
            return mac.doFinal(macData.getBytes(CHARSET));
        } catch (UnsupportedEncodingException | InvalidKeyException | NoSuchAlgorithmException e) {
            // error message log
            return new byte[0];
        }
    }

    public static String base_64(byte[] bytes) {

        try {
            return new String(Base64.encodeBase64(bytes), CHARSET);
        } catch (UnsupportedEncodingException e) {
            return null;
        }
    }

    public static String decryptSaasExtendParams(String saasExtendParams) {
        String decryptStr = null;

        try {
            decryptStr = new String(Base64.decodeBase64(saasExtendParams), CHARSET);
            return URLDecoder.decode(decryptStr, CHARSET);
        } catch (UnsupportedEncodingException e) {
            // error message log
            return null;
        }
    }
}
