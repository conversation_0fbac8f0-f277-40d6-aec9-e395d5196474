package com.boyo.web.controller.admin;

import java.util.List;
import java.util.Arrays;

import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.boyo.system.domain.DatabaseLog;
import com.boyo.system.service.IDatabaseLogService;

/**
 * 数据库变更管理Controller
 *
 * <AUTHOR>
 */
@Api("数据库变更管理")
@RestController
@RequestMapping("/system/database")
@AllArgsConstructor
public class DatabaseLogController extends BaseController {
    private final IDatabaseLogService databaseLogService;

    /**
     * 查询数据库变更管理列表
     */
    @ApiOperation("查询数据库变更管理列表")
    @GetMapping("/list")
    public TableDataInfo list(DatabaseLog databaseLog) {
        startPage();
        List<DatabaseLog> list = databaseLogService.selectDatabaseLogList(databaseLog);
        return getDataTable(list);
    }

    /**
     * 获取数据库变更管理详细信息
     */
    @ApiOperation("获取数据库变更管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(databaseLogService.getById(id));
    }

    /**
     * 新增数据库变更管理
     */
    @ApiOperation("新增数据库变更管理")
    @PostMapping
    public AjaxResult add(@RequestBody DatabaseLog databaseLog) {
        return toBooleanAjax(databaseLogService.save(databaseLog));
    }

    /**
     * 修改数据库变更管理
     */
    @ApiOperation("修改数据库变更管理")
    @PutMapping
    public AjaxResult edit(@RequestBody DatabaseLog databaseLog) {
        return toBooleanAjax(databaseLogService.updateById(databaseLog));
    }

    /**
     * 删除数据库变更管理
     */
    @ApiOperation("删除数据库变更管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(databaseLogService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 执行sql更新
     * @param id
     * @return
     */
    @ApiOperation("执行sql更新")
    @PostMapping("/execute")
    public AjaxResult executeSql(Integer id){
        databaseLogService.executeSql(id);
        return AjaxResult.success();
    }
}
