package com.boyo.web.controller.master;

import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.master.entity.EnterpriseCustom;
import com.boyo.master.service.IEnterpriseCustomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 企业自定义信息(EnterpriseCustom)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-07 20:19:56
 */
@Api("企业自定义信息")
@RestController
@RequestMapping("/master/enterpriseCustom")
@AllArgsConstructor
public class EnterpriseCustomController extends BaseController{
    /**
     * 服务对象
     */
    private final IEnterpriseCustomService enterpriseCustomService;

    /**
     * 查询企业自定义信息列表
     *
     */
    @ApiOperation("查询企业自定义信息列表")
    @GetMapping("/list")
    public AjaxResult list() {
        List<EnterpriseCustom> list = enterpriseCustomService.list();
        if(list != null && list.size() > 0){
            return AjaxResult.success(list.get(0));
        }
        return AjaxResult.success();
    }
    
    /**
     * 获取企业自定义信息详情
     */
    @ApiOperation("获取企业自定义信息详情")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(enterpriseCustomService.getById(id));
    }

    /**
     * 新增企业自定义信息
     */
    @ApiOperation("新增企业自定义信息")
    @PostMapping
    public AjaxResult add(@RequestBody EnterpriseCustom enterpriseCustom) {
        return toBooleanAjax(enterpriseCustomService.save(enterpriseCustom));
    }

    /**
     * 修改企业自定义信息
     */
    @ApiOperation("修改企业自定义信息")
    @PutMapping
    public AjaxResult edit(@RequestBody EnterpriseCustom enterpriseCustom) {
        return toBooleanAjax(enterpriseCustomService.updateById(enterpriseCustom));
    }

    /**
     * 删除企业自定义信息
     */
    @ApiOperation("删除企业自定义信息")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(enterpriseCustomService.removeByIds(Arrays.asList(ids)));
    }

}