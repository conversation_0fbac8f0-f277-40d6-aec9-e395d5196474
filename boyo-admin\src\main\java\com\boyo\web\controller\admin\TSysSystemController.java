package com.boyo.web.controller.admin;

import java.util.List;
import java.util.Arrays;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.boyo.common.core.controller.BaseController;
import com.boyo.common.core.domain.AjaxResult;
import com.boyo.common.core.domain.entity.EnterpriseUser;
import com.boyo.common.core.domain.model.LoginUser;
import com.boyo.common.core.page.TableDataInfo;
import com.boyo.common.utils.ServletUtils;
import com.boyo.framework.web.service.TokenService;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.boyo.system.domain.TSysSystem;
import com.boyo.system.service.ITSysSystemService;

/**
 * 系统管理Controller
 *
 * <AUTHOR>
 */
@Api("系统管理")
@RestController
@RequestMapping("/system/systematic")
@AllArgsConstructor
public class TSysSystemController extends BaseController {

    private final ITSysSystemService tSysSystemService;

    private TokenService tokenService;

    /**
     * 查询系统管理列表
     */
    @ApiOperation("查询系统管理列表")
    @GetMapping("/list")
    public TableDataInfo list(TSysSystem tSysSystem) {
        startPage();
        List<TSysSystem> list = tSysSystemService.selectTSysSystemList(tSysSystem);
        return getDataTable(list);
    }

    /**
     * 获取系统管理详细信息
     */
    @ApiOperation("获取系统管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(tSysSystemService.getById(id));
    }

    /**
     * 新增系统管理
     */
    @ApiOperation("新增系统管理")
    @PostMapping
    public AjaxResult add(@RequestBody TSysSystem tSysSystem) {
        tSysSystem.setSystemOpenid(IdUtil.fastSimpleUUID());
        return toBooleanAjax(tSysSystemService.save(tSysSystem));
    }

    /**
     * 修改系统管理
     */
    @ApiOperation("修改系统管理")
    @PutMapping
    public AjaxResult edit(@RequestBody TSysSystem tSysSystem) {
        return toBooleanAjax(tSysSystemService.updateById(tSysSystem));
    }

    /**
     * 删除系统管理
     */
    @ApiOperation("删除系统管理")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toBooleanAjax(tSysSystemService.removeByIds(Arrays.asList(ids)));
    }

    @GetMapping("/findEnterpriseSystem")
    public AjaxResult findEnterpriseSystem(){
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        EnterpriseUser enterpriseUser = loginUser.getEnterpriseUser();
        if(ObjectUtil.isNull(enterpriseUser)){
            return null;
        }
        if(enterpriseUser.getUserAdmin().equals(1L)){
            return AjaxResult.success(tSysSystemService.findEnterpriseSystem(enterpriseUser.getEnterpriseOpenid()));
        }else{
            return AjaxResult.success(tSysSystemService.findUserSystem(enterpriseUser.getEnterpriseOpenid(),enterpriseUser.getUserOpenid()));
        }
    }

}
